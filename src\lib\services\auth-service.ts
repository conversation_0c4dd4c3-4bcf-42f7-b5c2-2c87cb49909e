import { httpClient } from '../http-client';
import { User } from '@/types/user';
import {
  AcceptInvitationData,
  AcceptInvitationResponse,
  LoginCredentials,
  LoginResponse,
  RefreshTokenResponse,
  AuthenticatedUser,
  ChangePasswordData,
  ForgotPasswordData,
  ResetPasswordData,
  ResetPasswordConfirmData,
  InvitationUserInfoResponse,
} from '@/types/auth';

/**
 * Authentication Service - Handles all authentication-related operations
 */
export class AuthService {
  /**
   * Authenticate user with email and password
   */
  async login(credentials: LoginCredentials): Promise<LoginResponse> {
    const response = await httpClient.post<LoginResponse>('/auth/login', {
      email: credentials.email,
      password: credentials.password,
    });

    return response;
  }

  /**
   * Refresh access token using refresh token
   */
  async refreshToken(refreshToken: string): Promise<RefreshTokenResponse> {
    const response = await httpClient.post<RefreshTokenResponse>(
      '/auth/refresh',
      {
        refreshToken,
      }
    );

    return response;
  }

  /**
   * Complete authentication flow: login + fetch user profile
   */
  async authenticate(
    credentials: LoginCredentials
  ): Promise<AuthenticatedUser> {
    try {
      // Step 1: Login to get tokens
      const loginResponse = await this.login(credentials);

      // Step 2: Fetch user profile using the access token
      const userProfile = await this.getUserProfile(loginResponse.accessToken);

      // Step 3: Return complete authenticated user data
      const authenticatedUser = {
        ...userProfile,
        accessToken: loginResponse.accessToken,
        refreshToken: loginResponse.refreshToken,
        accessTokenExpires: loginResponse.accessTokenExpireTime * 1000,
        refreshTokenExpires: loginResponse.refreshTokenExpireTime * 1000,
      };

      return authenticatedUser;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get user profile using access token
   */
  async getUserProfile(accessToken: string): Promise<User> {
    // For this specific case, we need to make a direct request with the access token
    // since we don't have it in the session yet during authentication
    const baseUrl =
      process.env.NEXT_PUBLIC_NESTJS_API_URL || 'http://localhost:3001';
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000);

    try {
      const response = await fetch(`${baseUrl}/users/me`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${accessToken}`,
        },
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(
          `Failed to fetch user profile: ${response.status} ${response.statusText}`
        );
      }

      const userProfile = await response.json();
      return userProfile;
    } catch (error) {
      clearTimeout(timeoutId);
      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error('User profile fetch timeout - please try again');
      }
      throw error;
    }
  }

  /**
   * Change user password
   */
  async changePassword(data: ChangePasswordData): Promise<void> {
    return httpClient.post<void>('/auth/change-password', data);
  }

  /**
   * Request password reset
   */
  async forgotPassword(data: ForgotPasswordData): Promise<void> {
    return httpClient.post<void>('/auth/reset-password', data);
  }

  /**
   * Reset password with token
   */
  async resetPassword(data: ResetPasswordData): Promise<void> {
    return httpClient.post<void>('/auth/reset-password', data);
  }

  /**
   * Confirm password reset with token
   */
  async resetPasswordConfirm(data: ResetPasswordConfirmData): Promise<void> {
    return httpClient.post<void>('/auth/reset-password/confirm', data);
  }

  /**
   * Logout user (invalidate tokens on server)
   */
  async logout(): Promise<void> {
    return httpClient.post<void>('/auth/logout');
  }

  /**
   * Verify email address
   */
  async verifyEmail(token: string): Promise<void> {
    return httpClient.post<void>('/auth/verify-email', { token });
  }

  /**
   * Resend email verification
   */
  async resendVerificationEmail(): Promise<void> {
    return httpClient.post<void>('/auth/resend-verification');
  }

  /**
   * Get user information from invitation token
   */
  async getInvitationUserInfo(
    token: string
  ): Promise<InvitationUserInfoResponse> {
    const baseUrl =
      process.env.NEXT_PUBLIC_NESTJS_API_URL || 'http://localhost:3001';
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000);

    try {
      const response = await fetch(
        `${baseUrl}/auth/invitation?token=${token}`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
          signal: controller.signal,
        }
      );

      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.message ||
            `Failed to fetch user information: ${response.status} ${response.statusText}`
        );
      }

      const data = await response.json();
      return data;
    } catch (error) {
      clearTimeout(timeoutId);
      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error('Request timeout - please try again');
      }
      throw error;
    }
  }

  /**
   * Accept invitation and set password
   */
  async acceptInvitation(
    data: AcceptInvitationData
  ): Promise<AcceptInvitationResponse> {
    const baseUrl =
      process.env.NEXT_PUBLIC_NESTJS_API_URL || 'http://localhost:3001';
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000);

    try {
      const response = await fetch(`${baseUrl}/auth/accept-invitation`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          token: data.token,
          password: data.password,
        }),
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.message ||
            `Failed to accept invitation: ${response.status} ${response.statusText}`
        );
      }

      const responseData = await response.json();
      return responseData;
    } catch (error) {
      clearTimeout(timeoutId);
      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error('Request timeout - please try again');
      }
      throw error;
    }
  }
}

// Export singleton instance
export const authService = new AuthService();
