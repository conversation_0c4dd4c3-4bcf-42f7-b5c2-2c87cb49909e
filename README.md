# Scoutr - Next.js 15 Application

A modern Next.js 15 application with TypeScript, authentication, beautiful UI components, and route protection.

## Features

- ⚡ **Next.js 15** - Latest version with App Router
- 🔐 **Authentication** - NextAuth.js with credentials provider
- 🎨 **Beautiful UI** - shadcn/ui components with Tailwind CSS
- 📝 **TypeScript** - Full type safety
- 🛡️ **Route Protection** - Public and private routes with middleware
- 🎯 **Code Quality** - ESLint and Prettier configuration
- 📱 **Responsive Design** - Mobile-first approach
- 🔔 **Notifications** - Toast notifications with Sonner

## Tech Stack

- **Framework**: Next.js 15
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **UI Components**: shadcn/ui
- **Authentication**: NextAuth.js
- **Code Quality**: ESLint + Prettier
- **Notifications**: Sonner

## Getting Started

### Prerequisites

- Node.js 18+
- npm or yarn

### Installation

1. Clone the repository:

```bash
git clone <repository-url>
cd scoutr-frontend
```

2. Install dependencies:

```bash
npm install
```

3. Create environment variables:

```bash
cp .env.example .env.local
```

4. Run the development server:

```bash
npm run dev
```

5. Open [http://localhost:3000](http://localhost:3000) in your browser.

## Project Structure

```
src/
├── app/                    # App Router pages
│   ├── api/               # API routes
│   │   └── auth/          # NextAuth API
│   ├── auth/              # Authentication pages
│   │   └── signin/        # Sign-in page
│   ├── dashboard/         # Protected dashboard
│   └── layout.tsx         # Root layout
├── components/            # React components
│   └── ui/               # shadcn/ui components
├── contexts/             # React contexts
│   └── auth-context.tsx  # Authentication context
├── lib/                  # Utility libraries
│   ├── auth.ts           # NextAuth configuration
│   └── utils.ts          # Utility functions
└── middleware.ts         # Route protection middleware
```

## Authentication

The application includes a complete authentication system with:

- **Sign-in page** at `/auth/signin`
- **Protected routes** with automatic redirects
- **Role-based access** (admin/user)
- **Session management** with NextAuth.js

### Demo Credentials

- **Admin User**: `<EMAIL>` / `admin123`
- **Regular User**: `<EMAIL>` / `user123`

## Route Protection

- **Public Routes**: `/` (landing page)
- **Auth Routes**: `/auth/*` (sign-in, sign-up)
- **Protected Routes**: All other routes require authentication

## Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm run format` - Format code with Prettier

## Code Quality

The project includes:

- **ESLint** configuration with Next.js and TypeScript rules
- **Prettier** for consistent code formatting
- **TypeScript** for type safety

## Customization

### Adding New Components

1. Install shadcn/ui components:

```bash
npx shadcn@latest add <component-name>
```

2. Use components in your pages:

```tsx
import { Button } from '@/components/ui/button';
```

### Adding New Routes

1. Create new pages in `src/app/`
2. Protected routes are automatically handled by middleware
3. Public routes should be added to the middleware configuration

### Styling

- Use Tailwind CSS classes for styling
- Custom styles can be added to `src/app/globals.css`
- Component-specific styles can be added using CSS modules or styled-components

## Deployment

The application can be deployed to:

- **Vercel** (recommended for Next.js)
- **Netlify**
- **Railway**
- Any platform supporting Node.js

### Environment Variables

Create a `.env.local` file with:

```env
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-secret-key-here
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests and linting
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Support

For support, please open an issue in the repository or contact the development team.
