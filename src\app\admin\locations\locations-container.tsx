'use client';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { SearchIcon } from '@/lib/icons';
import { ChevronLeft } from 'lucide-react';
import { ChevronRight } from 'lucide-react';
import { Plus } from 'lucide-react';
import { useMemo } from 'react';
import { useRouter } from 'next/navigation';
import LocationsTable from './locations-table';
import Pagination from '@/components/Pagination';
import { useLocations } from '@/hooks/useLocations';

export default function LocationsContainer() {
  const router = useRouter();
  const {
    locations,
    tagOptions,
    totalPages,
    loading,
    filters,
    setSearch,
    setTags,
    setPlace,
    setPage,
    refreshData,
  } = useLocations();

  const locationsMain = useMemo(
    () => (
      <LocationsTable
        data={locations}
        loading={loading}
        onActionSuccess={() => {
          setPage(1);
          refreshData();
        }}
      />
    ),
    [locations, loading, setPage, refreshData]
  );

  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold">Locations</h1>
      <div className="text-base">Add, update, and organize all locations.</div>
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:flex-1">
          <div className="relative w-full md:w-[180px] xl:w-[256px]">
            <SearchIcon className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search by name..."
              value={filters.search ?? ''}
              onChange={e => setSearch(e.target.value)}
              className="pl-9"
              debounceMs={300}
            />
          </div>
          <div className="flex flex-wrap items-center gap-4">
            <Select value={filters.tags} onValueChange={v => setTags(v)}>
              <SelectTrigger aria-label="Tags">
                <SelectValue placeholder="All Tags" />
              </SelectTrigger>
              <SelectContent>
                {[{ value: 'all', label: 'All Tags' }, ...tagOptions].map(
                  tag => (
                    <SelectItem key={tag.value} value={tag.value}>
                      {tag.label}
                    </SelectItem>
                  )
                )}
              </SelectContent>
            </Select>

            <Select value={filters.place} onValueChange={v => setPlace(v)}>
              <SelectTrigger aria-label="Places">
                <SelectValue placeholder="All Places" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Places</SelectItem>
                <SelectItem value="place-id-1">Place 1</SelectItem>
                <SelectItem value="place-id-2">Place 2</SelectItem>
                <SelectItem value="place-id-3">Place 3</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
        <Button
          variant="default"
          className="w-full sm:w-[205px] h-[42px]"
          onClick={() => router.push('/admin/locations/create-location')}
        >
          <Plus /> Create new location
        </Button>
      </div>
      <div className="overflow-auto">{locationsMain}</div>
      <div className="mt-6 flex items-center justify-center">
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setPage(Math.max(filters.page - 1, 1))}
            disabled={filters.page === 1}
            className="w-9 h-9 p-0"
          >
            <ChevronLeft />
          </Button>
          <Pagination
            page={filters.page}
            totalPage={totalPages}
            setPage={setPage}
          />
          <Button
            variant="outline"
            size="sm"
            onClick={() => setPage(Math.min(filters.page + 1, totalPages))}
            disabled={filters.page === totalPages}
            className="w-9 h-9 p-0"
          >
            <ChevronRight />
          </Button>
        </div>
      </div>
    </div>
  );
}
