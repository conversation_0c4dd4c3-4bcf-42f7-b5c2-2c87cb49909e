import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import LocationPicker from './LocationPicker';

interface FormData {
  title: string;
  address: string;
  latitude?: number;
  longitude?: number;
  description: string;
}

interface BasicDetailsProps {
  data: FormData;
  onChange: (data: Partial<FormData>) => void;
}

export default function BasicDetailsForm({
  data,
  onChange,
}: BasicDetailsProps) {
  const handleInputChange = (field: keyof FormData, value: string) => {
    onChange({ [field]: value });
  };
  const handleLocationChange = (
    address?: string,
    latitude?: number,
    longitude?: number
  ) => {
    onChange({ address, latitude, longitude });
  };

  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <Label htmlFor="title" className="text-sm font-medium text-gray-900">
          Location Title
        </Label>
        <Input
          id="title"
          value={data.title}
          onChange={e => handleInputChange('title', e.target.value)}
          placeholder="Enter the location name"
          className="w-full"
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="address" className="text-sm font-medium text-gray-900">
          Address
        </Label>
        <div className="relative">
          <LocationPicker
            defaultAddress={{
              address: data.address,
              lat: data.latitude,
              lng: data.longitude,
            }}
            onChange={handleLocationChange}
          />
        </div>
      </div>

      <div className="space-y-2">
        <Label
          htmlFor="description"
          className="text-sm font-medium text-gray-900"
        >
          Description
        </Label>
        <Textarea
          id="description"
          value={data.description}
          onChange={e => handleInputChange('description', e.target.value)}
          placeholder="Describe what makes this location special..."
          className="w-full min-h-[120px]"
        />
      </div>
    </div>
  );
}
