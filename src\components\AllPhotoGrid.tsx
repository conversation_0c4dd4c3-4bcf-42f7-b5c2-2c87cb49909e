'use client';
import { Photo } from '@/types/location';
import Image from 'next/image';
import { Badge } from './ui/badge';

export default function AllPhotoGrid({
  images,
  onClickImage,
}: {
  images: Photo[];
  onClickImage: (index: number) => void;
}) {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2">
      {images.map((image, index) => (
        <div
          key={index}
          className="relative w-full h-auto aspect-square rounded-xl cursor-pointer"
          onClick={() => onClickImage(index)}
        >
          <Image
            src={image.url}
            alt="location image"
            fill
            className="object-cover rounded-lg"
          />
          {/* Overlay label */}
          <div className="absolute inset-0 flex items-center justify-center">
            <span className="text-white text-sm sm:text-base font-medium drop-shadow-md">
              {image.id}
            </span>
          </div>

          {/* Tags */}
          {image.tags && (
            <div className="absolute bottom-2 right-2 flex gap-1">
              {image.tags.map((tag, index) => (
                <Badge
                  key={index}
                  variant="secondary"
                  className={`${tag.color ? `${tag.color} text-white` : ''}`}
                >
                  {tag.name}
                </Badge>
              ))}
            </div>
          )}
        </div>
      ))}
    </div>
  );
}
