import React from 'react';
import { toast, ExternalToast } from 'sonner';
import { CheckCircleIcon } from './icons';
import { XCircleIcon } from 'lucide-react';
import { Loader2 } from 'lucide-react';
import { InfoIcon } from 'lucide-react';

export function toastSuccess(message: string, options?: ExternalToast) {
  toast.success(message, {
    ...options,
    icon: React.createElement(CheckCircleIcon, {
      className: 'w-4 h-4 text-green-500',
    }),
  });
}

export function toastError(message: string, options?: ExternalToast) {
  toast.error(message, {
    ...options,
    icon: React.createElement(XCircleIcon, {
      className: 'w-4 h-4 text-red-500',
    }),
  });
}

export function toastLoading(message: string, options?: ExternalToast) {
  return toast(message, {
    ...options,
    icon: React.createElement(Loader2, {
      className: 'w-4 h-4 text-blue-500 animate-spin',
    }),
  });
}

export function toastDismiss(id: string | number) {
  toast.dismiss(id);
}

export function toastWarning(message: string, options?: ExternalToast) {
  toast.warning(message, {
    ...options,
    icon: React.createElement(XCircleIcon, {
      className: 'w-4 h-4 text-yellow-500',
    }),
  });
}

export function toastInfo(message: string, options?: ExternalToast) {
  toast.info(message, {
    ...options,
    icon: React.createElement(InfoIcon, {
      className: 'w-4 h-4 text-blue-500',
    }),
  });
}
