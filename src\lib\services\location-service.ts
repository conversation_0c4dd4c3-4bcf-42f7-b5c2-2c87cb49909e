import { TagOption } from '@/types/tag';
import { httpClient } from '../http-client';
import { Location } from '@/types';

export interface PhotosData {
  file: File;
  tags: TagOption[];
}

export interface CreateLocationData {
  title: string;
  address: string;
  description: string;
  tagIds: string[];
  size: string;
  images: {
    url: string;
    order: number;
    key: string;
    tagIds: string[];
  }[];
  latitude?: number;
  longitude?: number;
}

const buildQueryParams = (params: {
  page?: number;
  search?: string;
  status?: string;
  tagIds?: string;
  place?: string;
  limit?: number;
}) => {
  const { page, search, status, tagIds, limit, place } = params;
  const queryParams: string[] = [];
  if (page) {
    queryParams.push(`page=${page}`);
  }
  if (limit) {
    queryParams.push(`limit=${limit}`);
  }
  if (search && search !== '') {
    queryParams.push(`search=${search}`);
  }
  if (status && status !== 'all') {
    queryParams.push(`status=${status}`);
  }
  if (tagIds && tagIds !== 'all') {
    queryParams.push(`tagIds=${tagIds}`);
  }
  if (place && place !== 'all') {
    queryParams.push(`place=${place}`);
  }
  return queryParams.join('&');
};

export interface UpdateLocationData extends Partial<CreateLocationData> {
  status?: 'active' | 'inactive' | 'pending';
}

/**
 * Location Service - Handles all location-related API operations
 */
export class LocationService {
  /**
   * Get all locations
   */
  async getLocations(
    page: number,
    search?: string,
    status?: string,
    tagIds?: string,
    place?: string,
    limit?: number
  ): Promise<{
    data: Location[];
    meta: { totalPages: number };
  }> {
    const queryParams = buildQueryParams({
      page,
      search,
      status,
      tagIds,
      place,
      limit,
    });

    return httpClient.get<{
      data: Location[];
      meta: { totalPages: number };
    }>(`/locations?${queryParams}`);
  }

  /**
   * Get location by ID
   */
  async getLocationById(id: string): Promise<Location> {
    return httpClient.get<Location>(`/locations/${id}`);
  }

  /**
   * Create new location
   */
  async createLocation(locationData: CreateLocationData): Promise<Location> {
    return httpClient.post<Location>('/locations', locationData);
  }

  /**
   * Update location by ID
   */
  async updateLocation(
    id: string,
    locationData: UpdateLocationData
  ): Promise<Location> {
    return httpClient.patch<Location>(`/locations/${id}`, locationData);
  }

  /**
   * Delete location by ID
   */
  async deleteLocation(id: string): Promise<void> {
    return httpClient.delete<void>(`/locations/${id}`);
  }

  /**
   * Search locations by query
   */
  async searchLocations(query: string): Promise<Location[]> {
    return httpClient.get<Location[]>(
      `/locations/search?q=${encodeURIComponent(query)}`
    );
  }

  /**
   * Get locations by status
   */
  async getLocationsByStatus(
    status: 'active' | 'inactive' | 'pending'
  ): Promise<Location[]> {
    return httpClient.get<Location[]>(`/locations?status=${status}`);
  }

  /**
   * Upload location images
   */
  async uploadLocationImages(
    locationId: string,
    files: File[]
  ): Promise<{ urls: string[] }> {
    const formData = new FormData();
    files.forEach((file, index) => {
      formData.append(`images[${index}]`, file);
    });
    formData.append('locationId', locationId);

    return httpClient.uploadFile<{ urls: string[] }>(
      '/locations/images',
      files[0],
      {
        locationId,
        imageCount: files.length.toString(),
      }
    );
  }
}

// Export singleton instance
export const locationService = new LocationService();

export const MOCK_DATA: Location[] = [
  {
    id: '1',
    title: 'Downtown Creative Loft',
    coverImageUrl:
      'https://picsum.photos/id/80/350/200.jpg?hmac=Cz94yz1-BauZ1S70aYhHQj7aRmHRqIHjNyaroT81TpI',
    images: [
      {
        url: 'https://picsum.photos/id/80/350/200.jpg?hmac=Cz94yz1-BauZ1S70aYhHQj7aRmHRqIHjNyaroT81TpI',
        order: 0,
        tags: [
          { id: '1', name: 'commercial', type: 'category' },
          { id: '2', name: 'modern', type: 'category' },
        ],
      },
    ],
    name: 'Downtown Creative Loft',
    address: '123 Main St, Los Angeles, CA',
    city: 'Los Angeles',
    state: 'CA',
    zipCode: '90012',
    country: 'USA',
    status: 'active',
    createdAt: '',
    updatedAt: '',
    tags: [
      { id: '1', name: 'commercial', type: 'category', color: '#3B82F6' },
      { id: '2', name: 'modern', type: 'category', color: '#10B981' },
    ],
  },
  {
    id: '2',
    title: 'Rooftop Garden Terrace',
    coverImageUrl:
      'https://picsum.photos/id/80/350/200.jpg?hmac=Cz94yz1-BauZ1S70aYhHQj7aRmHRqIHjNyaroT81TpI',
    images: [
      {
        url: 'https://picsum.photos/id/80/350/200.jpg?hmac=Cz94yz1-BauZ1S70aYhHQj7aRmHRqIHjNyaroT81TpI',
        order: 0,
        tags: [
          { id: '1', name: 'commercial', type: 'category' },
          { id: '2', name: 'luxury', type: 'category' },
        ],
      },
    ],
    name: 'Rooftop Garden Terrace',
    address: '456 Sunset Blvd, Hollywood, CA',
    city: 'Hollywood',
    state: 'CA',
    zipCode: '90012',
    country: 'USA',
    status: 'active',
    createdAt: '',
    updatedAt: '',
    tags: [
      { id: '1', name: 'commercial', type: 'category', color: '#3B82F6' },
      { id: '2', name: 'luxury', type: 'category', color: '#F59E0B' },
    ],
  },
  {
    id: '3',
    title: 'Industrial Warehouse',
    coverImageUrl:
      'https://picsum.photos/id/80/350/200.jpg?hmac=Cz94yz1-BauZ1S70aYhHQj7aRmHRqIHjNyaroT81TpI',
    images: [
      {
        url: 'https://picsum.photos/id/80/350/200.jpg?hmac=Cz94yz1-BauZ1S70aYhHQj7aRmHRqIHjNyaroT81TpI',
        order: 0,
        tags: [
          { id: '1', name: 'industrial', type: 'category' },
          { id: '2', name: 'warehouse', type: 'category' },
        ],
      },
    ],
    name: 'Industrial Warehouse',
    address: '789 Arts District, LA, CA',
    city: 'Los Angeles',
    state: 'CA',
    zipCode: '90012',
    country: 'USA',
    status: 'active',
    createdAt: '',
    updatedAt: '',
    tags: [
      { id: '1', name: 'outdoor', type: 'category', color: '#059669' },
      { id: '2', name: 'urban', type: 'category', color: '#6B7280' },
    ],
  },
  {
    id: '4',
    title: 'Downtown Creative Loft',
    coverImageUrl:
      'https://picsum.photos/id/80/350/200.jpg?hmac=Cz94yz1-BauZ1S70aYhHQj7aRmHRqIHjNyaroT81TpI',
    images: [
      {
        url: 'https://picsum.photos/id/80/350/200.jpg?hmac=Cz94yz1-BauZ1S70aYhHQj7aRmHRqIHjNyaroT81TpI',
        order: 0,
        tags: [
          { id: '1', name: 'commercial', type: 'category' },
          { id: '2', name: 'modern', type: 'category' },
        ],
      },
    ],
    name: 'Downtown Creative Loft',
    address: '123 Main St, Los Angeles, CA',
    city: 'Los Angeles',
    state: 'CA',
    zipCode: '90012',
    country: 'USA',
    status: 'active',
    createdAt: '',
    updatedAt: '',
    tags: [
      { id: '1', name: 'commercial', type: 'category', color: '#3B82F6' },
      { id: '2', name: 'modern', type: 'category', color: '#10B981' },
    ],
  },
  {
    id: '5',
    title: 'Rooftop Garden Terrace',
    coverImageUrl:
      'https://picsum.photos/id/80/350/200.jpg?hmac=Cz94yz1-BauZ1S70aYhHQj7aRmHRqIHjNyaroT81TpI',
    images: [
      {
        url: 'https://picsum.photos/id/80/350/200.jpg?hmac=Cz94yz1-BauZ1S70aYhHQj7aRmHRqIHjNyaroT81TpI',
        order: 0,
        tags: [
          { id: '1', name: 'commercial', type: 'category' },
          { id: '2', name: 'luxury', type: 'category' },
        ],
      },
    ],
    name: 'Rooftop Garden Terrace',
    address: '456 Sunset Blvd, Hollywood, CA',
    city: 'Hollywood',
    state: 'CA',
    zipCode: '90012',
    country: 'USA',
    status: 'active',
    createdAt: '',
    updatedAt: '',
    tags: [
      { id: '1', name: 'commercial', type: 'category', color: '#3B82F6' },
      { id: '2', name: 'luxury', type: 'category', color: '#F59E0B' },
    ],
  },
  {
    id: '6',
    title: 'Industrial Warehouse',
    coverImageUrl:
      'https://picsum.photos/id/80/350/200.jpg?hmac=Cz94yz1-BauZ1S70aYhHQj7aRmHRqIHjNyaroT81TpI',
    images: [
      {
        url: 'https://picsum.photos/id/80/350/200.jpg?hmac=Cz94yz1-BauZ1S70aYhHQj7aRmHRqIHjNyaroT81TpI',
        order: 0,
        tags: [
          { id: '1', name: 'outdoor', type: 'category' },
          { id: '2', name: 'urban', type: 'category' },
        ],
      },
    ],
    name: 'Industrial Warehouse',
    address: '789 Arts District, LA, CA',
    city: 'Los Angeles',
    state: 'CA',
    zipCode: '90012',
    country: 'USA',
    status: 'active',
    createdAt: '',
    updatedAt: '',
    tags: [
      { id: '1', name: 'outdoor', type: 'category', color: '#059669' },
      { id: '2', name: 'urban', type: 'category', color: '#6B7280' },
    ],
  },
];
