'use client';

import { useSession } from 'next-auth/react';
import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { httpClient } from '@/lib/http-client';

/**
 * Test component to validate authentication improvements
 * This component helps verify:
 * 1. Reduced session calls through token caching
 * 2. Proper 401 error handling and retry logic
 * 3. Token refresh functionality
 */
export default function AuthTestComponent() {
  const { data: session, status } = useSession();
  const [testResults, setTestResults] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const addResult = (message: string) => {
    setTestResults(prev => [
      ...prev,
      `${new Date().toLocaleTimeString()}: ${message}`,
    ]);
  };

  // Test 1: Multiple rapid API calls to verify token caching
  const testTokenCaching = async () => {
    setIsLoading(true);
    addResult('Starting token caching test...');

    try {
      const startTime = Date.now();

      // Make 5 rapid API calls - should use cached token for most
      const promises = Array.from({ length: 5 }, (_, i) =>
        httpClient
          .get('/users/me')
          .catch(err => ({ error: err.message, index: i }))
      );

      const results = await Promise.all(promises);
      const endTime = Date.now();

      const successCount = results.filter(r => !('error' in r)).length;
      addResult(`Token caching test completed in ${endTime - startTime}ms`);
      addResult(`Successful requests: ${successCount}/5`);

      if (successCount >= 4) {
        addResult('✅ Token caching appears to be working');
      } else {
        addResult('❌ Token caching may have issues');
      }
    } catch (error) {
      addResult(`❌ Token caching test failed: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  // Test 2: Test session access patterns
  const testSessionAccess = async () => {
    addResult('Testing session access...');

    if (!session) {
      addResult('❌ No session available');
      return;
    }

    addResult(`✅ Session available for user: ${session.user?.email}`);
    addResult(`✅ Access token present: ${!!session.accessToken}`);
    addResult(`✅ User role: ${session.user?.role}`);
  };

  // Test 3: Test API error handling
  const testErrorHandling = async () => {
    setIsLoading(true);
    addResult('Testing error handling...');

    try {
      // Try to access a non-existent endpoint to test error handling
      await httpClient.get('/non-existent-endpoint');
      addResult('❌ Expected error but request succeeded');
    } catch (error) {
      if (error instanceof Error) {
        addResult(`✅ Error handling working: ${error.message}`);
      } else {
        addResult('✅ Error caught successfully');
      }
    } finally {
      setIsLoading(false);
    }
  };

  // Clear test results
  const clearResults = () => {
    setTestResults([]);
  };

  // Auto-run basic tests on component mount
  useEffect(() => {
    if (status === 'authenticated') {
      testSessionAccess();
    }
  }, [status, session]);

  if (status === 'loading') {
    return (
      <Card className="w-full max-w-2xl mx-auto">
        <CardHeader>
          <CardTitle>Authentication Test</CardTitle>
        </CardHeader>
        <CardContent>
          <p>Loading session...</p>
        </CardContent>
      </Card>
    );
  }

  if (status === 'unauthenticated') {
    return (
      <Card className="w-full max-w-2xl mx-auto">
        <CardHeader>
          <CardTitle>Authentication Test</CardTitle>
        </CardHeader>
        <CardContent>
          <Alert>
            <AlertDescription>
              Please sign in to test authentication improvements.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>Authentication Improvements Test</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex flex-wrap gap-2">
          <Button
            onClick={testTokenCaching}
            disabled={isLoading}
            variant="outline"
          >
            Test Token Caching
          </Button>
          <Button
            onClick={testSessionAccess}
            disabled={isLoading}
            variant="outline"
          >
            Test Session Access
          </Button>
          <Button
            onClick={testErrorHandling}
            disabled={isLoading}
            variant="outline"
          >
            Test Error Handling
          </Button>
          <Button onClick={clearResults} variant="secondary">
            Clear Results
          </Button>
        </div>

        {testResults.length > 0 && (
          <div className="mt-4">
            <h3 className="font-semibold mb-2">Test Results:</h3>
            <div className="bg-gray-50 p-3 rounded-md max-h-64 overflow-y-auto">
              {testResults.map((result, index) => (
                <div key={index} className="text-sm font-mono mb-1">
                  {result}
                </div>
              ))}
            </div>
          </div>
        )}

        <Alert>
          <AlertDescription>
            <strong>How to verify improvements:</strong>
            <ul className="list-disc list-inside mt-2 space-y-1">
              <li>Open browser dev tools → Network tab</li>
              <li>
                Click "Test Token Caching" and observe reduced session requests
              </li>
              <li>Monitor console for simplified token refresh logs</li>
              <li>Check that 401 errors are handled gracefully with retries</li>
            </ul>
          </AlertDescription>
        </Alert>
      </CardContent>
    </Card>
  );
}
