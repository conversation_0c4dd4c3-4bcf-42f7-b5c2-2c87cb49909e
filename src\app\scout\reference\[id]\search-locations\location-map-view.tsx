'use client';

import { useC<PERSON>back, useEffect, useMemo, useState } from 'react';
import {
  APIProvider,
  Map,
  Marker,
  InfoWindow,
} from '@vis.gl/react-google-maps';
import { Heading } from '@/components/ui/typography';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from '@/components/ui/carousel';
import Image from 'next/image';
import { Location } from '@/types';

interface LocationsMapViewProps {
  locations?: Location[];
}

interface LocationWithCoords extends Location {
  lat: number;
  lng: number;
  imageUrls?: string[];
}

export default function LocationsMapView({
  locations = [],
}: LocationsMapViewProps) {
  const [selectedLocation, setSelectedLocation] =
    useState<LocationWithCoords | null>(null);
  const [mapCenter, setMapCenter] = useState({ lat: 34.0522, lng: -118.2437 }); // Default to LA

  // Mock coordinates for the locations (in real app, these would come from the location data)
  const locationsWithCoords: LocationWithCoords[] = useMemo(() => {
    const mockCoordinates = [
      { lat: 34.0522, lng: -118.2437 },
      { lat: 34.0928, lng: -118.3287 },
      { lat: 34.0394, lng: -118.2334 },
      { lat: 34.0522, lng: -118.2437 },
      { lat: 34.0928, lng: -118.3287 },
      { lat: 34.0394, lng: -118.2334 },
    ];

    return locations.map((location, index) => ({
      ...location,
      lat: mockCoordinates[index % mockCoordinates.length]?.lat || 34.0522,
      lng: mockCoordinates[index % mockCoordinates.length]?.lng || -118.2437,
      id: location.id || `location-${index}`,
      // imageUrls: location.imageUrls || [],
    }));
  }, [locations]);

  // Calculate center point for the map based on locations
  useEffect(() => {
    if (locationsWithCoords.length > 0) {
      const centerLat =
        locationsWithCoords.reduce((sum, loc) => sum + loc.lat, 0) /
        locationsWithCoords.length;
      const centerLng =
        locationsWithCoords.reduce((sum, loc) => sum + loc.lng, 0) /
        locationsWithCoords.length;
      setMapCenter({ lat: centerLat, lng: centerLng });
    }
  }, [locationsWithCoords]);

  const handleMarkerClick = useCallback((location: LocationWithCoords) => {
    setSelectedLocation(location);
  }, []);

  const handleInfoWindowClose = useCallback(() => {
    setSelectedLocation(null);
  }, []);

  const handleMapClick = useCallback(() => {
    setSelectedLocation(null);
  }, []);

  if (!process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY) {
    return (
      <div className="w-full h-[calc(100vh-280px)] rounded-lg overflow-hidden border bg-gray-100 flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="text-gray-500">
            <svg
              className="w-16 h-16 mx-auto mb-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1.5}
                d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
              />
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1.5}
                d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
              />
            </svg>
          </div>
          <h3 className="text-lg font-semibold text-gray-700">
            Google Maps API Key Required
          </h3>
          <p className="text-gray-500 max-w-md">
            To display the map, please add your Google Maps API key to the
            environment variables as{' '}
            <code className="bg-gray-200 px-2 py-1 rounded text-sm">
              NEXT_PUBLIC_GOOGLE_MAPS_API_KEY
            </code>
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full h-[calc(100vh-280px)] rounded-2xl overflow-hidden border">
      <APIProvider apiKey={process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY}>
        <Map
          defaultCenter={mapCenter}
          defaultZoom={12}
          mapId="scoutr-locations-map"
          className="w-full h-full"
          gestureHandling="greedy"
          disableDefaultUI={false}
          onClick={() => handleMapClick()}
        >
          {locationsWithCoords.map(location => (
            <Marker
              key={location.id}
              position={{ lat: location.lat, lng: location.lng }}
              onClick={() => handleMarkerClick(location)}
              title={location.name}
              // icon={{
              //   url: '/assets/icons/location-marker.svg', // put your file in /public/assets/
              //   scaledSize: new google.maps.Size(24, 24), // resize
              //   anchor: new google.maps.Point(12, 12), // bottom-center
              // }}
            />
          ))}

          {selectedLocation && (
            <InfoWindow
              position={{
                lat: selectedLocation.lat,
                lng: selectedLocation.lng,
              }}
              onCloseClick={handleInfoWindowClose}
              headerDisabled
              pixelOffset={[0, -36]}
              maxWidth={360}
              minWidth={360}
            >
              <div>
                <Carousel className="relative">
                  <CarouselContent>
                    {Array.from({ length: 5 }).map((_, index) => (
                      <CarouselItem key={index}>
                        <div className="relative w-full h-[200px]">
                          <Image
                            src={`https://picsum.photos/350/200?random=${index}`}
                            alt="Location"
                            width={350}
                            height={200}
                            className="w-full h-full object-cover rounded-t-lg"
                          />
                        </div>
                      </CarouselItem>
                    ))}
                  </CarouselContent>
                  <CarouselPrevious className="absolute top-1/2 left-2 -translate-y-1/2 z-10 w-8.5 h-10 bg-white shadow-[0px_10px_15px_0px_rgba(0,0,0,0.1),0px_4px_6px_0px_rgba(0,0,0,0.1)]" />
                  <CarouselNext className="absolute top-1/2 right-2 -translate-y-1/2 z-10 w-8.5 h-10 bg-white shadow-[0px_10px_15px_0px_rgba(0,0,0,0.1),0px_4px_6px_0px_rgba(0,0,0,0.1)]" />
                </Carousel>
                <div className="px-4 pt-4 pb-3">
                  <Heading level={6} className="mb-1">
                    {selectedLocation.name}
                  </Heading>
                  <p className="text-sm leading-5 text-foreground mb-2">
                    {selectedLocation.address}
                  </p>
                  <div className="flex flex-wrap gap-1.5">
                    {selectedLocation.tags?.map((tag, index) => (
                      <Badge
                        key={index}
                        className="inline-block text-sm leading-4 px-2 py-1 rounded-full"
                        variant="secondary"
                      >
                        {tag.name}
                      </Badge>
                    ))}
                  </div>
                  <div className="mt-5 flex items-center justify-center gap-2">
                    <Button
                      onClick={() => {
                        console.log('Add location:', selectedLocation);
                        handleInfoWindowClose();
                      }}
                      className="w-full max-w-[176px]"
                    >
                      <Plus /> Add to list
                    </Button>
                    <Button
                      onClick={() => {
                        console.log('View details:', selectedLocation);
                        handleInfoWindowClose();
                      }}
                      className="flex-1"
                      variant="outline"
                    >
                      View details
                    </Button>
                  </div>
                </div>
              </div>
            </InfoWindow>
          )}
        </Map>
      </APIProvider>
    </div>
  );
}
