'use client';

import { DistanceIcon } from '@/lib/icons';
import LocationPickerModal from './LocationPickerModal';
import { useState } from 'react';
import LocationAutocomplete from './LocationAutoComplete';
import { APIProvider } from '@vis.gl/react-google-maps';

interface LocationPickerProps {
  defaultAddress?: {
    lat?: number;
    lng?: number;
    address?: string;
  } | null;
  onChange: (address?: string, latitude?: number, longitude?: number) => void;
}

export default function LocationPicker({
  onChange,
  defaultAddress,
}: LocationPickerProps) {
  const [locationPickerOpen, setLocationPickerOpen] = useState<boolean>(false);
  const [address, setAddress] = useState(defaultAddress?.address ?? '');
  const handleChangeLocation = (
    data: { address?: string; lat?: number; lng?: number } | null
  ) => {
    if (data) {
      const { address, lat, lng } = data;
      onChange(address, lat, lng);
      setAddress(address ?? '');
    } else {
      onChange(undefined, undefined, undefined);
      setAddress('');
    }
  };

  return (
    <APIProvider
      apiKey={process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY!}
      libraries={['places']}
    >
      <LocationAutocomplete
        value={address}
        onSelect={place => {
          if (place) {
            const coords = {
              address: place.formatted_address,
              lat: place.geometry?.location?.lat(),
              lng: place.geometry?.location?.lng(),
            };
            handleChangeLocation(coords);
          } else handleChangeLocation(null);
        }}
      />
      <DistanceIcon
        onClick={() => setLocationPickerOpen(true)}
        className="absolute right-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400 cursor-pointer"
      />
      <LocationPickerModal
        defaultAddress={defaultAddress}
        open={locationPickerOpen}
        onOpenChange={setLocationPickerOpen}
        onChange={handleChangeLocation}
      />
    </APIProvider>
  );
}
