import { memo, useMemo, useCallback, useEffect, useRef } from 'react';
import { FileUpload } from '@/types';
import Image from 'next/image';
import React from 'react';
import { DropdownMenu } from '../ui/dropdown-menu';
import { DropdownMenuTrigger } from '../ui/dropdown-menu';
import { MoreHorizIcon } from '@/lib/icons';
import { DropdownMenuContent } from '../ui/dropdown-menu';
import { DropdownMenuItem } from '../ui/dropdown-menu';
import { Checkbox } from '../ui/checkbox';
import { CheckedState } from '@radix-ui/react-checkbox';
import { Badge } from '../ui/badge';
import { useFileUpload } from '@/hooks/useFileUpload';

interface GalleryItemProps {
  photo: FileUpload;
  onEdit?: () => void;
  onRemove?: () => void;
  onCheckedChange?: (checked: CheckedState) => void;
  onUploadComplete?: (file: FileUpload) => void;
  onUploadError?: () => void;
}

export const GalleryItem: React.FC<GalleryItemProps> = memo(
  ({
    photo,
    onEdit,
    onRemove,
    onCheckedChange,
    onUploadComplete,
    onUploadError,
  }: GalleryItemProps) => {
    const fileKey = useRef<string | null>(null);

    const { uploadSingleFile, uploadResult, uploadProgress, isUploading } =
      useFileUpload({
        onUploadComplete: (data: unknown) => {
          const result = data as { url: string; key: string };
          onUploadComplete?.({
            ...photo,
            url: result.url,
            key: result.key,
            isUploading: false,
            isUploaded: true,
            isUploadSuccess: uploadResult?.success ? true : false,
            isUploadError: uploadResult?.error ? true : false,
          });
        },
        onUploadError: () => {
          console.log('uploadError');
          onUploadError?.();
        },
      });

    useEffect(() => {
      // Create a unique key for this file to track uploads
      const currentFileKey = photo.file?.name + '_' + photo.file?.size;

      if (
        !photo.url &&
        photo.file &&
        !photo.isUploading &&
        !photo.isUploaded &&
        fileKey.current !== currentFileKey
      ) {
        fileKey.current = currentFileKey;
        uploadSingleFile(photo.file as File);
      }
    }, [
      uploadSingleFile,
      photo.file,
      photo.url,
      photo.isUploading,
      photo.isUploaded,
      photo,
    ]);

    // Memoize the image source to prevent URL recreation on every render
    const imageSrc = useMemo(() => {
      if (photo.url) {
        return photo.url;
      }
      if (photo.file && photo.file instanceof File) {
        return URL.createObjectURL(photo.file);
      }
      return '';
    }, [photo.url, photo.file]);

    // Memoize the checkbox change handler
    const handleCheckedChange = useCallback(
      (checked: CheckedState) => {
        onCheckedChange?.(checked);
      },
      [onCheckedChange]
    );

    // Memoize the edit handler
    const handleEdit = useCallback(() => {
      onEdit?.();
    }, [onEdit]);

    // Memoize the remove handler
    const handleRemove = useCallback(() => {
      onRemove?.();
    }, [onRemove]);

    return (
      <div className="relative w-full h-full group border rounded overflow-hidden">
        {imageSrc ? (
          <Image
            src={imageSrc}
            alt={photo.file?.name || 'photo'}
            width={264}
            height={169}
            className="w-full h-full object-cover"
          />
        ) : (
          <div className="w-full h-full bg-gray-200 flex items-center justify-center">
            <span className="text-gray-500 text-sm">No image</span>
          </div>
        )}
        {isUploading && (
          <>
            <div
              className="absolute bottom-0 right-0 w-full bg-black/80 transition-all duration-300"
              style={{ height: `${100 - uploadProgress}%` }}
            ></div>
            <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 text-white text-sm">
              {uploadProgress}%
            </div>
          </>
        )}
        <Checkbox
          id="gallery-item-checkbox"
          checked={photo.isChecked ?? false}
          onCheckedChange={handleCheckedChange}
          className="absolute left-4 top-4"
          onMouseDown={e => e.stopPropagation()}
        />
        <div
          className="absolute top-4 right-4 w-6 h-6 cursor-pointer flex items-center justify-center"
          onMouseDown={e => e.stopPropagation()}
        >
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <MoreHorizIcon
                className="cursor-pointer w-4 h-4"
                // onMouseDown={e => e.stopPropagation()}
              />
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem
                onClick={handleEdit}
                // onMouseDown={e => e.stopPropagation()}
              >
                Edit
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={handleRemove}
                // onMouseDown={e => e.stopPropagation()}
              >
                Remove
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
        <div className="absolute bottom-4 right-4 left-4 gap-2 flex justify-end flex-wrap-reverse">
          {(photo.tags?.length ?? 0) > 0 &&
            photo.tags?.map(tag => (
              <div className="text-right" key={tag.id}>
                <Badge
                  variant="secondary"
                  className={`capitalize ${tag.color ? `${tag.color} text-white` : ''}`}
                >
                  {tag.name}
                </Badge>
              </div>
            ))}
        </div>
      </div>
    );
  },
  // Custom comparison function to prevent unnecessary re-renders
  (prevProps, nextProps) => {
    return (
      prevProps.photo.key === nextProps.photo.key &&
      prevProps.photo.isChecked === nextProps.photo.isChecked &&
      prevProps.photo.url === nextProps.photo.url &&
      prevProps.photo.file === nextProps.photo.file &&
      prevProps.photo.isUploading === nextProps.photo.isUploading &&
      prevProps.photo.isUploaded === nextProps.photo.isUploaded &&
      JSON.stringify(prevProps.photo.tags) ===
        JSON.stringify(nextProps.photo.tags) &&
      prevProps.onEdit === nextProps.onEdit &&
      prevProps.onRemove === nextProps.onRemove &&
      prevProps.onCheckedChange === nextProps.onCheckedChange &&
      prevProps.onUploadComplete === nextProps.onUploadComplete &&
      prevProps.onUploadError === nextProps.onUploadError
    );
  }
);

GalleryItem.displayName = 'GalleryItem';
