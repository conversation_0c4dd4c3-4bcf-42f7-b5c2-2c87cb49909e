'use client';

import { usePageNavbar } from '@/hooks/use-page-navbar';
import {
  SearchIcon,
  ShareIcon,
  EditIcon,
  Download,
  ArrowLeft,
} from 'lucide-react';

/**
 * Example component showing how to use dynamic navbar items
 * This demonstrates the different ways to add navbar items to specific pages
 */
export function NavbarExample() {
  // Example 1: Simple navbar items with different variants
  const exampleItems = [
    {
      id: 'search',
      label: 'Search',
      icon: <SearchIcon className="h-4 w-4" />,
      onClick: () => console.log('Search clicked'),
      variant: 'outline' as const,
    },
    {
      id: 'edit',
      label: 'Edit',
      icon: <EditIcon className="h-4 w-4" />,
      onClick: () => console.log('Edit clicked'),
      variant: 'ghost' as const,
    },
    {
      id: 'share',
      label: 'Share',
      icon: <ShareIcon className="h-4 w-4" />,
      onClick: () => console.log('Share clicked'),
      variant: 'secondary' as const,
    },
  ];

  // Example 2: Navbar items with navigation links
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const navigationItems = [
    {
      id: 'back',
      label: 'Back',
      icon: <ArrowLeft className="h-4 w-4" />,
      href: '/previous-page',
      variant: 'ghost' as const,
    },
    {
      id: 'export',
      label: 'Export',
      icon: <Download className="h-4 w-4" />,
      onClick: () => console.log('Export clicked'),
      variant: 'outline' as const,
    },
  ];

  // Use the custom hook to set navbar items
  usePageNavbar(exampleItems);

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-4">Navbar Example</h1>
      <p className="text-gray-600 mb-4">
        This page demonstrates dynamic navbar items. Check the navbar above to
        see the additional items.
      </p>

      <div className="space-y-4">
        <div>
          <h2 className="text-lg font-semibold mb-2">Current Navbar Items:</h2>
          <ul className="list-disc list-inside space-y-1">
            {exampleItems.map(item => (
              <li key={item.id} className="text-sm">
                <strong>{item.label}</strong> - {item.variant} variant
                {/* Remove href check since exampleItems don't have href property */}
                {' (Click handler)'}
              </li>
            ))}
          </ul>
        </div>

        <div>
          <h2 className="text-lg font-semibold mb-2">Available Variants:</h2>
          <ul className="list-disc list-inside space-y-1 text-sm">
            <li>
              <strong>default</strong> - Standard button appearance
            </li>
            <li>
              <strong>destructive</strong> - Red/danger styling
            </li>
            <li>
              <strong>outline</strong> - Outlined button
            </li>
            <li>
              <strong>secondary</strong> - Secondary styling
            </li>
            <li>
              <strong>ghost</strong> - Transparent background
            </li>
            <li>
              <strong>link</strong> - Link-like appearance
            </li>
          </ul>
        </div>

        <div>
          <h2 className="text-lg font-semibold mb-2">Usage Examples:</h2>
          <pre className="bg-gray-100 p-4 rounded text-sm overflow-x-auto">
            {`// Simple usage with usePageNavbar hook
import { usePageNavbar } from '@/hooks/use-page-navbar';
import { SearchIcon } from 'lucide-react';

export function MyPage() {
  const navbarItems = [
    {
      id: 'search',
      label: 'Search',
      icon: <SearchIcon className="h-4 w-4" />,
      onClick: () => console.log('Search clicked'),
      variant: 'outline' as const,
    },
  ];

  usePageNavbar(navbarItems);

  return <div>My page content</div>;
}

// Direct usage with useNavbar hook
import { useNavbar } from '@/contexts/navbar-context';

export function MyPage() {
  const { setAdditionalItems, clearNavbarItems } = useNavbar();

  useEffect(() => {
    setAdditionalItems(navbarItems);
    return () => clearNavbarItems();
  }, []);

  return <div>My page content</div>;
}`}
          </pre>
        </div>
      </div>
    </div>
  );
}
