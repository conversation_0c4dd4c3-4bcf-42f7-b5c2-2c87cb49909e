'use client';

import React, {
  createContext,
  useContext,
  useState,
  useCallback,
  useMemo,
  ReactNode,
  useEffect,
} from 'react';
import { useRouter, useSearchParams } from 'next/navigation';

export type LocationCategory = 'residential' | 'commercial' | 'public' | null;

export interface LocationFilters {
  query: string;
  where: string;
  category: LocationCategory;
  subCategories: string[];
  style: string[];
  size: {
    small: boolean;
    medium: boolean;
    large: boolean;
  };
}

export const defaultFilters: LocationFilters = {
  query: '',
  where: '',
  category: null,
  subCategories: [],
  style: [],
  size: { small: false, medium: false, large: false },
};

// Utility functions for URL synchronization
function serializeFiltersToUrl(filters: LocationFilters): URLSearchParams {
  const params = new URLSearchParams();

  if (filters.query) params.set('query', filters.query);
  if (filters.where) params.set('where', filters.where);
  if (filters.category) params.set('category', filters.category);
  if (filters.subCategories.length > 0) {
    params.set('subCategories', filters.subCategories.join(','));
  }
  if (filters.style.length > 0) {
    params.set('style', filters.style.join(','));
  }

  // Handle size filters
  const sizeFilters = [];
  if (filters.size.small) sizeFilters.push('small');
  if (filters.size.medium) sizeFilters.push('medium');
  if (filters.size.large) sizeFilters.push('large');
  if (sizeFilters.length > 0) {
    params.set('size', sizeFilters.join(','));
  }

  return params;
}

function deserializeFiltersFromUrl(
  searchParams: URLSearchParams
): LocationFilters {
  const filters = { ...defaultFilters };

  const query = searchParams.get('query');
  if (query) filters.query = query;

  const where = searchParams.get('where');
  if (where) filters.where = where;

  const category = searchParams.get('category');
  if (category && ['residential', 'commercial', 'public'].includes(category)) {
    filters.category = category as LocationCategory;
  }

  const subCategories = searchParams.get('subCategories');
  if (subCategories) {
    filters.subCategories = subCategories.split(',').filter(Boolean);
  }

  const style = searchParams.get('style');
  if (style) {
    filters.style = style.split(',').filter(Boolean);
  }

  const size = searchParams.get('size');
  if (size) {
    const sizeArray = size.split(',').filter(Boolean);
    filters.size = {
      small: sizeArray.includes('small'),
      medium: sizeArray.includes('medium'),
      large: sizeArray.includes('large'),
    };
  }

  return filters;
}

interface SearchLocationContextType {
  // Panel state
  isPanelOpen: boolean;
  togglePanel: () => void;
  openPanel: () => void;
  closePanel: () => void;

  // Filters state
  filters: LocationFilters;
  setFilters: (
    updater: LocationFilters | ((prev: LocationFilters) => LocationFilters)
  ) => void;
}

const SearchLocationContext = createContext<
  SearchLocationContextType | undefined
>(undefined);

interface SearchLocationProviderProps {
  children: ReactNode;
}

export function SearchLocationProvider({
  children,
}: SearchLocationProviderProps) {
  const router = useRouter();
  const searchParams = useSearchParams();

  // Panel state
  const [isPanelOpen, setIsPanelOpen] = useState(false);

  // Initialize filters from URL or use defaults
  const [filters, setFiltersState] = useState<LocationFilters>(() => {
    return deserializeFiltersFromUrl(searchParams);
  });

  // Panel actions
  const togglePanel = useCallback(() => {
    setIsPanelOpen(prev => !prev);
  }, []);

  const openPanel = useCallback(() => {
    setIsPanelOpen(true);
  }, []);

  const closePanel = useCallback(() => {
    setIsPanelOpen(false);
  }, []);

  // Filters actions
  const setFilters = useCallback(
    (
      updater: LocationFilters | ((prev: LocationFilters) => LocationFilters)
    ) => {
      setFiltersState(prev =>
        typeof updater === 'function' ? updater(prev) : updater
      );
    },
    []
  );

  // Sync URL when filters change (but not on initial load)
  useEffect(() => {
    const urlParams = serializeFiltersToUrl(filters);
    const currentUrl = `${window.location.pathname}?${searchParams.toString()}`;
    const newUrl = `${window.location.pathname}?${urlParams.toString()}`;

    // Only update URL if it's different from current URL
    if (currentUrl !== newUrl) {
      router.replace(newUrl, { scroll: false });
    }
  }, [filters, router, searchParams]);

  // Sync filters when URL changes (e.g., browser back/forward)
  useEffect(() => {
    const urlFilters = deserializeFiltersFromUrl(searchParams);
    setFiltersState(urlFilters);
  }, [searchParams]);

  const value = useMemo<SearchLocationContextType>(
    () => ({
      // Panel state
      isPanelOpen,
      togglePanel,
      openPanel,
      closePanel,

      // Filters state
      filters,
      setFilters,
    }),
    [isPanelOpen, togglePanel, openPanel, closePanel, filters, setFilters]
  );

  return (
    <SearchLocationContext.Provider value={value}>
      {children}
    </SearchLocationContext.Provider>
  );
}

export function useSearchLocation() {
  const context = useContext(SearchLocationContext);
  if (context === undefined) {
    throw new Error(
      'useSearchLocation must be used within a SearchLocationProvider'
    );
  }
  return context;
}

// Convenience hooks for specific functionality
export function useSearchLocationFilters() {
  const { filters, setFilters } = useSearchLocation();
  return { filters, setFilters };
}

export function useSearchLocationPanel() {
  const { isPanelOpen, togglePanel, openPanel, closePanel } =
    useSearchLocation();
  return { isPanelOpen, togglePanel, openPanel, closePanel };
}
