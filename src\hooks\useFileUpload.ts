import { useState, useRef, useCallback } from 'react';
import { FileService } from '@/lib/services';
import { UploadProgress } from '@/types/file';

export interface FileValidation {
  maxSize?: number; // in bytes
  allowedTypes?: string[]; // MIME types
  maxFiles?: number; // for multiple files
}

export interface UseFileUploadOptions {
  validation?: FileValidation;
  onUploadComplete?: (data: unknown) => void;
  onUploadError?: (error: string) => void;
  onValidationError?: (error: string) => void;
  autoUpload?: boolean; // Automatically upload after validation
  presignedPath?: string;
  completePath?: string;
}

export interface UseFileUploadReturn {
  // File selection
  selectedFiles: File[];
  fileInputRef: React.RefObject<HTMLInputElement | null>;
  openFileDialog: () => void;
  handleFileChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  clearFiles: () => void;

  // Validation
  validationError: string;
  isValid: boolean;

  // Upload state
  isUploading: boolean;
  uploadProgress: number;
  uploadStatus: string;
  uploadResult: {
    success: boolean;
    data?: unknown;
    error?: string;
  } | null;

  // Upload functions
  uploadFiles: () => Promise<void>;
  uploadSingleFile: (
    file: File
  ) => Promise<{ success: boolean; data?: unknown; error?: string }>;

  // Utility functions
  validateFile: (file: File) => string | null;
  formatFileSize: (bytes: number) => string;
}

export function useFileUpload({
  validation = {},
  onUploadComplete,
  onUploadError,
  onValidationError,
  autoUpload = false,
  presignedPath,
  completePath,
}: UseFileUploadOptions = {}): UseFileUploadReturn {
  const fileInputRef = useRef<HTMLInputElement | null>(null);
  const fileService = new FileService(presignedPath, completePath);

  // File state
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [validationError, setValidationError] = useState<string>('');

  // Upload state
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploadStatus, setUploadStatus] = useState<string>('');
  const [uploadResult, setUploadResult] = useState<{
    success: boolean;
    data?: unknown;
    error?: string;
  } | null>(null);

  const {
    maxSize = 10 * 1024 * 1024, // 10MB default
    allowedTypes = ['image/jpeg', 'image/jpg', 'image/png'], // JPG/PNG default
    maxFiles = 1,
  } = validation;

  const validateFile = useCallback(
    (file: File): string | null => {
      // Check file type
      if (allowedTypes.length > 0 && !allowedTypes.includes(file.type)) {
        return `File "${file.name}" is not a supported format. Allowed types: ${allowedTypes
          .map(type => type.split('/')[1].toUpperCase())
          .join(', ')}`;
      }

      // Check file size
      if (file.size > maxSize) {
        const maxSizeMB = Math.round(maxSize / (1024 * 1024));
        return `File "${file.name}" is too large. Maximum size: ${maxSizeMB}MB`;
      }

      return null;
    },
    [allowedTypes, maxSize]
  );

  const validateFiles = useCallback(
    (files: FileList | null): { valid: File[]; errors: string[] } => {
      if (!files || files.length === 0) {
        return { valid: [], errors: [] };
      }

      const fileArray = Array.from(files);
      const errors: string[] = [];
      const validFiles: File[] = [];

      // Check max files limit
      if (fileArray.length > maxFiles) {
        errors.push(`Too many files selected. Maximum allowed: ${maxFiles}`);
        return { valid: [], errors };
      }

      // Validate each file
      fileArray.forEach(file => {
        const error = validateFile(file);
        if (error) {
          errors.push(error);
        } else {
          validFiles.push(file);
        }
      });

      return { valid: validFiles, errors };
    },
    [maxFiles, validateFile]
  );

  const formatFileSize = useCallback((bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }, []);

  const openFileDialog = useCallback(() => {
    fileInputRef.current?.click();
  }, []);

  const clearFiles = useCallback(() => {
    setSelectedFiles([]);
    setValidationError('');
    setUploadResult(null);
    setUploadStatus('');
    setUploadProgress(0);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  }, []);

  const uploadSingleFile = useCallback(
    async (
      file: File
    ): Promise<{ success: boolean; data?: unknown; error?: string }> => {
      setIsUploading(true);
      setUploadProgress(0);
      setUploadStatus('Starting upload...');
      setUploadResult(null);

      try {
        const result = await fileService.uploadFile(
          file,
          (progressData: UploadProgress) => {
            setUploadProgress(progressData.percentage);
            setUploadStatus(`Uploading... ${progressData.percentage}%`);
          }
        );

        setUploadResult(result);
        console.log('uploadResult', result);

        if (result.success && result.data) {
          setUploadStatus('Upload completed successfully!');
          onUploadComplete?.(result.data);
        } else {
          setUploadStatus(`Upload failed: ${result.error}`);
          onUploadError?.(result.error || 'Upload failed');
        }

        return result;
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : 'Upload failed';
        const result = { success: false, error: errorMessage };

        setUploadResult(result);
        setUploadStatus(`Upload failed: ${errorMessage}`);
        onUploadError?.(errorMessage);

        return result;
      } finally {
        setIsUploading(false);
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [onUploadComplete, onUploadError]
  );

  const handleFileChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const files = event.target.files;
      const { valid, errors } = validateFiles(files);

      // Clear previous state
      setValidationError('');
      setSelectedFiles([]);
      setUploadResult(null);
      setUploadStatus('');
      setUploadProgress(0);

      if (errors.length > 0) {
        const errorMessage = errors.join('; ');
        setValidationError(errorMessage);
        onValidationError?.(errorMessage);
        return;
      }

      if (valid.length > 0) {
        setSelectedFiles(valid);

        // Auto-upload if enabled
        if (autoUpload && valid.length === 1) {
          uploadSingleFile(valid[0]);
        }
      }
    },
    [validateFiles, onValidationError, autoUpload, uploadSingleFile]
  );

  const uploadFiles = useCallback(async (): Promise<void> => {
    if (selectedFiles.length === 0) return;

    // For now, handle single file upload
    if (selectedFiles.length === 1) {
      await uploadSingleFile(selectedFiles[0]);
    } else {
      // Handle multiple files (could be extended)
      console.warn('Multiple file upload not implemented yet');
    }
  }, [selectedFiles, uploadSingleFile]);

  const isValid = selectedFiles.length > 0 && validationError === '';

  return {
    // File selection
    selectedFiles,
    fileInputRef,
    openFileDialog,
    handleFileChange,
    clearFiles,

    // Validation
    validationError,
    isValid,

    // Upload state
    isUploading,
    uploadProgress,
    uploadStatus,
    uploadResult,

    // Upload functions
    uploadFiles,
    uploadSingleFile,

    // Utility functions
    validateFile,
    formatFileSize,
  };
}

// Convenience hooks for common use cases
export function useImageUpload(
  options?: Omit<UseFileUploadOptions, 'validation'>
) {
  return useFileUpload({
    ...options,
    validation: {
      maxSize: 10 * 1024 * 1024, // 10MB
      allowedTypes: ['image/jpeg', 'image/jpg', 'image/png'],
      maxFiles: 1,
    },
  });
}

export function useDocumentUpload(
  options?: Omit<UseFileUploadOptions, 'validation'>
) {
  return useFileUpload({
    ...options,
    validation: {
      maxSize: 50 * 1024 * 1024, // 50MB
      allowedTypes: [
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'text/plain',
      ],
      maxFiles: 1,
    },
  });
}
