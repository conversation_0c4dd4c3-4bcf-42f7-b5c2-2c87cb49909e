import * as Sentry from '@sentry/nextjs';

export async function register() {
  if (process.env.NEXT_RUNTIME === 'nodejs') {
    await import('../sentry.server.config');
  }

  if (process.env.NEXT_RUNTIME === 'edge') {
    await import('../sentry.edge.config');
  }

  if (typeof window !== 'undefined') {
    performance.mark('client-start');

    requestIdleCallback(() => {
      performance.measure('client-hydration', 'client-start');
      const measures = performance.getEntriesByName('client-hydration');
      console.log('[Hydration Perf]', measures[0]?.duration);
    });
  }
}

export const onRequestError = Sentry.captureRequestError;
