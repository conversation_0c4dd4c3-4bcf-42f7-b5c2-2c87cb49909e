import { use } from 'react';
import ReferenceListClient from './reference-list-client';
import { Location } from '@/types/location';
import { Reference } from '@/types';

const locations: Location[] = [
  {
    id: '1',
    name: 'Downtown Creative Loft',
    address: '123 Main St',
    city: 'Los Angeles',
    state: 'CA',
    zipCode: '90012',
    country: 'USA',
    latitude: 34.0522,
    longitude: -118.2437,
    tags: [
      { id: 'residential', name: 'residential', type: 'category' },
      { id: 'modern', name: 'modern', type: 'category' },
    ],
    description:
      'Perfect lighting setup here! The natural light from the east windows works great for morning shoots.',
    comments: 1,
    images: [
      {
        url: 'https://picsum.photos/350/200?random=1',
        tags: [{ id: '', name: '', type: '' }],
        order: 0,
      },
      {
        url: 'https://picsum.photos/350/200?random=5',
        tags: [{ id: '', name: '', type: '' }],
        order: 1,
      },
      {
        url: 'https://picsum.photos/350/200?random=6',
        tags: [{ id: '', name: '', type: '' }],
        order: 2,
      },
      {
        url: 'https://picsum.photos/350/200?random=7',
        tags: [{ id: '', name: '', type: '' }],
        order: 3,
      },
      {
        url: 'https://picsum.photos/350/200?random=8',
        tags: [{ id: '', name: '', type: '' }],
        order: 4,
      },
      {
        url: 'https://picsum.photos/350/200?random=9',
        tags: [{ id: '', name: '', type: '' }],
        order: 5,
      },
    ],
    coverImageUrl: 'https://picsum.photos/350/200?random=1',
    title: 'Downtown Creative Loft',
    createdAt: '',
    updatedAt: '',
    status: '',
  },
  {
    id: '2',
    title: 'Rooftop Garden Terrace',
    name: 'Rooftop Garden Terrace',
    address: '456 Sunset Blvd',
    city: 'Hollywood',
    state: 'CA',
    zipCode: '90028',
    country: 'USA',
    latitude: 34.0928,
    longitude: -118.3587,
    tags: [
      { id: 'commercial', name: 'Commercial', type: 'category' },
      { id: 'modern', name: 'Modern', type: 'category' },
    ],
    description:
      'The terrace has an ideal lighting arrangement! The morning sun streaming in from the east side is perfect for outdoor shoots.',
    comments: 1,
    images: [
      {
        url: 'https://picsum.photos/350/200?random=2',
        tags: [],
        order: 0,
      },
      {
        url: 'https://picsum.photos/350/200?random=3',
        tags: [],
        order: 1,
      },
    ],
    coverImageUrl: 'https://picsum.photos/350/200?random=2',
    createdAt: '2023-01-01T10:00:00Z',
    updatedAt: '2023-01-01T10:00:00Z',
    status: 'active',
  },
  {
    id: '3',
    title: 'Industrial Warehouse',
    name: 'Industrial Warehouse',
    address: '789 Warehouse Ave',
    city: 'Los Angeles',
    state: 'CA',
    zipCode: '90013',
    country: 'USA',
    latitude: 34.0452,
    longitude: -118.2566,
    tags: [
      { id: 'commercial', name: 'Commercial', type: 'category' },
      { id: 'warehouse', name: 'Warehouse', type: 'category' },
    ],
    description:
      'The lighting in this warehouse is ideal! The natural light streaming in from the east-facing windows is perfect for morning photography',
    comments: 0,
    images: [
      {
        url: 'https://picsum.photos/350/200?random=4',
        tags: [],
        order: 1,
      },
    ],
    coverImageUrl: 'https://picsum.photos/350/200?random=4',
    createdAt: '2023-02-01T11:00:00Z',
    updatedAt: '2023-02-01T11:00:00Z',
    status: 'active',
  },
];
const mockReference: Reference = {
  id: '1',
  projectName: 'Downtown Coffee Shop Scene',
  productionHouse: 'Netflix Studios',
  shootDates: {
    from: '2025-01-15T00:00:00.000Z',
    to: '2025-01-17T00:00:00.000Z',
  },
  status: 'in_progress',
  locations,
};
export default function PreviewReferenceList({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = use(params);
  console.log(id);
  const referenceData = mockReference;
  return <ReferenceListClient data={referenceData} />;
}
