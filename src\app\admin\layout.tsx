'use client';

import { ScrollArea } from '@/components/ui/scroll-area';
import AdminSidebar from './admin-sidebar';
import { usePathname } from 'next/navigation';

interface PageLayoutProps {
  children: React.ReactNode;
}
const withoutSidebarPathNames = [
  '/admin/locations/create-location',
  '/admin/locations/edit-location',
];
export default function AdminLayout({ children }: PageLayoutProps) {
  const pathname = usePathname();

  return (
    <div className="flex h-full w-full">
      {!withoutSidebarPathNames.some(path => pathname.startsWith(path)) && (
        <AdminSidebar />
      )}

      <ScrollArea className="px-4 sm:px-8 2xl:px-20 py-4 sm:py-6 h-[calc(100dvh-var(--navbar-height))] w-full">
        {children}
      </ScrollArea>
    </div>
  );
}
