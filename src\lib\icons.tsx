import React from 'react';

// SVG Icon Components
export const DashboardIcon = ({
  className = 'h-4 w-4',
  ...props
}: React.SVGProps<SVGSVGElement>) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
    {...props}
  >
    <path d="M16 16H0V0H16V16Z" stroke="#E5E7EB" />
    <g clipPath="url(#clip0_322_4793)">
      <path
        d="M1.75 1.75C1.75 1.26602 1.35898 0.875 0.875 0.875C0.391016 0.875 0 1.26602 0 1.75V10.9375C0 12.1461 0.978906 13.125 2.1875 13.125H13.125C13.609 13.125 14 12.734 14 12.25C14 11.766 13.609 11.375 13.125 11.375H2.1875C1.94687 11.375 1.75 11.1781 1.75 10.9375V1.75ZM12.868 4.11797C13.2098 3.77617 13.2098 3.22109 12.868 2.8793C12.5262 2.5375 11.9711 2.5375 11.6293 2.8793L8.75 5.76133L7.18047 4.1918C6.83867 3.85 6.28359 3.85 5.9418 4.1918L2.8793 7.2543C2.5375 7.59609 2.5375 8.15117 2.8793 8.49297C3.22109 8.83477 3.77617 8.83477 4.11797 8.49297L6.5625 6.05117L8.13203 7.6207C8.47383 7.9625 9.02891 7.9625 9.3707 7.6207L12.8707 4.1207L12.868 4.11797Z"
        fill="#737373"
      />
    </g>
    <defs>
      <clipPath id="clip0_322_4793">
        <path d="M0 0H14V14H0V0Z" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const UsersIcon = ({
  className = 'h-4 w-4',
  ...props
}: React.SVGProps<SVGSVGElement>) => (
  <svg
    width="18"
    height="14"
    viewBox="0 0 18 14"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
    {...props}
  >
    <g clipPath="url(#clip0_322_4800)">
      <g clipPath="url(#clip1_322_4800)">
        <path
          d="M3.9375 0C4.51766 0 5.07406 0.230468 5.4843 0.640704C5.89453 1.05094 6.125 1.60734 6.125 2.1875C6.125 2.76766 5.89453 3.32406 5.4843 3.7343C5.07406 4.14453 4.51766 4.375 3.9375 4.375C3.35734 4.375 2.80094 4.14453 2.3907 3.7343C1.98047 3.32406 1.75 2.76766 1.75 2.1875C1.75 1.60734 1.98047 1.05094 2.3907 0.640704C2.80094 0.230468 3.35734 0 3.9375 0ZM14 0C14.5802 0 15.1366 0.230468 15.5468 0.640704C15.957 1.05094 16.1875 1.60734 16.1875 2.1875C16.1875 2.76766 15.957 3.32406 15.5468 3.7343C15.1366 4.14453 14.5802 4.375 14 4.375C13.4198 4.375 12.8634 4.14453 12.4532 3.7343C12.043 3.32406 11.8125 2.76766 11.8125 2.1875C11.8125 1.60734 12.043 1.05094 12.4532 0.640704C12.8634 0.230468 13.4198 0 14 0ZM0 8.16758C0 6.55703 1.30703 5.25 2.91758 5.25H4.08516C4.51992 5.25 4.93281 5.3457 5.30469 5.51523C5.26914 5.71211 5.25273 5.91719 5.25273 6.125C5.25273 7.16953 5.71211 8.10742 6.43672 8.75C6.43125 8.75 6.42578 8.75 6.41758 8.75H0.582422C0.2625 8.75 0 8.4875 0 8.16758ZM11.0824 8.75C11.077 8.75 11.0715 8.75 11.0633 8.75C11.7906 8.10742 12.2473 7.16953 12.2473 6.125C12.2473 5.91719 12.2281 5.71484 12.1953 5.51523C12.5672 5.34297 12.9801 5.25 13.4148 5.25H14.5824C16.193 5.25 17.5 6.55703 17.5 8.16758C17.5 8.49023 17.2375 8.75 16.9176 8.75H11.0824ZM6.125 6.125C6.125 5.42881 6.40156 4.76113 6.89384 4.26884C7.38613 3.77656 8.05381 3.5 8.75 3.5C9.44619 3.5 10.1139 3.77656 10.6062 4.26884C11.0984 4.76113 11.375 5.42881 11.375 6.125C11.375 6.82119 11.0984 7.48887 10.6062 7.98116C10.1139 8.47344 9.44619 8.75 8.75 8.75C8.05381 8.75 7.38613 8.47344 6.89384 7.98116C6.40156 7.48887 6.125 6.82119 6.125 6.125ZM3.5 13.2699C3.5 11.2574 5.13242 9.625 7.14492 9.625H10.3551C12.3676 9.625 14 11.2574 14 13.2699C14 13.6719 13.6746 14 13.2699 14H4.23008C3.82812 14 3.5 13.6746 3.5 13.2699Z"
          fill={props.color ?? '#A3A3A3'}
        />
      </g>
    </g>
    <defs>
      <clipPath id="clip0_322_4800">
        <rect width="17.5" height="14" fill="white" />
      </clipPath>
      <clipPath id="clip1_322_4800">
        <path d="M0 0H17.5V14H0V0Z" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const LocationIcon = ({
  className = 'h-4 w-4',
  ...props
}: React.SVGProps<SVGSVGElement>) => (
  <svg
    width="11"
    height="14"
    viewBox="0 0 11 14"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
    {...props}
  >
    <g clipPath="url(#clip0_322_4805)">
      <g clipPath="url(#clip1_322_4805)">
        <path
          d="M5.89805 13.65C7.30078 11.8945 10.5 7.63984 10.5 5.25C10.5 2.35156 8.14844 0 5.25 0C2.35156 0 0 2.35156 0 5.25C0 7.63984 3.19922 11.8945 4.60195 13.65C4.93828 14.0684 5.56172 14.0684 5.89805 13.65ZM5.25 3.5C5.71413 3.5 6.15925 3.68437 6.48744 4.01256C6.81563 4.34075 7 4.78587 7 5.25C7 5.71413 6.81563 6.15925 6.48744 6.48744C6.15925 6.81563 5.71413 7 5.25 7C4.78587 7 4.34075 6.81563 4.01256 6.48744C3.68437 6.15925 3.5 5.71413 3.5 5.25C3.5 4.78587 3.68437 4.34075 4.01256 4.01256C4.34075 3.68437 4.78587 3.5 5.25 3.5Z"
          fill={props.color ?? '#A3A3A3'}
        />
      </g>
    </g>
    <defs>
      <clipPath id="clip0_322_4805">
        <rect width="10.5" height="14" fill="white" />
      </clipPath>
      <clipPath id="clip1_322_4805">
        <path d="M0 0H10.5V14H0V0Z" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const DistanceIcon = ({ ...props }: React.SVGProps<SVGSVGElement>) => (
  <svg
    width="14"
    height="20"
    viewBox="0 0 14 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M7 20C5.23333 20 3.79167 19.7208 2.675 19.1625C1.55833 18.6042 1 17.8833 1 17C1 16.6 1.12083 16.2292 1.3625 15.8875C1.60417 15.5458 1.94167 15.25 2.375 15L3.95 16.475C3.8 16.5417 3.6375 16.6167 3.4625 16.7C3.2875 16.7833 3.15 16.8833 3.05 17C3.26667 17.2667 3.76667 17.5 4.55 17.7C5.33333 17.9 6.15 18 7 18C7.85 18 8.67083 17.9 9.4625 17.7C10.2542 17.5 10.7583 17.2667 10.975 17C10.8583 16.8667 10.7083 16.7583 10.525 16.675C10.3417 16.5917 10.1667 16.5167 10 16.45L11.55 14.95C12.0167 15.2167 12.375 15.5208 12.625 15.8625C12.875 16.2042 13 16.5833 13 17C13 17.8833 12.4417 18.6042 11.325 19.1625C10.2083 19.7208 8.76667 20 7 20ZM7.025 14.5C8.675 13.2833 9.91667 12.0625 10.75 10.8375C11.5833 9.6125 12 8.38333 12 7.15C12 5.45 11.4583 4.16667 10.375 3.3C9.29167 2.43333 8.16667 2 7 2C5.83333 2 4.70833 2.43333 3.625 3.3C2.54167 4.16667 2 5.45 2 7.15C2 8.26667 2.40833 9.42917 3.225 10.6375C4.04167 11.8458 5.30833 13.1333 7.025 14.5ZM7 17C4.65 15.2667 2.89583 13.5833 1.7375 11.95C0.579167 10.3167 0 8.71667 0 7.15C0 5.96667 0.2125 4.92917 0.6375 4.0375C1.0625 3.14583 1.60833 2.4 2.275 1.8C2.94167 1.2 3.69167 0.75 4.525 0.45C5.35833 0.15 6.18333 0 7 0C7.81667 0 8.64167 0.15 9.475 0.45C10.3083 0.75 11.0583 1.2 11.725 1.8C12.3917 2.4 12.9375 3.14583 13.3625 4.0375C13.7875 4.92917 14 5.96667 14 7.15C14 8.71667 13.4208 10.3167 12.2625 11.95C11.1042 13.5833 9.35 15.2667 7 17ZM7 9C7.55 9 8.02083 8.80417 8.4125 8.4125C8.80417 8.02083 9 7.55 9 7C9 6.45 8.80417 5.97917 8.4125 5.5875C8.02083 5.19583 7.55 5 7 5C6.45 5 5.97917 5.19583 5.5875 5.5875C5.19583 5.97917 5 6.45 5 7C5 7.55 5.19583 8.02083 5.5875 8.4125C5.97917 8.80417 6.45 9 7 9Z"
      fill="#64748B"
    />
  </svg>
);

export const TagIcon = ({
  className = 'h-4 w-4',
  ...props
}: React.SVGProps<SVGSVGElement>) => (
  <svg
    width="16"
    height="17"
    viewBox="0 0 16 17"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
    {...props}
  >
    <mask
      id="mask0_345_6563"
      style={{ maskType: 'alpha' }}
      maskUnits="userSpaceOnUse"
      x="0"
      y="0"
      width="16"
      height="17"
    >
      <rect y="0.5" width="16" height="16" fill="#D9D9D9" />
    </mask>
    <g mask="url(#mask0_345_6563)">
      <path
        d="M14.2667 10.0007L9.5 14.7673C9.36667 14.9007 9.21667 15.0007 9.05 15.0673C8.88333 15.134 8.71667 15.1673 8.55 15.1673C8.38333 15.1673 8.21667 15.134 8.05 15.0673C7.88333 15.0007 7.73333 14.9007 7.6 14.7673L1.71667 8.88398C1.59445 8.76176 1.5 8.6201 1.43333 8.45898C1.36667 8.29787 1.33333 8.12843 1.33333 7.95065V3.16732C1.33333 2.80065 1.46389 2.48676 1.725 2.22565C1.98611 1.96454 2.3 1.83398 2.66667 1.83398H7.45C7.62778 1.83398 7.8 1.8701 7.96667 1.94232C8.13333 2.01454 8.27778 2.11176 8.4 2.23398L14.2667 8.11732C14.4 8.25065 14.4972 8.40065 14.5583 8.56732C14.6194 8.73398 14.65 8.90065 14.65 9.06732C14.65 9.23398 14.6194 9.39787 14.5583 9.55898C14.4972 9.7201 14.4 9.86732 14.2667 10.0007ZM8.55 13.834L13.3167 9.06732L7.43333 3.16732H2.66667V7.93398L8.55 13.834ZM4.33333 5.83398C4.61111 5.83398 4.84722 5.73676 5.04167 5.54232C5.23611 5.34787 5.33333 5.11176 5.33333 4.83398C5.33333 4.55621 5.23611 4.3201 5.04167 4.12565C4.84722 3.93121 4.61111 3.83398 4.33333 3.83398C4.05556 3.83398 3.81944 3.93121 3.625 4.12565C3.43056 4.3201 3.33333 4.55621 3.33333 4.83398C3.33333 5.11176 3.43056 5.34787 3.625 5.54232C3.81944 5.73676 4.05556 5.83398 4.33333 5.83398Z"
        fill={props.color ?? '#A3A3A3'}
      />
    </g>
  </svg>
);

export const CheckCircleIcon = ({
  className = 'h-4 w-4',
  ...props
}: React.SVGProps<SVGSVGElement>) => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
    {...props}
  >
    <path
      d="M8.6 14.6L15.65 7.55L14.25 6.15L8.6 11.8L5.75 8.95L4.35 10.35L8.6 14.6ZM10 20C8.61667 20 7.31667 19.7375 6.1 19.2125C4.88333 18.6875 3.825 17.975 2.925 17.075C2.025 16.175 1.3125 15.1167 0.7875 13.9C0.2625 12.6833 0 11.3833 0 10C0 8.61667 0.2625 7.31667 0.7875 6.1C1.3125 4.88333 2.025 3.825 2.925 2.925C3.825 2.025 4.88333 1.3125 6.1 0.7875C7.31667 0.2625 8.61667 0 10 0C11.3833 0 12.6833 0.2625 13.9 0.7875C15.1167 1.3125 16.175 2.025 17.075 2.925C17.975 3.825 18.6875 4.88333 19.2125 6.1C19.7375 7.31667 20 8.61667 20 10C20 11.3833 19.7375 12.6833 19.2125 13.9C18.6875 15.1167 17.975 16.175 17.075 17.075C16.175 17.975 15.1167 18.6875 13.9 19.2125C12.6833 19.7375 11.3833 20 10 20ZM10 18C12.2333 18 14.125 17.225 15.675 15.675C17.225 14.125 18 12.2333 18 10C18 7.76667 17.225 5.875 15.675 4.325C14.125 2.775 12.2333 2 10 2C7.76667 2 5.875 2.775 4.325 4.325C2.775 5.875 2 7.76667 2 10C2 12.2333 2.775 14.125 4.325 15.675C5.875 17.225 7.76667 18 10 18Z"
      fill="#1C1B1F"
    />
  </svg>
);

export const CheckOutlinedIcon = ({
  className = 'h-4 w-4',
  ...props
}: React.SVGProps<SVGSVGElement>) => (
  <svg
    width="17"
    height="16"
    viewBox="0 0 17 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
    {...props}
  >
    <g clipPath="url(#clip0_463_2660)">
      <path
        d="M8.51562 16C10.6374 16 12.6722 15.1571 14.1725 13.6569C15.6728 12.1566 16.5156 10.1217 16.5156 8C16.5156 5.87827 15.6728 3.84344 14.1725 2.34315C12.6722 0.842855 10.6374 0 8.51562 0C6.39389 0 4.35906 0.842855 2.85877 2.34315C1.35848 3.84344 0.515625 5.87827 0.515625 8C0.515625 10.1217 1.35848 12.1566 2.85877 13.6569C4.35906 15.1571 6.39389 16 8.51562 16ZM12.0469 6.53125L8.04688 10.5312C7.75313 10.825 7.27813 10.825 6.9875 10.5312L4.9875 8.53125C4.69375 8.2375 4.69375 7.7625 4.9875 7.47188C5.28125 7.18125 5.75625 7.17813 6.04688 7.47188L7.51562 8.94063L10.9844 5.46875C11.2781 5.175 11.7531 5.175 12.0437 5.46875C12.3344 5.7625 12.3375 6.2375 12.0437 6.52812L12.0469 6.53125Z"
        fill="white"
      />
    </g>
    <defs>
      <clipPath id="clip0_463_2660">
        <path d="M0.515625 0H16.5156V16H0.515625V0Z" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const EditIcon = ({
  className = 'h-4 w-4',
  ...props
}: React.SVGProps<SVGSVGElement>) => (
  <svg
    width="17"
    height="16"
    viewBox="0 0 17 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
    {...props}
  >
    <g clipPath="url(#clip0_463_3501)">
      <path
        d="M15.0656 0.677344C14.3813 -0.00703126 13.275 -0.00703126 12.5906 0.677344L11.65 1.61484L14.7094 4.67422L15.65 3.73359C16.3344 3.04922 16.3344 1.94297 15.65 1.25859L15.0656 0.677344ZM5.71562 7.55234C5.525 7.74297 5.37812 7.97734 5.29375 8.23672L4.36875 11.0117C4.27813 11.2805 4.35 11.5773 4.55 11.7805C4.75 11.9836 5.04688 12.0523 5.31875 11.9617L8.09375 11.0367C8.35 10.9523 8.58437 10.8055 8.77812 10.6148L14.0063 5.38359L10.9438 2.32109L5.71562 7.55234ZM3.32812 1.99922C1.67188 1.99922 0.328125 3.34297 0.328125 4.99922V12.9992C0.328125 14.6555 1.67188 15.9992 3.32812 15.9992H11.3281C12.9844 15.9992 14.3281 14.6555 14.3281 12.9992V9.99922C14.3281 9.44609 13.8813 8.99922 13.3281 8.99922C12.775 8.99922 12.3281 9.44609 12.3281 9.99922V12.9992C12.3281 13.5523 11.8813 13.9992 11.3281 13.9992H3.32812C2.775 13.9992 2.32812 13.5523 2.32812 12.9992V4.99922C2.32812 4.44609 2.775 3.99922 3.32812 3.99922H6.32812C6.88125 3.99922 7.32812 3.55234 7.32812 2.99922C7.32812 2.44609 6.88125 1.99922 6.32812 1.99922H3.32812Z"
        fill="#525252"
      />
    </g>
    <defs>
      <clipPath id="clip0_463_3501">
        <path d="M0.328125 0H16.3281V16H0.328125V0Z" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const PencilIcon = ({ ...props }: React.SVGProps<SVGSVGElement>) => (
  <svg
    width="18"
    height="18"
    viewBox="0 0 18 18"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M2 16H3.425L13.2 6.225L11.775 4.8L2 14.575V16ZM0 18V13.75L13.2 0.575C13.4 0.391667 13.6208 0.25 13.8625 0.15C14.1042 0.05 14.3583 0 14.625 0C14.8917 0 15.15 0.05 15.4 0.15C15.65 0.25 15.8667 0.4 16.05 0.6L17.425 2C17.625 2.18333 17.7708 2.4 17.8625 2.65C17.9542 2.9 18 3.15 18 3.4C18 3.66667 17.9542 3.92083 17.8625 4.1625C17.7708 4.40417 17.625 4.625 17.425 4.825L4.25 18H0ZM12.475 5.525L11.775 4.8L13.2 6.225L12.475 5.525Z"
      fill="#1C1B1F"
    />
  </svg>
);

export const DeleteIcon = ({ ...props }: React.SVGProps<SVGSVGElement>) => (
  <svg
    width="15"
    height="16"
    viewBox="0 0 15 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <g clipPath="url(#clip0_463_3506)">
      <path
        d="M4.55312 0.553125L4.32812 1H1.32812C0.775 1 0.328125 1.44687 0.328125 2C0.328125 2.55312 0.775 3 1.32812 3H13.3281C13.8813 3 14.3281 2.55312 14.3281 2C14.3281 1.44687 13.8813 1 13.3281 1H10.3281L10.1031 0.553125C9.93437 0.2125 9.5875 0 9.20938 0H5.44688C5.06875 0 4.72187 0.2125 4.55312 0.553125ZM13.3281 4H1.32812L1.99063 14.5938C2.04062 15.3844 2.69688 16 3.4875 16H11.1687C11.9594 16 12.6156 15.3844 12.6656 14.5938L13.3281 4Z"
        fill="#525252"
      />
    </g>
    <defs>
      <clipPath id="clip0_463_3506">
        <path d="M0.328125 0H14.3281V16H0.328125V0Z" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const TrashIcon = ({ ...props }: React.SVGProps<SVGSVGElement>) => (
  <svg
    width="16"
    height="18"
    viewBox="0 0 16 18"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M3 18C2.45 18 1.97917 17.8042 1.5875 17.4125C1.19583 17.0208 1 16.55 1 16V3H0V1H5V0H11V1H16V3H15V16C15 16.55 14.8042 17.0208 14.4125 17.4125C14.0208 17.8042 13.55 18 13 18H3ZM13 3H3V16H13V3ZM5 14H7V5H5V14ZM9 14H11V5H9V14Z"
      fill="#1C1B1F"
    />
  </svg>
);

export const ArrowRightIcon = ({
  className = 'h-4 w-4',
  ...props
}: React.SVGProps<SVGSVGElement>) => (
  <svg
    width="13"
    height="14"
    viewBox="0 0 13 14"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
    {...props}
  >
    <g clipPath="url(#clip0_572_2014)">
      <path
        d="M12.4305 7.61807C12.7723 7.27627 12.7723 6.72119 12.4305 6.3794L8.05547 2.00439C7.71367 1.6626 7.15859 1.6626 6.8168 2.00439C6.475 2.34619 6.475 2.90127 6.8168 3.24307L9.70156 6.1251H1.3125C0.828516 6.1251 0.4375 6.51611 0.4375 7.0001C0.4375 7.48408 0.828516 7.8751 1.3125 7.8751H9.69883L6.81953 10.7571C6.47773 11.0989 6.47773 11.654 6.81953 11.9958C7.16133 12.3376 7.71641 12.3376 8.0582 11.9958L12.4332 7.6208L12.4305 7.61807Z"
        fill={props.color ?? '#171717'}
      />
    </g>
    <defs>
      <clipPath id="clip0_572_2014">
        <path d="M0.4375 0H12.6875V14H0.4375V0Z" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const CalendarIcon = ({ ...props }: React.SVGProps<SVGSVGElement>) => (
  <svg
    width="18"
    height="21"
    viewBox="0 0 18 21"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M2 20.75C1.45 20.75 0.979167 20.5493 0.5875 20.1478C0.195833 19.7464 0 19.2638 0 18.7V4.35C0 3.78625 0.195833 3.30365 0.5875 2.90219C0.979167 2.50073 1.45 2.3 2 2.3H3V0.25H5V2.3H13V0.25H15V2.3H16C16.55 2.3 17.0208 2.50073 17.4125 2.90219C17.8042 3.30365 18 3.78625 18 4.35V18.7C18 19.2638 17.8042 19.7464 17.4125 20.1478C17.0208 20.5493 16.55 20.75 16 20.75H2ZM2 18.7H16V8.45H2V18.7ZM2 6.4H16V4.35H2V6.4ZM9 12.55C8.71667 12.55 8.47917 12.4518 8.2875 12.2553C8.09583 12.0589 8 11.8154 8 11.525C8 11.2346 8.09583 10.9911 8.2875 10.7947C8.47917 10.5982 8.71667 10.5 9 10.5C9.28333 10.5 9.52083 10.5982 9.7125 10.7947C9.90417 10.9911 10 11.2346 10 11.525C10 11.8154 9.90417 12.0589 9.7125 12.2553C9.52083 12.4518 9.28333 12.55 9 12.55ZM5 12.55C4.71667 12.55 4.47917 12.4518 4.2875 12.2553C4.09583 12.0589 4 11.8154 4 11.525C4 11.2346 4.09583 10.9911 4.2875 10.7947C4.47917 10.5982 4.71667 10.5 5 10.5C5.28333 10.5 5.52083 10.5982 5.7125 10.7947C5.90417 10.9911 6 11.2346 6 11.525C6 11.8154 5.90417 12.0589 5.7125 12.2553C5.52083 12.4518 5.28333 12.55 5 12.55ZM13 12.55C12.7167 12.55 12.4792 12.4518 12.2875 12.2553C12.0958 12.0589 12 11.8154 12 11.525C12 11.2346 12.0958 10.9911 12.2875 10.7947C12.4792 10.5982 12.7167 10.5 13 10.5C13.2833 10.5 13.5208 10.5982 13.7125 10.7947C13.9042 10.9911 14 11.2346 14 11.525C14 11.8154 13.9042 12.0589 13.7125 12.2553C13.5208 12.4518 13.2833 12.55 13 12.55ZM9 16.65C8.71667 16.65 8.47917 16.5518 8.2875 16.3553C8.09583 16.1589 8 15.9154 8 15.625C8 15.3346 8.09583 15.0911 8.2875 14.8947C8.47917 14.6982 8.71667 14.6 9 14.6C9.28333 14.6 9.52083 14.6982 9.7125 14.8947C9.90417 15.0911 10 15.3346 10 15.625C10 15.9154 9.90417 16.1589 9.7125 16.3553C9.52083 16.5518 9.28333 16.65 9 16.65ZM5 16.65C4.71667 16.65 4.47917 16.5518 4.2875 16.3553C4.09583 16.1589 4 15.9154 4 15.625C4 15.3346 4.09583 15.0911 4.2875 14.8947C4.47917 14.6982 4.71667 14.6 5 14.6C5.28333 14.6 5.52083 14.6982 5.7125 14.8947C5.90417 15.0911 6 15.3346 6 15.625C6 15.9154 5.90417 16.1589 5.7125 16.3553C5.52083 16.5518 5.28333 16.65 5 16.65ZM13 16.65C12.7167 16.65 12.4792 16.5518 12.2875 16.3553C12.0958 16.1589 12 15.9154 12 15.625C12 15.3346 12.0958 15.0911 12.2875 14.8947C12.4792 14.6982 12.7167 14.6 13 14.6C13.2833 14.6 13.5208 14.6982 13.7125 14.8947C13.9042 15.0911 14 15.3346 14 15.625C14 15.9154 13.9042 16.1589 13.7125 16.3553C13.5208 16.5518 13.2833 16.65 13 16.65Z"
      fill="#1C1B1F"
    />
  </svg>
);

export const CommentIcon = ({ ...props }: React.SVGProps<SVGSVGElement>) => (
  <svg
    width="12"
    height="12"
    viewBox="0 0 12 12"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <g clipPath="url(#clip0_463_6541)">
      <path
        d="M12.0007 5.625C12.0007 8.31797 9.31476 10.5 6.0007 10.5C5.13116 10.5 4.30616 10.35 3.56085 10.0805C3.28194 10.2844 2.82726 10.5633 2.2882 10.7977C1.7257 11.0414 1.04835 11.25 0.375695 11.25C0.223351 11.25 0.0874138 11.1586 0.02882 11.018C-0.0297737 10.8773 0.00303879 10.718 0.108508 10.6102L0.115539 10.6031C0.12257 10.5961 0.131945 10.5867 0.146008 10.5703C0.171789 10.5422 0.211633 10.4977 0.260851 10.4367C0.356945 10.3195 0.485851 10.1461 0.617101 9.93047C0.851476 9.54141 1.07413 9.03047 1.11866 8.45625C0.415539 7.65937 0.000695041 6.68203 0.000695041 5.625C0.000695041 2.93203 2.68663 0.75 6.0007 0.75C9.31476 0.75 12.0007 2.93203 12.0007 5.625Z"
        fill="white"
      />
    </g>
    <defs>
      <clipPath id="clip0_463_6541">
        <path d="M0 0H12V12H0V0Z" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const PlayArrowIcon = ({ ...props }: React.SVGProps<SVGSVGElement>) => (
  <svg
    width="11"
    height="14"
    viewBox="0 0 11 14"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M0 14V0L11 7L0 14Z" fill="#1C1B1F" />
  </svg>
);

export const ShareIcon = ({ ...props }: React.SVGProps<SVGSVGElement>) => (
  <svg
    width="14"
    height="13"
    viewBox="0 0 14 13"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M8.39453 0.201137C8.08008 0.34059 7.875 0.655043 7.875 0.999574V2.74957H4.8125C2.15469 2.74957 0 4.90426 0 7.56207C0 10.6601 2.22852 12.0437 2.73984 12.3226C2.8082 12.3609 2.88477 12.3746 2.96133 12.3746C3.25938 12.3746 3.5 12.1312 3.5 11.8359C3.5 11.6308 3.38242 11.4422 3.23203 11.3027C2.975 11.0593 2.625 10.5808 2.625 9.74957C2.625 8.30036 3.80078 7.12457 5.25 7.12457H7.875V8.87457C7.875 9.21911 8.07734 9.53356 8.39453 9.67301C8.71172 9.81247 9.07812 9.75504 9.33516 9.52536L13.7102 5.58786C13.8934 5.42106 14 5.1859 14 4.93707C14 4.68825 13.8961 4.45309 13.7102 4.28629L9.33516 0.348793C9.07812 0.116371 8.70898 0.0589493 8.39453 0.201137Z"
      fill="white"
    />
  </svg>
);

export const SearchIcon = ({ ...props }: React.SVGProps<SVGSVGElement>) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M13 6.5C13 7.93437 12.5344 9.25938 11.75 10.3344L15.7063 14.2937C16.0969 14.6844 16.0969 15.3188 15.7063 15.7094C15.3156 16.1 14.6812 16.1 14.2906 15.7094L10.3344 11.75C9.25938 12.5375 7.93437 13 6.5 13C2.90937 13 0 10.0906 0 6.5C0 2.90937 2.90937 0 6.5 0C10.0906 0 13 2.90937 13 6.5ZM6.5 11C7.09095 11 7.67611 10.8836 8.22208 10.6575C8.76804 10.4313 9.26412 10.0998 9.68198 9.68198C10.0998 9.26412 10.4313 8.76804 10.6575 8.22208C10.8836 7.67611 11 7.09095 11 6.5C11 5.90905 10.8836 5.32389 10.6575 4.77792C10.4313 4.23196 10.0998 3.73588 9.68198 3.31802C9.26412 2.90016 8.76804 2.56869 8.22208 2.34254C7.67611 2.1164 7.09095 2 6.5 2C5.90905 2 5.32389 2.1164 4.77792 2.34254C4.23196 2.56869 3.73588 2.90016 3.31802 3.31802C2.90016 3.73588 2.56869 4.23196 2.34254 4.77792C2.1164 5.32389 2 5.90905 2 6.5C2 7.09095 2.1164 7.67611 2.34254 8.22208C2.56869 8.76804 2.90016 9.26412 3.31802 9.68198C3.73588 10.0998 4.23196 10.4313 4.77792 10.6575C5.32389 10.8836 5.90905 11 6.5 11Z"
      fill="#A3A3A3"
    />
  </svg>
);

export const CircleXOutlineIcon = ({
  className = 'h-4 w-4',
  ...props
}: React.SVGProps<SVGSVGElement>) => (
  <svg
    width="14"
    height="14"
    viewBox="0 0 14 14"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
    {...props}
  >
    <g clipPath="url(#clip0_571_1303)">
      <path
        d="M7 14C8.85652 14 10.637 13.2625 11.9497 11.9497C13.2625 10.637 14 8.85652 14 7C14 5.14348 13.2625 3.36301 11.9497 2.05025C10.637 0.737498 8.85652 0 7 0C5.14348 0 3.36301 0.737498 2.05025 2.05025C0.737498 3.36301 0 5.14348 0 7C0 8.85652 0.737498 10.637 2.05025 11.9497C3.36301 13.2625 5.14348 14 7 14ZM4.78516 4.78516C5.04219 4.52812 5.45781 4.52812 5.71211 4.78516L6.99727 6.07031L8.28242 4.78516C8.53945 4.52812 8.95508 4.52812 9.20937 4.78516C9.46367 5.04219 9.46641 5.45781 9.20937 5.71211L7.92422 6.99727L9.20937 8.28242C9.46641 8.53945 9.46641 8.95508 9.20937 9.20937C8.95234 9.46367 8.53672 9.46641 8.28242 9.20937L6.99727 7.92422L5.71211 9.20937C5.45508 9.46641 5.03945 9.46641 4.78516 9.20937C4.53086 8.95234 4.52812 8.53672 4.78516 8.28242L6.07031 6.99727L4.78516 5.71211C4.52812 5.45508 4.52812 5.03945 4.78516 4.78516Z"
        fill="currentColor"
      />
    </g>
    <defs>
      <clipPath id="clip0_571_1303">
        <path d="M0 0H14V14H0V0Z" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const InputFileIcon = ({ ...props }: React.SVGProps<SVGSVGElement>) => (
  <svg
    width="30"
    height="22"
    viewBox="0 0 30 22"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M6.75 21.5C3.02344 21.5 0 18.4766 0 14.75C0 11.8062 1.88437 9.30313 4.50937 8.37969C4.50469 8.25313 4.5 8.12656 4.5 8C4.5 3.85625 7.85625 0.5 12 0.5C14.7797 0.5 17.2031 2.00937 18.5016 4.25937C19.2141 3.78125 20.0766 3.5 21 3.5C23.4844 3.5 25.5 5.51562 25.5 8C25.5 8.57187 25.3922 9.11563 25.2 9.62188C27.9375 10.175 30 12.5984 30 15.5C30 18.8141 27.3141 21.5 24 21.5H6.75ZM10.4531 11.3281C10.0125 11.7688 10.0125 12.4812 10.4531 12.9172C10.8938 13.3531 11.6062 13.3578 12.0422 12.9172L13.8703 11.0891V17.375C13.8703 17.9984 14.3719 18.5 14.9953 18.5C15.6187 18.5 16.1203 17.9984 16.1203 17.375V11.0891L17.9484 12.9172C18.3891 13.3578 19.1016 13.3578 19.5375 12.9172C19.9734 12.4766 19.9781 11.7641 19.5375 11.3281L15.7875 7.57812C15.3469 7.1375 14.6344 7.1375 14.1984 7.57812L10.4484 11.3281H10.4531Z"
      fill="#737373"
    />
  </svg>
);

export const MoreHorizIcon = ({ ...props }: React.SVGProps<SVGSVGElement>) => (
  <svg
    width="16"
    height="4"
    viewBox="0 0 16 4"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M2 4C1.45 4 0.979167 3.80417 0.5875 3.4125C0.195833 3.02083 0 2.55 0 2C0 1.45 0.195833 0.979167 0.5875 0.5875C0.979167 0.195833 1.45 0 2 0C2.55 0 3.02083 0.195833 3.4125 0.5875C3.80417 0.979167 4 1.45 4 2C4 2.55 3.80417 3.02083 3.4125 3.4125C3.02083 3.80417 2.55 4 2 4ZM8 4C7.45 4 6.97917 3.80417 6.5875 3.4125C6.19583 3.02083 6 2.55 6 2C6 1.45 6.19583 0.979167 6.5875 0.5875C6.97917 0.195833 7.45 0 8 0C8.55 0 9.02083 0.195833 9.4125 0.5875C9.80417 0.979167 10 1.45 10 2C10 2.55 9.80417 3.02083 9.4125 3.4125C9.02083 3.80417 8.55 4 8 4ZM14 4C13.45 4 12.9792 3.80417 12.5875 3.4125C12.1958 3.02083 12 2.55 12 2C12 1.45 12.1958 0.979167 12.5875 0.5875C12.9792 0.195833 13.45 0 14 0C14.55 0 15.0208 0.195833 15.4125 0.5875C15.8042 0.979167 16 1.45 16 2C16 2.55 15.8042 3.02083 15.4125 3.4125C15.0208 3.80417 14.55 4 14 4Z"
      fill="white"
    />
  </svg>
);

export const LocationMarkerIcon = ({
  className = 'h-4 w-4',
  ...props
}: React.SVGProps<SVGSVGElement>) => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
    {...props}
  >
    <mask
      id="mask0_463_7749"
      style={{ maskType: 'alpha' }}
      maskUnits="userSpaceOnUse"
      x="0"
      y="0"
      width="24"
      height="24"
    >
      <rect width="24" height="24" fill="#D9D9D9" />
    </mask>
    <g mask="url(#mask0_463_7749)">
      <path
        d="M12 22C10.2333 22 8.79167 21.7208 7.675 21.1625C6.55833 20.6042 6 19.8833 6 19C6 18.6 6.12083 18.2292 6.3625 17.8875C6.60417 17.5458 6.94167 17.25 7.375 17L8.95 18.475C8.8 18.5417 8.6375 18.6167 8.4625 18.7C8.2875 18.7833 8.15 18.8833 8.05 19C8.26667 19.2667 8.76667 19.5 9.55 19.7C10.3333 19.9 11.15 20 12 20C12.85 20 13.6708 19.9 14.4625 19.7C15.2542 19.5 15.7583 19.2667 15.975 19C15.8583 18.8667 15.7083 18.7583 15.525 18.675C15.3417 18.5917 15.1667 18.5167 15 18.45L16.55 16.95C17.0167 17.2167 17.375 17.5208 17.625 17.8625C17.875 18.2042 18 18.5833 18 19C18 19.8833 17.4417 20.6042 16.325 21.1625C15.2083 21.7208 13.7667 22 12 22ZM12.025 16.5C13.675 15.2833 14.9167 14.0625 15.75 12.8375C16.5833 11.6125 17 10.3833 17 9.15C17 7.45 16.4583 6.16667 15.375 5.3C14.2917 4.43333 13.1667 4 12 4C10.8333 4 9.70833 4.43333 8.625 5.3C7.54167 6.16667 7 7.45 7 9.15C7 10.2667 7.40833 11.4292 8.225 12.6375C9.04167 13.8458 10.3083 15.1333 12.025 16.5ZM12 19C9.65 17.2667 7.89583 15.5833 6.7375 13.95C5.57917 12.3167 5 10.7167 5 9.15C5 7.96667 5.2125 6.92917 5.6375 6.0375C6.0625 5.14583 6.60833 4.4 7.275 3.8C7.94167 3.2 8.69167 2.75 9.525 2.45C10.3583 2.15 11.1833 2 12 2C12.8167 2 13.6417 2.15 14.475 2.45C15.3083 2.75 16.0583 3.2 16.725 3.8C17.3917 4.4 17.9375 5.14583 18.3625 6.0375C18.7875 6.92917 19 7.96667 19 9.15C19 10.7167 18.4208 12.3167 17.2625 13.95C16.1042 15.5833 14.35 17.2667 12 19ZM12 11C12.55 11 13.0208 10.8042 13.4125 10.4125C13.8042 10.0208 14 9.55 14 9C14 8.45 13.8042 7.97917 13.4125 7.5875C13.0208 7.19583 12.55 7 12 7C11.45 7 10.9792 7.19583 10.5875 7.5875C10.1958 7.97917 10 8.45 10 9C10 9.55 10.1958 10.0208 10.5875 10.4125C10.9792 10.8042 11.45 11 12 11Z"
        fill="#1C1B1F"
      />
    </g>
  </svg>
);

export const LikeIcon = ({ ...props }: React.SVGProps<SVGSVGElement>) => (
  <svg
    width="21"
    height="20"
    viewBox="0 0 21 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M16 20H6V7L13 0L14.25 1.25C14.3667 1.36667 14.4625 1.525 14.5375 1.725C14.6125 1.925 14.65 2.11667 14.65 2.3V2.65L13.55 7H19C19.5333 7 20 7.2 20.4 7.6C20.8 8 21 8.46667 21 9V11C21 11.1167 20.9875 11.2417 20.9625 11.375C20.9375 11.5083 20.9 11.6333 20.85 11.75L17.85 18.8C17.7 19.1333 17.45 19.4167 17.1 19.65C16.75 19.8833 16.3833 20 16 20ZM4 7V20H0V7H4Z"
      fill={props.color ?? 'white'}
    />
  </svg>
);

export const DislikeIcon = ({ ...props }: React.SVGProps<SVGSVGElement>) => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <mask
      id="mask0_467_16845"
      style={{ maskType: 'alpha' }}
      maskUnits="userSpaceOnUse"
      x="0"
      y="0"
      width="24"
      height="24"
    >
      <rect width="24" height="24" fill={props.color ?? '#171717'} />
    </mask>
    <g mask="url(#mask0_467_16845)">
      <path
        d="M6 3H16V16L9 23L7.75 21.75C7.63333 21.6333 7.5375 21.475 7.4625 21.275C7.3875 21.075 7.35 20.8833 7.35 20.7V20.35L8.45 16H3C2.46667 16 2 15.8 1.6 15.4C1.2 15 1 14.5333 1 14V12C1 11.8833 1.0125 11.7583 1.0375 11.625C1.0625 11.4917 1.1 11.3667 1.15 11.25L4.15 4.2C4.3 3.86667 4.55 3.58333 4.9 3.35C5.25 3.11667 5.61667 3 6 3ZM18 16V3H22V16H18Z"
        fill={props.color ?? '#171717'}
      />
    </g>
  </svg>
);

// location offers icons
export const ParkingIcon = ({ ...props }: React.SVGProps<SVGSVGElement>) => (
  <svg
    width="16"
    height="14"
    viewBox="0 0 16 14"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M4.225 2.66875L3.40937 5H12.5906L11.775 2.66875C11.6344 2.26875 11.2563 2 10.8313 2H5.16875C4.74375 2 4.36562 2.26875 4.225 2.66875ZM1.2375 5.15L2.3375 2.00938C2.75938 0.80625 3.89375 0 5.16875 0H10.8313C12.1062 0 13.2406 0.80625 13.6625 2.00938L14.7625 5.15C15.4875 5.45 16 6.16563 16 7V11.5V13C16 13.5531 15.5531 14 15 14H14C13.4469 14 13 13.5531 13 13V11.5H3V13C3 13.5531 2.55312 14 2 14H1C0.446875 14 0 13.5531 0 13V11.5V7C0 6.16563 0.5125 5.45 1.2375 5.15ZM4 8C4 7.73478 3.89464 7.48043 3.70711 7.29289C3.51957 7.10536 3.26522 7 3 7C2.73478 7 2.48043 7.10536 2.29289 7.29289C2.10536 7.48043 2 7.73478 2 8C2 8.26522 2.10536 8.51957 2.29289 8.70711C2.48043 8.89464 2.73478 9 3 9C3.26522 9 3.51957 8.89464 3.70711 8.70711C3.89464 8.51957 4 8.26522 4 8ZM13 9C13.2652 9 13.5196 8.89464 13.7071 8.70711C13.8946 8.51957 14 8.26522 14 8C14 7.73478 13.8946 7.48043 13.7071 7.29289C13.5196 7.10536 13.2652 7 13 7C12.7348 7 12.4804 7.10536 12.2929 7.29289C12.1054 7.48043 12 7.73478 12 8C12 8.26522 12.1054 8.51957 12.2929 8.70711C12.4804 8.89464 12.7348 9 13 9Z"
      fill="#525252"
    />
  </svg>
);

export const RooftopAccessIcon = ({
  ...props
}: React.SVGProps<SVGSVGElement>) => (
  <svg
    width="12"
    height="16"
    viewBox="0 0 12 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <g clip-path="url(#clip0_467_14547)">
      <path
        d="M1.5 0C0.671875 0 0 0.671875 0 1.5V14.5C0 15.3281 0.671875 16 1.5 16H4.5V13.5C4.5 12.6719 5.17188 12 6 12C6.82812 12 7.5 12.6719 7.5 13.5V16H10.5C11.3281 16 12 15.3281 12 14.5V1.5C12 0.671875 11.3281 0 10.5 0H1.5ZM2 7.5C2 7.225 2.225 7 2.5 7H3.5C3.775 7 4 7.225 4 7.5V8.5C4 8.775 3.775 9 3.5 9H2.5C2.225 9 2 8.775 2 8.5V7.5ZM5.5 7H6.5C6.775 7 7 7.225 7 7.5V8.5C7 8.775 6.775 9 6.5 9H5.5C5.225 9 5 8.775 5 8.5V7.5C5 7.225 5.225 7 5.5 7ZM8 7.5C8 7.225 8.225 7 8.5 7H9.5C9.775 7 10 7.225 10 7.5V8.5C10 8.775 9.775 9 9.5 9H8.5C8.225 9 8 8.775 8 8.5V7.5ZM2.5 3H3.5C3.775 3 4 3.225 4 3.5V4.5C4 4.775 3.775 5 3.5 5H2.5C2.225 5 2 4.775 2 4.5V3.5C2 3.225 2.225 3 2.5 3ZM5 3.5C5 3.225 5.225 3 5.5 3H6.5C6.775 3 7 3.225 7 3.5V4.5C7 4.775 6.775 5 6.5 5H5.5C5.225 5 5 4.775 5 4.5V3.5ZM8.5 3H9.5C9.775 3 10 3.225 10 3.5V4.5C10 4.775 9.775 5 9.5 5H8.5C8.225 5 8 4.775 8 4.5V3.5C8 3.225 8.225 3 8.5 3Z"
        fill="#525252"
      />
    </g>
    <defs>
      <clipPath id="clip0_467_14547">
        <path d="M0 0H12V16H0V0Z" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const WifiIcon = ({ ...props }: React.SVGProps<SVGSVGElement>) => (
  <svg
    width="20"
    height="14"
    viewBox="0 0 20 14"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M1.69356 5.34062C3.84981 3.27187 6.77481 2 9.99981 2C13.2248 2 16.1498 3.27187 18.3061 5.34062C18.7061 5.72187 19.3373 5.70937 19.7186 5.3125C20.0998 4.91563 20.0873 4.28125 19.6904 3.9C17.1779 1.48438 13.7623 0 9.99981 0C6.23731 0 2.82168 1.48438 0.306058 3.89687C-0.0908168 4.28125 -0.103317 4.9125 0.277933 5.3125C0.659183 5.7125 1.29356 5.725 1.69043 5.34062H1.69356ZM9.99981 7C11.7748 7 13.3936 7.65938 14.6311 8.75C15.0467 9.11563 15.6779 9.075 16.0436 8.6625C16.4092 8.25 16.3686 7.61562 15.9561 7.25C14.3686 5.85 12.2811 5 9.99981 5C7.71856 5 5.63106 5.85 4.04668 7.25C3.63106 7.61562 3.59356 8.24688 3.95918 8.6625C4.32481 9.07812 4.95606 9.11563 5.37168 8.75C6.60606 7.65938 8.22481 7 10.0029 7H9.99981ZM11.9998 12C11.9998 11.4696 11.7891 10.9609 11.414 10.5858C11.0389 10.2107 10.5302 10 9.99981 10C9.46938 10 8.96067 10.2107 8.5856 10.5858C8.21052 10.9609 7.99981 11.4696 7.99981 12C7.99981 12.5304 8.21052 13.0391 8.5856 13.4142C8.96067 13.7893 9.46938 14 9.99981 14C10.5302 14 11.0389 13.7893 11.414 13.4142C11.7891 13.0391 11.9998 12.5304 11.9998 12Z"
      fill="#525252"
    />
  </svg>
);

export const SnowflakeIcon = ({ ...props }: React.SVGProps<SVGSVGElement>) => (
  <svg
    width="14"
    height="16"
    viewBox="0 0 14 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <g clip-path="url(#clip0_467_14571)">
      <path
        d="M7.00162 0C7.55475 0 8.00162 0.446875 8.00162 1V1.94062L8.47037 1.47187C8.76412 1.17812 9.23912 1.17812 9.52975 1.47187C9.82037 1.76562 9.8235 2.24062 9.52975 2.53125L7.9985 4.0625V6.25938L9.91725 5.14062L10.4704 3.075C10.5766 2.675 10.9891 2.4375 11.3891 2.54375C11.7891 2.65 12.0266 3.0625 11.9204 3.4625L11.7579 4.06563L12.4954 3.63438C12.9735 3.35625 13.586 3.51875 13.8641 3.99375C14.1422 4.46875 13.9829 5.08438 13.5047 5.3625L12.7141 5.825L13.3922 6.00625C13.7922 6.1125 14.0297 6.525 13.9235 6.925C13.8172 7.325 13.4047 7.5625 13.0047 7.45625L10.8891 6.89062L8.986 8L10.8891 9.10938L13.0047 8.54375C13.4047 8.4375 13.8172 8.675 13.9235 9.075C14.0297 9.475 13.7922 9.8875 13.3922 9.99375L12.7141 10.175L13.5047 10.6375C13.9829 10.9156 14.1422 11.5281 13.8641 12.0063C13.586 12.4844 12.9735 12.6438 12.4954 12.3656L11.7579 11.9344L11.9204 12.5375C12.0266 12.9375 11.7891 13.35 11.3891 13.4563C10.9891 13.5625 10.5766 13.325 10.4704 12.925L9.91725 10.8594L8.00162 9.74063V11.9375L9.53287 13.4688C9.82662 13.7625 9.82662 14.2375 9.53287 14.5281C9.23912 14.8187 8.76412 14.8219 8.4735 14.5281L8.00475 14.0594V15C8.00475 15.5531 7.55787 16 7.00475 16C6.45162 16 6.00475 15.5531 6.00475 15V14.0594L5.536 14.5281C5.24225 14.8219 4.76725 14.8219 4.47662 14.5281C4.186 14.2344 4.18287 13.7594 4.47662 13.4688L6.00787 11.9375V9.74063L4.08912 10.8594L3.536 12.925C3.42975 13.325 3.01725 13.5625 2.61725 13.4563C2.21725 13.35 1.97975 12.9375 2.086 12.5375L2.2485 11.9344L1.50475 12.3625C1.02662 12.6406 0.414124 12.4781 0.135999 12.0031C-0.142126 11.5281 0.020374 10.9125 0.495374 10.6344L1.286 10.1719L0.607874 9.99063C0.207874 9.88438 -0.029626 9.47187 0.076624 9.07187C0.182874 8.67188 0.595374 8.43437 0.995374 8.54062L3.111 9.10625L5.01725 8L3.11412 6.89062L0.998499 7.45625C0.598499 7.5625 0.185999 7.325 0.079749 6.925C-0.026501 6.525 0.210999 6.1125 0.610999 6.00625L1.28912 5.825L0.498499 5.3625C0.020374 5.08438 -0.139001 4.47188 0.139124 3.99688C0.417249 3.52188 1.02975 3.35938 1.50787 3.6375L2.24537 4.06875L2.08287 3.46563C1.97662 3.06562 2.21412 2.65313 2.61412 2.54688C3.01412 2.44062 3.42662 2.67812 3.53287 3.07812L4.086 5.14375L6.00162 6.25938V4.05937L4.47037 2.53125C4.17662 2.2375 4.17662 1.7625 4.47037 1.47187C4.76412 1.18125 5.23912 1.17812 5.52975 1.47187L5.9985 1.94062V1C5.9985 0.446875 6.44537 0 6.9985 0H7.00162Z"
        fill="#525252"
      />
    </g>
    <defs>
      <clipPath id="clip0_467_14571">
        <path d="M0 0H14V16H0V0Z" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const NaturalLightIcon = ({
  ...props
}: React.SVGProps<SVGSVGElement>) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <g clip-path="url(#clip0_467_14541)">
      <path
        d="M11.298 0.0376804C11.4543 0.103305 11.5668 0.24393 11.598 0.409555L12.2199 3.78143L15.5918 4.40018C15.7574 4.43143 15.898 4.54393 15.9637 4.70018C16.0293 4.85643 16.0105 5.03456 15.9137 5.17518L13.9668 8.00018L15.9137 10.8221C16.0105 10.9627 16.0293 11.1408 15.9637 11.2971C15.898 11.4533 15.7574 11.5658 15.5918 11.5971L12.2199 12.2189L11.598 15.5908C11.5668 15.7564 11.4543 15.8971 11.298 15.9627C11.1418 16.0283 10.9637 16.0096 10.823 15.9127L8.00116 13.9658L5.17928 15.9127C5.03866 16.0096 4.86053 16.0283 4.70428 15.9627C4.54803 15.8971 4.43553 15.7564 4.40428 15.5908L3.78241 12.2189L0.410532 11.5971C0.244907 11.5658 0.104282 11.4533 0.038657 11.2971C-0.026968 11.1408 -0.00821799 10.9627 0.088657 10.8221L2.03553 8.00018L0.088657 5.17831C-0.00821799 5.03768 -0.026968 4.85956 0.038657 4.70331C0.104282 4.54706 0.244907 4.43456 0.410532 4.40331L3.78241 3.78143L4.40428 0.409555C4.43553 0.24393 4.54803 0.103305 4.70428 0.0376804C4.86053 -0.0279445 5.03866 -0.00919455 5.17928 0.0876804L8.00116 2.03456L10.823 0.0876804C10.9637 -0.00919455 11.1418 -0.0279445 11.298 0.0376804ZM5.00116 8.00018C5.00116 7.20453 5.31723 6.44147 5.87984 5.87886C6.44245 5.31625 7.20551 5.00018 8.00116 5.00018C8.79681 5.00018 9.55987 5.31625 10.1225 5.87886C10.6851 6.44147 11.0012 7.20453 11.0012 8.00018C11.0012 8.79583 10.6851 9.55889 10.1225 10.1215C9.55987 10.6841 8.79681 11.0002 8.00116 11.0002C7.20551 11.0002 6.44245 10.6841 5.87984 10.1215C5.31723 9.55889 5.00116 8.79583 5.00116 8.00018ZM12.0012 8.00018C12.0012 6.93931 11.5797 5.9219 10.8296 5.17175C10.0794 4.42161 9.06202 4.00018 8.00116 4.00018C6.94029 4.00018 5.92287 4.42161 5.17273 5.17175C4.42258 5.9219 4.00116 6.93931 4.00116 8.00018C4.00116 9.06105 4.42258 10.0785 5.17273 10.8286C5.92287 11.5788 6.94029 12.0002 8.00116 12.0002C9.06202 12.0002 10.0794 11.5788 10.8296 10.8286C11.5797 10.0785 12.0012 9.06105 12.0012 8.00018Z"
        fill="#525252"
      />
    </g>
    <defs>
      <clipPath id="clip0_467_14541">
        <path d="M0 0H16V16H0V0Z" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const WheelchairIcon = ({ ...props }: React.SVGProps<SVGSVGElement>) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <g clip-path="url(#clip0_467_14553)">
      <path
        d="M6 3C6.39782 3 6.77936 2.84196 7.06066 2.56066C7.34196 2.27936 7.5 1.89782 7.5 1.5C7.5 1.10218 7.34196 0.720644 7.06066 0.43934C6.77936 0.158035 6.39782 0 6 0C5.60218 0 5.22064 0.158035 4.93934 0.43934C4.65804 0.720644 4.5 1.10218 4.5 1.5C4.5 1.89782 4.65804 2.27936 4.93934 2.56066C5.22064 2.84196 5.60218 3 6 3ZM3.76562 7.725C4.15312 7.57812 4.35 7.14687 4.20312 6.75938C4.05625 6.37188 3.625 6.175 3.2375 6.32188C1.34687 7.03438 0 8.85938 0 11C0 13.7625 2.2375 16 5 16C6.9125 16 8.57188 14.9281 9.4125 13.3531C9.60625 12.9875 9.46875 12.5344 9.10312 12.3375C8.7375 12.1406 8.28437 12.2812 8.0875 12.6469C7.5 13.75 6.3375 14.5 5 14.5C3.06562 14.5 1.5 12.9344 1.5 11C1.5 9.50313 2.44062 8.225 3.76562 7.725ZM8.11875 5.5L8.05937 5.19688C7.91875 4.5 7.30938 4 6.59688 4C5.65625 4 4.95 4.85938 5.13438 5.78125L5.85625 9.39062C6.04375 10.325 6.8625 10.9969 7.81875 10.9969H7.97813C7.99062 10.9969 8.00313 10.9969 8.01875 10.9969H10.9594C11.1688 10.9969 11.3531 11.125 11.4281 11.3219L12.5625 14.35C12.75 14.8531 13.3063 15.1187 13.8156 14.9469L15.3156 14.4469C15.8406 14.2719 16.1219 13.7062 15.9469 13.1812C15.7719 12.6562 15.2062 12.375 14.6812 12.55L14.0969 12.7437L13.3 10.6187C12.9344 9.64375 12 8.99687 10.9594 8.99687H8.81875L8.51875 7.49687H10.5C11.0531 7.49687 11.5 7.05 11.5 6.49687C11.5 5.94375 11.0531 5.49687 10.5 5.49687H8.11875V5.5Z"
        fill="#525252"
      />
    </g>
    <defs>
      <clipPath id="clip0_467_14553">
        <path d="M0 0H16V16H0V0Z" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const LightingIcon = ({ ...props }: React.SVGProps<SVGSVGElement>) => (
  <svg
    width="14"
    height="16"
    viewBox="0 0 14 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <g clip-path="url(#clip0_467_14565)">
      <path
        d="M10.92 1.39354C11.1044 0.965419 10.9669 0.465419 10.5888 0.190419C10.2107 -0.0845806 9.69504 -0.0595806 9.34192 0.246669L1.34192 7.24667C1.02942 7.52167 0.91692 7.96229 1.0638 8.34979C1.21067 8.73729 1.58567 8.99979 2.0013 8.99979H5.48567L3.08255 14.606C2.89817 15.0342 3.03567 15.5342 3.4138 15.8092C3.79192 16.0842 4.30755 16.0592 4.66067 15.7529L12.6607 8.75292C12.9732 8.47792 13.0857 8.03729 12.9388 7.64979C12.7919 7.26229 12.42 7.00292 12.0013 7.00292H8.51692L10.92 1.39354Z"
        fill="#525252"
      />
    </g>
    <defs>
      <clipPath id="clip0_467_14565">
        <path d="M0 0H14V16H0V0Z" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const ShieldIcon = ({ ...props }: React.SVGProps<SVGSVGElement>) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <g clip-path="url(#clip0_467_14577)">
      <path
        d="M8.00001 0C8.14376 0 8.28751 0.03125 8.41876 0.090625L14.3031 2.5875C14.9906 2.87813 15.5031 3.55625 15.5 4.375C15.4844 7.475 14.2094 13.1469 8.82501 15.725C8.30314 15.975 7.69689 15.975 7.17501 15.725C1.79064 13.1469 0.515639 7.475 0.500014 4.375C0.496889 3.55625 1.00939 2.87813 1.69689 2.5875L7.58439 0.090625C7.71251 0.03125 7.85626 0 8.00001 0ZM8.00001 2.0875V13.9C12.3125 11.8125 13.4719 7.19062 13.5 4.41875L8.00001 2.0875Z"
        fill="#525252"
      />
    </g>
    <defs>
      <clipPath id="clip0_467_14577">
        <path d="M0 0H16V16H0V0Z" fill="white" />
      </clipPath>
    </defs>
  </svg>
);
