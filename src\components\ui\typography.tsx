import * as React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '@/lib/utils';

const typographyVariants = cva('', {
  variants: {
    variant: {
      // Display variants - for large headings
      'display-2xl': 'text-7xl font-bold tracking-tight lg:text-8xl',
      'display-xl': 'text-6xl font-bold tracking-tight lg:text-7xl',
      'display-lg': 'text-5xl font-bold tracking-tight lg:text-6xl',
      'display-md': 'text-4xl font-bold tracking-tight lg:text-5xl',
      'display-sm': 'text-3xl font-bold tracking-tight lg:text-4xl',
      'display-xs': 'text-2xl font-bold tracking-tight lg:text-3xl',

      // Heading variants - for section headings
      h1: 'text-4xl font-bold tracking-tight text-header',
      h2: 'text-3xl font-bold tracking-tight text-header',
      h3: 'text-2xl font-bold tracking-tight text-header',
      h4: 'text-xl font-bold tracking-tight text-header',
      h5: 'text-lg font-bold tracking-tight text-header',
      h6: 'text-base font-bold tracking-tight text-header',

      // Body text variants
      'body-xl': 'text-xl font-normal leading-7',
      'body-lg': 'text-lg font-normal leading-7',
      'body-md': 'text-base font-normal leading-6',
      'body-sm': 'text-sm font-normal leading-5',
      'body-xs': 'text-xs font-normal leading-4',

      // Lead text - for introductory paragraphs
      lead: 'text-xl font-normal leading-7 text-muted-foreground',

      // Large text
      large: 'text-lg font-semibold',

      // Small text
      small: 'text-sm font-medium leading-none',

      // Muted text
      muted: 'text-sm text-muted-foreground',

      // Code text
      code: 'relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm font-semibold',

      // Link text
      link: 'font-medium text-primary underline underline-offset-4 hover:no-underline',

      // Blockquote
      blockquote: 'mt-6 border-l-2 pl-6 italic',

      // List
      list: 'my-6 ml-6 list-disc [&>li]:mt-2',

      // Inline code
      'inline-code':
        'relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm font-semibold',
    },
    color: {
      default: 'text-foreground',
      header: 'text-header',
      'sub-header': 'text-sub-header',
      muted: 'text-muted-foreground',
      primary: 'text-primary',
      secondary: 'text-secondary-foreground',
      accent: 'text-accent-foreground',
      destructive: 'text-destructive',
      success: 'text-green-600 dark:text-green-400',
      warning: 'text-yellow-600 dark:text-yellow-400',
      info: 'text-blue-600 dark:text-blue-400',
    },
    align: {
      left: 'text-left',
      center: 'text-center',
      right: 'text-right',
      justify: 'text-justify',
    },
    weight: {
      thin: 'font-thin',
      light: 'font-light',
      normal: 'font-normal',
      medium: 'font-medium',
      semibold: 'font-semibold',
      bold: 'font-bold',
      extrabold: 'font-extrabold',
      black: 'font-black',
    },
  },
  defaultVariants: {
    variant: 'body-md',
    color: 'default',
    align: 'left',
    weight: 'normal',
  },
});

export interface TypographyProps
  extends Omit<React.HTMLAttributes<HTMLElement>, 'color'>,
    VariantProps<typeof typographyVariants> {
  as?: keyof React.JSX.IntrinsicElements;
  children: React.ReactNode;
}

const Typography = React.forwardRef<HTMLElement, TypographyProps>(
  (
    { className, variant, color, align, weight, as, children, ...props },
    ref
  ) => {
    // Determine the appropriate HTML element based on variant
    const getElement = (): keyof React.JSX.IntrinsicElements => {
      if (as) return as;

      switch (variant) {
        case 'display-2xl':
        case 'display-xl':
        case 'display-lg':
        case 'display-md':
        case 'display-sm':
        case 'display-xs':
        case 'h1':
          return 'h1';
        case 'h2':
          return 'h2';
        case 'h3':
          return 'h3';
        case 'h4':
          return 'h4';
        case 'h5':
          return 'h5';
        case 'h6':
          return 'h6';
        case 'blockquote':
          return 'blockquote';
        case 'list':
          return 'ul';
        case 'code':
        case 'inline-code':
          return 'code';
        default:
          return 'p';
      }
    };

    const Component = getElement();

    return React.createElement(
      Component,
      {
        className: cn(
          typographyVariants({ variant, color, align, weight }),
          className
        ),
        ref,
        ...props,
      },
      children
    );
  }
);

Typography.displayName = 'Typography';

// Convenience components for common use cases
export const Display = React.forwardRef<
  HTMLHeadingElement,
  Omit<TypographyProps, 'variant'>
>(({ children, color = 'header', ...props }, ref) => (
  <Typography ref={ref} variant="display-lg" color={color} {...props}>
    {children}
  </Typography>
));
Display.displayName = 'Display';

export const Heading = React.forwardRef<
  HTMLHeadingElement,
  Omit<TypographyProps, 'variant'> & { level?: 1 | 2 | 3 | 4 | 5 | 6 }
>(({ level = 1, children, color = 'header', ...props }, ref) => (
  <Typography ref={ref} variant={`h${level}` as const} color={color} {...props}>
    {children}
  </Typography>
));
Heading.displayName = 'Heading';

export const Body = React.forwardRef<
  HTMLParagraphElement,
  Omit<TypographyProps, 'variant'> & { size?: 'xl' | 'lg' | 'md' | 'sm' | 'xs' }
>(({ size = 'md', children, ...props }, ref) => (
  <Typography ref={ref} variant={`body-${size}` as const} {...props}>
    {children}
  </Typography>
));
Body.displayName = 'Body';

export const Lead = React.forwardRef<
  HTMLParagraphElement,
  Omit<TypographyProps, 'variant'>
>(({ children, ...props }, ref) => (
  <Typography ref={ref} variant="lead" {...props}>
    {children}
  </Typography>
));
Lead.displayName = 'Lead';

export const Large = React.forwardRef<
  HTMLDivElement,
  Omit<TypographyProps, 'variant'>
>(({ children, color = 'sub-header', ...props }, ref) => (
  <Typography ref={ref} variant="large" color={color} {...props}>
    {children}
  </Typography>
));
Large.displayName = 'Large';
export const Small = React.forwardRef<
  HTMLElement,
  Omit<TypographyProps, 'variant'>
>(({ children, ...props }, ref) => (
  <Typography ref={ref} variant="small" {...props}>
    {children}
  </Typography>
));
Small.displayName = 'Small';

export const Muted = React.forwardRef<
  HTMLParagraphElement,
  Omit<TypographyProps, 'variant'>
>(({ children, ...props }, ref) => (
  <Typography ref={ref} variant="muted" {...props}>
    {children}
  </Typography>
));
Muted.displayName = 'Muted';

export const Code = React.forwardRef<
  HTMLElement,
  Omit<TypographyProps, 'variant'>
>(({ children, ...props }, ref) => (
  <Typography ref={ref} variant="code" {...props}>
    {children}
  </Typography>
));
Code.displayName = 'Code';

export const InlineCode = React.forwardRef<
  HTMLElement,
  Omit<TypographyProps, 'variant'>
>(({ children, ...props }, ref) => (
  <Typography ref={ref} variant="inline-code" {...props}>
    {children}
  </Typography>
));
InlineCode.displayName = 'InlineCode';

export const Link = React.forwardRef<
  HTMLAnchorElement,
  Omit<TypographyProps, 'variant'> & {
    href?: string;
    target?: string;
    rel?: string;
  }
>(({ children, href, target, rel, ...props }, ref) => (
  <Typography
    ref={ref}
    variant="link"
    as={href ? 'a' : 'span'}
    {...(href && {
      href,
      target,
      rel,
    })}
    {...props}
  >
    {children}
  </Typography>
));
Link.displayName = 'Link';

export const Blockquote = React.forwardRef<
  HTMLQuoteElement,
  Omit<TypographyProps, 'variant'>
>(({ children, ...props }, ref) => (
  <Typography ref={ref} variant="blockquote" {...props}>
    {children}
  </Typography>
));
Blockquote.displayName = 'Blockquote';

export const List = React.forwardRef<
  HTMLUListElement,
  Omit<TypographyProps, 'variant'>
>(({ children, ...props }, ref) => (
  <Typography ref={ref} variant="list" {...props}>
    {children}
  </Typography>
));
List.displayName = 'List';
export { Typography, typographyVariants };
export type { TypographyProps as TypographyPropsType };
