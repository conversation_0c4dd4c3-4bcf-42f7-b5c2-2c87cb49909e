'use client';

import { useEffect } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { HomeIcon, RefreshCwIcon, AlertTriangleIcon } from 'lucide-react';
import { Routes } from '@/lib/routes';

interface ErrorProps {
  error: Error & { digest?: string };
  reset: () => void;
}

export default function Error({ error, reset }: ErrorProps) {
  useEffect(() => {
    // Log the error to an error reporting service
    console.error('Error:', error);
  }, [error]);

  return (
    <div className="flex items-center justify-center min-h-[calc(100vh-64px)] bg-background">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-destructive/10">
            <AlertTriangleIcon className="h-8 w-8 text-destructive" />
          </div>
          <CardTitle className="text-2xl font-bold">
            Something went wrong!
          </CardTitle>
          <CardDescription className="text-muted-foreground">
            An unexpected error occurred. Please try again or contact support if
            the problem persists.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex flex-col space-y-2">
            <Button onClick={reset} className="w-full">
              <RefreshCwIcon className="mr-2 h-4 w-4" />
              Try again
            </Button>
            <Button variant="outline" asChild className="w-full">
              <Link
                href={Routes.HOME}
                className="flex items-center justify-center space-x-2"
              >
                <HomeIcon className="h-4 w-4" />
                <span>Go to Home</span>
              </Link>
            </Button>
          </div>
          {process.env.NODE_ENV === 'development' && (
            <div className="mt-4 rounded-md bg-muted p-3">
              <p className="text-xs font-mono text-muted-foreground">
                Error: {error.message}
                {error.digest && (
                  <>
                    <br />
                    Digest: {error.digest}
                  </>
                )}
              </p>
            </div>
          )}
          <div className="text-center text-sm text-muted-foreground">
            <p>If this error persists, please contact our support team.</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
