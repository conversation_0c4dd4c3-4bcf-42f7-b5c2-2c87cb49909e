'use client';

import { useState } from 'react';
import { useAuth } from '@/contexts/auth-context';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import Link from 'next/link';
import { Routes } from '@/lib/routes';
import { useSearchParams } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import Image from 'next/image';
import { toastDismiss, toastError, toastLoading } from '@/lib/toast';
import { Heading, Body } from '@/components/ui/typography';
import { EyeOff } from 'lucide-react';
import { Eye } from 'lucide-react';

// Form validation schema
const signInSchema = z.object({
  email: z
    .string()
    .min(1, 'Email is required')
    .email('Please enter a valid email address'),
  password: z
    .string()
    .min(1, 'Password is required')
    .min(6, 'Password must be at least 6 characters'),
});

type SignInFormData = z.infer<typeof signInSchema>;

export default function SignInPage() {
  const [loading, setLoading] = useState(false);
  const { signIn } = useAuth();
  const searchParams = useSearchParams();
  const callbackUrl = searchParams.get('callbackUrl') || '';
  const [showPassword, setShowPassword] = useState(false);

  const form = useForm<SignInFormData>({
    resolver: zodResolver(signInSchema),
    mode: 'onChange', // Enable real-time validation
    defaultValues: {
      email: '',
      password: 'securePassword@123',
    },
  });

  const onSubmit = async (data: SignInFormData) => {
    // Set loading immediately for better UX
    setLoading(true);
    const toastId = toastLoading('Signing you in...');

    try {
      await signIn(data.email, data.password, callbackUrl);

      toastDismiss(toastId);

      // Don't reset loading state here as we're redirecting
    } catch (error) {
      console.error('Error signing in:', error);
      // Update the toast to error
      toastDismiss(toastId);
      toastError('Invalid credentials. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="flex items-center justify-center w-full h-screen">
      <div className="flex-7/12 flex justify-center">
        <div className="space-y-6 max-w-[456px] w-full px-16 py-8">
          <div className="text-center md:text-left">
            <div className="flex items-center justify-center">
              <Image
                src="/assets/logo.svg"
                alt="Scoutr Logo"
                width={200}
                height={0}
              />
            </div>
            <div className="space-y-2 mt-8">
              <Heading
                level={4}
                className="text-header font-medium text-center"
              >
                Welcome to Scoutr
              </Heading>
              <Body className="text-center">
                The easiest way to create and share reference locations.
              </Body>
            </div>
          </div>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email</FormLabel>
                    <FormControl>
                      <Input
                        type="email"
                        placeholder="Enter your email"
                        {...field}
                        disabled={loading}
                      />
                    </FormControl>
                    <FormMessage className="text-red-500" />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItem className="mb-4">
                    <FormLabel>Password</FormLabel>
                    <FormControl className="relative">
                      <div className="relative">
                        <Input
                          type={showPassword ? 'text' : 'password'}
                          placeholder="Enter your password"
                          {...field}
                          disabled={loading}
                          className="pr-10"
                        />
                        <button
                          type="button"
                          onClick={() => setShowPassword(!showPassword)}
                          className="cursor-pointer absolute right-3 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700 focus:outline-none"
                          disabled={loading}
                        >
                          {showPassword ? (
                            <EyeOff className="h-4 w-4" />
                          ) : (
                            <Eye className="h-4 w-4" />
                          )}
                        </button>
                      </div>
                    </FormControl>
                    <FormMessage className="text-red-500" />
                  </FormItem>
                )}
              />

              <div className="text-right">
                <Link
                  href={Routes.FORGOT_PASSWORD}
                  className="text-sm text-header underline"
                >
                  Forgot your password?
                </Link>
              </div>

              <Button
                type="submit"
                className="w-full"
                color="primary"
                disabled={loading || !form.formState.isValid}
              >
                {loading ? (
                  <div className="flex items-center space-x-2">
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    <span>Logging in...</span>
                  </div>
                ) : (
                  'Log in'
                )}
              </Button>
            </form>
          </Form>
        </div>
      </div>
      <div className="flex-5/12 flex justify-center bg-border h-full">
        <Image
          src="/assets/circle-placeholder.svg"
          alt="Password Setup"
          width={360}
          height={360}
        />
      </div>
    </div>
  );
}
