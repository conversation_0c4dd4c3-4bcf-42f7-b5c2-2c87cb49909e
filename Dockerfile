FROM node:22-alpine AS base

FROM base AS build
WORKDIR /app

COPY package.json yarn.lock ./
RUN yarn install --frozen-lockfile

COPY . .
RUN yarn build

FROM base AS production
WORKDIR /app

ENV NODE_ENV=production
ENV PORT=3000

COPY --from=build /app/public ./public
COPY --from=build /app/.next/standalone ./
COPY --from=build /app/.next/static ./.next/static
COPY --from=build /app/.env .env

RUN addgroup -g 1001 -S nodejs && adduser -S nextjs -u 1001

EXPOSE 3000

USER nextjs

ENV HOSTNAME="0.0.0.0"
CMD ["node", "server.js"]
