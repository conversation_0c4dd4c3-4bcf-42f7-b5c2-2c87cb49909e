'use client';

import * as React from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import SimpleMultiSelect from './SimpleMultiSelect';
import { TagOption } from '@/types/tag';
import { FormEvent, useCallback, useEffect, useState } from 'react';
import { toast } from 'sonner';
import { useTagService } from '@/hooks/use-services';

interface EditImageTagsModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSave: (tags: TagOption[]) => void;
  tags?: TagOption[];
}

export default function EditImageTagsModal({
  open,
  onOpenChange,
  onSave,
  tags: originTags,
}: EditImageTagsModalProps) {
  const tagService = useTagService();
  const [tagIds, setTagIds] = useState<string[]>([]);
  const [tagOption, setTagOption] = useState<TagOption[]>([]);

  const handleSubmit = (e: FormEvent) => {
    e.preventDefault();
    if (!tagIds.length) return;
    const result = tagIds.map(tagId => {
      const tag = tagOption.find(tag => tag.value === tagId);
      return tag
        ? { value: tag.value, label: tag.label, color: tag.color }
        : ({} as TagOption);
    });
    onSave(result);
    onOpenChange(false);
  };

  useEffect(() => {
    if (!open) return;
    setTagIds(originTags?.map(tag => tag.value) ?? []);
  }, [originTags, open]);

  const fetchStyleTags = useCallback(async () => {
    try {
      const response = await tagService.getTags({ type: 'style' });
      setTagOption(
        response.data.map(data => ({
          value: data.id,
          label: data.name,
          color: data.color,
        }))
      );
    } catch {
      toast.error('Failed to get tag styles');
    }
  }, [tagService]);

  useEffect(() => {
    fetchStyleTags();
  }, [fetchStyleTags]);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[329px]">
        <DialogHeader>
          <DialogTitle>Add/Edit tags</DialogTitle>
          <DialogDescription className="text-sm">
            Add tags to better describe your photo.
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label
              htmlFor="type"
              className="text-sm font-medium text-foreground"
            >
              Tags
            </Label>
            <SimpleMultiSelect
              options={tagOption}
              value={tagIds}
              onChange={setTagIds}
              placeholder="Select tags"
              groupLabel="Style"
              className="w-[279px]"
            />
          </div>

          <DialogFooter className="mt-6">
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={tagIds.length === 0}>
              Save Changes
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
