'use client';

import { Card, CardContent, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';

export default function Loading({ isActive = true }) {
  return (
    <div className="space-y-6">
      <div className="border shadow-sm rounded-md">
        <Card className="py-0 border-0 gap-0 rounded-md">
          <CardTitle className="h-[62px] flex items-center px-6 bg-[#FAFAFA] rounded-md">
            Active Users
          </CardTitle>
          <CardContent className="px-0">
            <div className="border-t">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="px-6 bg-[#FAFAFA]">User</TableHead>
                    <TableHead className="px-6 bg-[#FAFAFA]">Email</TableHead>
                    <TableHead className="px-6 bg-[#FAFAFA]">Role</TableHead>
                    {isActive && (
                      <TableHead className="px-6 bg-[#FAFAFA]">
                        Status
                      </TableHead>
                    )}
                    <TableHead className="px-6 bg-[#FAFAFA]"></TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {Array.from({ length: 6 }).map((_, i) => (
                    <TableRow key={`s-${i}`}>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Skeleton className="h-8 w-8 rounded-full" />
                          <Skeleton className="h-4 w-32" />
                        </div>
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-48" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-20" />
                      </TableCell>
                      {isActive && (
                        <TableCell>
                          <Skeleton className="h-4 w-20" />
                        </TableCell>
                      )}
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
