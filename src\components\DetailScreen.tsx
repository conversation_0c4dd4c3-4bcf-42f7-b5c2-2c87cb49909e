'use client';

import * as React from 'react';
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
  CarouselPagination,
} from './ui/carousel';
import Image from 'next/image';
import { Button } from './ui/button';
import { X } from 'lucide-react';
import FullscreenModal from './FullscreenModal';

interface DetailScreenProps {
  images: string[];
  open: boolean;
  onOpenChange: (open: boolean) => void;
  startIndex?: number;
}

export default function DetailScreen({
  open,
  images,
  onOpenChange,
  startIndex = 0,
}: DetailScreenProps) {
  const [activeIndex, setActiveIndex] = React.useState<number>(0);
  const [imageSelected, setImageSelected] = React.useState<string>('');
  const [fullScreenOpen, setFullScreenOpen] = React.useState<boolean>(false);
  return (
    <>
      {open && (
        <div className="fixed top-0 left-0 w-screen h-screen bg-neutral-800 z-50 flex flex-col items-center justify-between py-12">
          <Button
            onClick={() => onOpenChange(false)}
            className="rounded-full absolute right-12 top-12"
          >
            <X />
          </Button>
          <div className=" rounded-[9999px] bg-neutral-800 px-4 py-2 text-white">
            {activeIndex + 1} of {images.length}
          </div>
          <div>
            <Carousel
              onSelectedIndex={setActiveIndex}
              startIndex={startIndex}
              className="relative w-[300px] md:w-[600px] 2xl:w-[900px] h-[200px] md:h-[400px] 2xl:h-[600px] mb-[calc((100vh-96px-200px)/2)] md:mb-[calc((100vh-96px-400px)/2)] 2xl:mb-[calc((100vh-96px-600px)/2)]"
            >
              <CarouselContent>
                {images.map((image, index) => (
                  <CarouselItem key={index}>
                    <div
                      onClick={() => {
                        setImageSelected(image);
                        setFullScreenOpen(true);
                      }}
                      className="w-full h-full relative aspect-[900/600]"
                    >
                      <Image
                        src={image}
                        alt="location view"
                        fill
                        className="w-full h-full object-cover rounded-lg"
                      />
                    </div>
                  </CarouselItem>
                ))}
              </CarouselContent>
              <CarouselPrevious className="absolute top-1/2 -left-9 md:-left-14 xl:-left-24 z-10 w-8 md:w-12 md:h-12 h-8 bg-black opacity-50 border-none" />
              <CarouselNext className="absolute top-1/2 -right-9 md:-right-14 xl:-right-24 z-10 w-8 md:w-12 md:h-12 h-8 bg-black opacity-50 border-none" />
              <div className="w-full flex justify-center">
                <CarouselPagination className="w-fit mt-[calc(100vh/2-100px-40px-72px)] md:mt-[calc(100vh/2-200px-40px-72px)] 2xl:mt-[calc(100vh/2-300px-40px-72px)]" />
              </div>
            </Carousel>
          </div>
          {fullScreenOpen && (
            <FullscreenModal
              src={imageSelected}
              onClose={() => setFullScreenOpen(false)}
            />
          )}
        </div>
      )}
    </>
  );
}
