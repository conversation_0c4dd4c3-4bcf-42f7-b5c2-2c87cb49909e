'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { Routes } from '@/lib/routes';
import { ArrowLeft, Loader2 } from 'lucide-react';
import { useCallback, useEffect, useMemo, useState } from 'react';
import StepIndicator, { Step } from '@/components/StepIndicator';
import { ArrowRightIcon } from '@/lib/icons';
import { isEmpty } from 'lodash';
import { toast } from 'sonner';
import { useRouter } from 'next/navigation';
import ConfirmDialog from '@/components/ConfirmDialog';
import PhotosForm from '@/components/photo/PhotosForm';
import { MINIMUM_IMAGES } from '@/types/constant';
import BasicDetailsForm from '@/components/BasicDetailsForm';
import TagsForm from '@/components/TagsForm';
import { useLocationService, useTagService } from '@/hooks/use-services';
import { Tag, TagOption } from '@/types/tag';
import { CreateLocationData } from '@/lib/services/location-service';
import { LocationRequest, Photo } from '@/types/location';
import { TagType } from '@/types/enum';

enum StepValue {
  BASIC_DETAIL = 'basic-details',
  TAGS = 'tags',
  PHOTOS = 'photos',
}
const steps: Step[] = [
  { label: 'Basic Details', value: StepValue.BASIC_DETAIL },
  { label: 'Tags', value: StepValue.TAGS },
  { label: 'Photos', value: StepValue.PHOTOS },
];

export default function CreateLocationPage() {
  const router = useRouter();
  const tagService = useTagService();
  const locationService = useLocationService();
  const initialFormData: LocationRequest = {
    title: '',
    address: '',
    description: '',
    categories: [],
    subCategory: '',
    style: [],
    size: '',
    images: [],
  };
  const [formData, setFormData] = useState<LocationRequest>(initialFormData);
  const [currentStep, setCurrentStep] = useState<string>(
    StepValue.BASIC_DETAIL
  );
  const [discardDialogOpen, setDiscardDialogOpen] = useState<boolean>(false);
  const [categoryOption, setCategoryOption] = useState<TagOption[]>([]);
  const [subCategoryOption, setSubCategoryOption] = useState<TagOption[]>([]);
  const [styleOption, setStyleOption] = useState<TagOption[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const isDirty = useMemo(() => {
    // check if the formData is different from the initial formData
    return JSON.stringify(formData) !== JSON.stringify(initialFormData);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [formData]);

  const handleContinue = () => {
    switch (currentStep) {
      case StepValue.BASIC_DETAIL:
        setCurrentStep(StepValue.TAGS);
        break;
      case StepValue.TAGS:
        setCurrentStep(StepValue.PHOTOS);
        break;
      default:
        break;
    }
  };

  const buildCreateLocationPayload = (
    data: LocationRequest
  ): CreateLocationData => {
    const {
      title,
      address,
      latitude,
      longitude,
      description,
      categories,
      subCategory,
      style,
      size,
      images,
    } = data;
    return {
      title,
      address,
      latitude,
      longitude,
      description,
      tagIds: [...categories, subCategory, ...style],
      size,
      images: images.map(image => ({
        url: image.url,
        order: image.order,
        key: image.key || '',
        tagIds: image.tags.map(tag => tag.id),
      })),
    };
  };

  const handleCreateLocation = async () => {
    setIsLoading(true);
    try {
      if (formData.images.some(image => isEmpty(image.tags))) {
        toast.warning('Not all images have a tag.');
      } else {
        const payload = buildCreateLocationPayload(formData);
        await locationService.createLocation(payload);
        toast.success('Location created successfully.');
        router.push(Routes.ADMIN_LOCATIONS);
      }
    } catch {
      toast.error('Failed to create location');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    setDiscardDialogOpen(true);
  };
  const handleChange = (data: Partial<LocationRequest>) => {
    if (data) {
      setFormData(prev => ({
        ...prev,
        ...data,
      }));
    }
  };

  const handlePhotosChange = (newPhotos: (Photo | undefined)[]) => {
    setFormData(prev => ({
      ...prev,
      images: newPhotos.filter((photo): photo is Photo => photo !== undefined),
    }));
  };

  const validateForm = (data: Partial<LocationRequest>) => {
    if (!data) {
      return true;
    }
    if (currentStep === StepValue.BASIC_DETAIL) {
      const basicDetailFields: Array<'title' | 'address' | 'description'> = [
        'title',
        'address',
        'description',
      ];
      return basicDetailFields.some(field => isEmpty(data[field]));
    }
    if (currentStep === StepValue.TAGS) {
      const tagsFields: Array<'categories' | 'subCategory' | 'style' | 'size'> =
        ['categories', 'subCategory', 'style', 'size'];
      return tagsFields.some(field => isEmpty(data[field]));
    }
    if (currentStep === StepValue.PHOTOS) {
      return (
        !data.images ||
        (Array.isArray(data.images) && data.images.length < MINIMUM_IMAGES)
      );
    }
  };

  const fetchCategories = useCallback(async () => {
    try {
      const response = await tagService.getTags({ type: TagType.CATEGORY });
      setCategoryOption(
        response.data.map(data => ({
          value: data.id,
          label: data.name,
          color: data.color,
        }))
      );
    } catch {
      toast.error('Failed to get tag categories');
    }
  }, [tagService]);

  const fetchSubCategories = useCallback(async () => {
    try {
      const response = await tagService.getTags({ type: TagType.SUB_CATEGORY });
      setSubCategoryOption(
        response.data.map(data => ({
          value: data.id,
          label: data.name,
          color: data.color,
        }))
      );
    } catch {
      toast.error('Failed to get tag sub-categories');
    }
  }, [tagService]);

  const fetchStyles = useCallback(async () => {
    try {
      const response = await tagService.getTags({ type: TagType.STYLE });
      setStyleOption(
        response.data.map(data => ({
          value: data.id,
          label: data.name,
          color: data.color,
        }))
      );
    } catch {
      toast.error('Failed to get tag styles');
    }
  }, [tagService]);

  const handleCreateNewTag = async (tag: Partial<Tag>) => {
    try {
      const { name, type, color } = tag;
      await tagService.createTag({ name, type, color });
      toast.success('Tag created successfully.');
      fetchTags();
    } catch {
      toast.error('Failed to add new tag');
    }
  };

  const fetchTags = useCallback(() => {
    fetchCategories();
    fetchSubCategories();
    fetchStyles();
  }, [fetchCategories, fetchStyles, fetchSubCategories]);

  useEffect(() => {
    fetchTags();
  }, [fetchTags]);

  return (
    <>
      <div
        className={`flex flex-col items-center pb-20 ${currentStep !== StepValue.PHOTOS ? 'sm:mb-0' : ''}`}
      >
        <div className="w-full sm:w-[unset] sm:max-w-4xl space-y-8">
          <div
            onClick={e => {
              e.preventDefault();
              if (isDirty) {
                setDiscardDialogOpen(true);
              } else {
                router.push(Routes.ADMIN_LOCATIONS);
              }
            }}
            className="cursor-pointer inline-flex items-center gap-2 text-sm text-gray-600 hover:text-gray-900 transition-colors"
          >
            <ArrowLeft className="h-4 w-4" />
            Back to Locations
          </div>

          <StepIndicator
            steps={steps}
            currentStep={currentStep}
            onStepClick={setCurrentStep}
          />
          <div className="w-full sm:w-[672px] flex flex-col gap-8">
            {currentStep === StepValue.BASIC_DETAIL && (
              <div className="space-y-6">
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">
                    Create a new location
                  </h1>
                  <p className="text-gray-600 mt-2">
                    Start by adding the basic details for the location.
                  </p>
                </div>
                <BasicDetailsForm data={formData} onChange={handleChange} />
              </div>
            )}
            {currentStep === StepValue.TAGS && (
              <div className="space-y-6">
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">
                    What best describes this location?
                  </h1>
                  <p className="text-gray-600 mt-2">
                    Select the category, style, and size tags that capture the
                    look and feel. These tags make it easier for scouts to
                    discover the right fit.
                  </p>
                </div>
                <TagsForm
                  data={formData}
                  categoryOption={categoryOption}
                  subCategoryOption={subCategoryOption}
                  styleOption={styleOption}
                  onChange={handleChange}
                  onCreateTag={handleCreateNewTag}
                />
              </div>
            )}
          </div>

          {currentStep === StepValue.PHOTOS && (
            <PhotosForm data={formData.images} onChange={handlePhotosChange} />
          )}

          <div className="bg-white z-50 fixed left-0 py-5 px-4 sm:px-0 right-0 bottom-0 border-t flex justify-center items-center">
            <div className="w-[672px] flex items-center justify-between">
              <Button variant="outline" onClick={handleCancel} className="px-6">
                Cancel
              </Button>
              {currentStep === StepValue.PHOTOS ? (
                <Button
                  onClick={handleCreateLocation}
                  disabled={validateForm(formData) || isLoading}
                  className="px-6 bg-black hover:bg-gray-800 text-white"
                >
                  {isLoading ? (
                    <>
                      Creating ... <Loader2 className="w-4 h-4 animate-spin" />
                    </>
                  ) : (
                    'Create location'
                  )}
                </Button>
              ) : (
                <Button
                  onClick={handleContinue}
                  disabled={validateForm(formData)}
                  className="px-6 bg-black hover:bg-gray-800 text-white"
                >
                  Continue
                  <ArrowRightIcon color="white" className="h-4 w-4 ml-2" />
                </Button>
              )}
            </div>
          </div>
        </div>
      </div>
      <ConfirmDialog
        open={discardDialogOpen}
        onOpenChange={setDiscardDialogOpen}
        title="Discard reference?"
        description="You’ve started filling in this reference’s details. If you cancel now, the information will be lost."
        confirmText="Discard"
        cancelText="Keep editing"
        onConfirm={() => router.push(Routes.ADMIN_LOCATIONS)}
        onCancel={() => setDiscardDialogOpen(false)}
      />
    </>
  );
}
