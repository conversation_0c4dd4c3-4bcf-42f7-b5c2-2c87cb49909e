'use client';

import ConfirmDialog from '@/components/ConfirmDialog';
import LocationCard from '@/components/LocationCard';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Typography } from '@/components/ui/typography';
import { PencilIcon, PlayArrowIcon, SearchIcon, ShareIcon } from '@/lib/icons';
import { getDisplayText } from '@/lib/utils';
import { useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { Location } from '@/types';

interface ReferenceListProps {
  locations: Location[];
  productionHouse: string;
  dateRange: string;
  totalReferences: number;
  status: string;
  referenceName: string;
  onDeleteLocation: (location?: Location) => void;
}

export default function ReferenceListView({
  locations,
  productionHouse,
  dateRange,
  totalReferences,
  status,
  referenceName,
  onDeleteLocation,
}: ReferenceListProps) {
  const [search, setSearch] = useState<string>('');
  const [selectedLocation, setSelectedLocation] = useState<Location>();
  const [removeLocationModalOpen, setRemoveLocationModalOpen] =
    useState<boolean>(false);
  const router = useRouter();
  const params = useParams();
  const id = params.id as string;

  return (
    <>
      <div className="w-full flex justify-center">
        <div className="space-y-6 xl:w-[1128px] p-6 xl:px-0">
          <div className="flex items-center gap-2">
            <h1 className="text-2xl font-bold">{referenceName}</h1>
            <PencilIcon className="cursor-pointer" />
          </div>
          <Typography color="muted">
            {productionHouse} • {dateRange} • {totalReferences} references{' '}
            {
              <Badge variant="secondary" className="capitalize">
                {getDisplayText(status)}
              </Badge>
            }
          </Typography>
          {locations.length === 0 && (
            <div className="fixed inset-0 grid place-items-center items-center justify-center">
              <div className="space-y-12 text-center">
                <div className="w-[320px] text-center">
                  <h1 className="text-2xl font-bold mb-">
                    Start building your reference list
                  </h1>
                  <span className="text-base">
                    Your selected locations will appear here.
                  </span>
                </div>
                <Button
                  variant="default"
                  className="w-[178px] h-[41px]"
                  onClick={() => {
                    router.push(`/scout/reference/${id}/search-locations`);
                  }}
                >
                  Search locations
                </Button>
              </div>
            </div>
          )}
          {locations.length > 0 && (
            <div className="w-full space-y-6">
              <div className="flex flex-col gap-4 lg:flex-row lg:items-center lg:justify-between">
                <div className="flex flex-col gap-4 lg:flex-row lg:items-center lg:flex-1 lg:justify-between">
                  <div className="relative w-full lg:w-[359px]">
                    <SearchIcon className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      placeholder="Search location..."
                      value={search ?? ''}
                      onChange={e => setSearch(e.target.value)}
                      className="pl-9"
                    />
                  </div>
                  <div className="flex gap-3 lg:flex-row lg:items-center lg:justify-end">
                    <Button
                      variant="outline"
                      className="w-[159px] h-[41px]"
                      onClick={() => {
                        router.push(`/scout/reference/${id}/search-locations`);
                      }}
                    >
                      Search locations
                    </Button>
                    <Button variant="outline" className="w-[159px] h-[41px]">
                      <PlayArrowIcon /> Preview
                    </Button>
                    <Button className="w-[201px] h-[41px]">
                      <ShareIcon />
                      Share List
                    </Button>
                  </div>
                </div>
              </div>
              {locations.length > 0 && (
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                  {locations.map((location, index) => {
                    return (
                      <LocationCard
                        key={index}
                        location={location}
                        onDelete={() => {
                          setSelectedLocation(location);
                          setRemoveLocationModalOpen(true);
                        }}
                        isReferenceListFullScreen
                      />
                    );
                  })}
                </div>
              )}
            </div>
          )}
        </div>
      </div>
      <ConfirmDialog
        open={removeLocationModalOpen}
        onOpenChange={setRemoveLocationModalOpen}
        title="Remove reference from list?"
        description="This location will be removed from the reference list. You can add it again later if needed."
        confirmText="Remove"
        cancelText="Keep location"
        onConfirm={() => onDeleteLocation(selectedLocation)}
      />
    </>
  );
}
