import { NextRequest, NextResponse } from 'next/server';
import { getServerSession, User } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { makeServerRequest } from '@/lib/api-client';
import { Role } from '@/types/enum';

export async function PUT(request: NextRequest) {
  try {
    // Get the current session
    const session = await getServerSession(authOptions);

    if (!session?.user?.id || !session?.accessToken) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { firstName, lastName, email, role, avatar } = body;

    // Validation
    if (!firstName || !lastName || !email || !role) {
      return NextResponse.json(
        { error: 'First name, last name, email, and role are required' },
        { status: 400 }
      );
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { error: 'Invalid email format' },
        { status: 400 }
      );
    }

    // Role validation
    const validRoles = [Role.ADMIN, Role.SCOUT, Role.VIEWER];
    if (!validRoles.includes(role)) {
      return NextResponse.json(
        { error: 'Invalid role selected' },
        { status: 400 }
      );
    }

    // Call NestJS profile update endpoint using the utility function
    const data = await makeServerRequest('/auth/profile', session.accessToken, {
      method: 'PUT',
      body: JSON.stringify({
        firstName,
        lastName,
        email,
        role,
        avatar,
      }),
    });

    return NextResponse.json(
      {
        message: 'Profile updated successfully',
        user: (data as { user: User }).user,
      },
      { status: 200 }
    );
  } catch (error) {
    console.error('Profile update error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
