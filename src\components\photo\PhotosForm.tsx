import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { InputFileIcon } from '@/lib/icons';
import { Button } from '@/components/ui/button';
import { MINIMUM_IMAGES } from '@/types/constant';
import EditImageTagsModal from '../EditImageTags';
import BulkAddTagsModal from '../BulkAddTags';
import { Photo } from '@/types/location';
import { TagOption } from '@/types/tag';
import { FileUpload } from '@/types';
import PhotoGallery from './PhotoGallery';
import { CheckedState } from '@radix-ui/react-checkbox';
import { toastInfo, toastSuccess } from '@/lib/toast';

interface PhotosFormProps {
  data: Photo[];
  onChange: (data: Partial<Photo[]>) => void;
}

export default function PhotosForm({ data, onChange }: PhotosFormProps) {
  const [isDragging, setIsDragging] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [editIndex, setEditIndex] = useState<number>();
  const [editTagOpen, setEditTagOpen] = useState<boolean>(false);
  const [bulkAddTagsOpen, setBulkAddTagsOpen] = useState<boolean>(false);
  const [photos, setPhotos] = useState<FileUpload[]>(data);

  useEffect(() => {
    if (data.length > 0 && photos.length === 0) {
      setPhotos(
        data.map(photo => ({
          url: photo.url,
          key: photo.key,
          order: photo.order,
          tags: photo.tags,
        }))
      );
    }
  }, [data, photos]);

  // useEffect(() => {
  //   onChange(photos.filter(photo => photo.url !== undefined) as Photo[]);
  // }, [photos, onChange]);

  const handleChange = (index: number, file: FileUpload) => {
    const newPhotos = [...photos];
    newPhotos[index] = file;
    onChange(newPhotos as Photo[]);
    setPhotos(prev => {
      Object.assign(prev[index], file);
      return prev;
    });
  };

  const handleReorder = useCallback(
    (reorderedData: FileUpload[]) => {
      setPhotos(reorderedData);
      // Update the parent component with the new order
      onChange(
        reorderedData.filter(photo => photo.url !== undefined) as Photo[]
      );
    },
    [onChange]
  );

  const handleAddPhotos = (newPhotos: FileUpload[]) => {
    setPhotos(prev => [
      ...prev,
      ...newPhotos.map((photo, index) => ({
        ...photo,
        order: prev.length + index,
      })),
    ]);
  };

  const handleFiles = (files: FileList | null) => {
    if (!files || files.length === 0) return;
    setPhotos(
      Array.from(files).map(file => ({
        file,
        url: '',
        key: '',
        order: 0,
        tags: [],
      }))
    );
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);
    handleFiles(e.dataTransfer.files);
  };

  const handleBrowseClick = () => fileInputRef.current?.click();

  const handleCheckedChange = (index: number, checked: CheckedState) => {
    setPhotos(prev =>
      prev.map((photo, _index) => {
        return _index === index
          ? {
              ...photo,
              isChecked: checked === 'indeterminate' ? undefined : checked,
            }
          : photo;
      })
    );
  };

  const handlePaste = useCallback((e: ClipboardEvent) => {
    const items = e.clipboardData?.items;
    if (!items) return;

    const files: File[] = Array.from(items)
      .map(item => item.getAsFile())
      .filter(file => file !== null);
    setPhotos(prev => [
      ...prev,
      ...files.map(file => ({
        file,
        url: '',
        key: '',
        order: 0,
        tags: [],
      })),
    ]);
    if (files.length > 0) {
      toastSuccess(
        `${files.length} image${files.length > 1 ? 's' : ''} pasted successfully`
      );
    }
  }, []);

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if ((e.ctrlKey || e.metaKey) && e.key === 'v') {
        setTimeout(() => {
          document.addEventListener('paste', handlePaste, { once: true });
        }, 0);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [handlePaste]);

  const handleRemove = (indexToRemove: number) => {
    setPhotos(prev => prev.filter((_, index) => index !== indexToRemove));
  };

  const handleEdit = (index: number) => {
    setEditIndex(index);
    setEditTagOpen(true);
  };

  const handleEditImageTags = (tags: TagOption[]) => {
    const result = photos.map((photo, index) => {
      return index !== editIndex
        ? photo
        : {
            ...photo,
            tags: tags.map(tag => ({
              id: tag.value,
              name: tag.label,
              type: 'style',
              color: tag.color,
            })),
          };
    });
    setPhotos(result);
    onChange(result as Photo[]);
    toastSuccess('Tags updated successfully.', { position: 'bottom-right' });
  };

  const handleBulkAddTags = (tags: TagOption[]) => {
    const updatedData = photos.map(photo => {
      if (photo.isChecked) {
        return {
          ...photo,
          tags: tags.map(tag => ({
            id: tag.value,
            name: tag.label,
            type: 'style',
            color: tag.color,
          })),
        };
      } else {
        return photo;
      }
    });
    setPhotos(updatedData);
    onChange(updatedData as Photo[]);
    if (updatedData.some(photo => photo.tags?.length === 0)) {
      toastInfo('Not all images have a tag.', { position: 'bottom-right' });
    }
    toastSuccess('Tags updated successfully.', { position: 'bottom-right' });
  };

  const handleOpenBulkAddTags = () => {
    if (photos.some(photo => photo.isChecked)) {
      setBulkAddTagsOpen(true);
      return;
    }
    toastInfo('Please select one or more photos', {
      position: 'bottom-right',
    });
  };

  const tagsSelected = useMemo(() => {
    return editIndex !== undefined ? (photos[editIndex]?.tags ?? []) : [];
  }, [photos, editIndex]);

  return (
    <>
      <div className="w-full sm:w-[672px] flex flex-col gap-6">
        <div className="space-y-2">
          <h1 className="text-2xl font-bold text-gray-900">
            {photos.length
              ? `${photos.length} ${photos.length > 1 ? 'photos' : 'photo'} selected`
              : 'Add photos for the location'}
          </h1>
          <p className="text-gray-600">
            {photos.length
              ? 'Drag to reorder. Add tags for context. The first photo will be used as the cover.'
              : `Choose at least ${MINIMUM_IMAGES} photos for your location`}
          </p>
          {photos.length > 0 && (
            <Button
              onClick={handleOpenBulkAddTags}
              variant="outline"
              className="w-[183px] h-[42px]"
            >
              Bulk add tags
            </Button>
          )}
        </div>
        <div
          className={`flex flex-col items-center ${photos.length ? 'hidden' : ''}`}
        >
          <div
            onDragOver={e => {
              e.preventDefault();
              setIsDragging(true);
            }}
            onDragLeave={() => setIsDragging(false)}
            onDrop={handleDrop}
            className={`w-full sm:w-[672px] border rounded-md py-[50px] flex flex-col items-center justify-center text-center transition-colors ${
              isDragging ? 'border-black' : 'border-dashed border-gray-300'
            }`}
          >
            <div className="text-gray-700">
              <button
                className="flex flex-col items-center cursor-pointer gap-4"
                type="button"
                onClick={handleBrowseClick}
              >
                <div className="flex items-center justify-center w-16 h-16 rounded-full bg-[#E5E5E5]">
                  <InputFileIcon />
                </div>
                Drag and drop photos here
              </button>
              <div className="text-sm text-gray-500">
                or click to browse your files
              </div>
            </div>
            <input
              ref={fileInputRef}
              type="file"
              accept="image/jpeg,image/jpg,image/png"
              multiple
              onChange={e => handleFiles(e.target.files)}
              className="hidden"
            />
          </div>
          <p className="text-xs text-gray-500 mt-3">
            Supported formats: JPG, PNG. Max size: 10MB per file.
          </p>
        </div>
        {photos.length > 0 && (
          <PhotoGallery
            data={photos}
            className="max-w-[552px]"
            onAdd={handleAddPhotos}
            onCheckedChange={handleCheckedChange}
            onRemove={handleRemove}
            onEdit={handleEdit}
            onChange={handleChange}
            onReorder={handleReorder}
          />
        )}
      </div>
      <EditImageTagsModal
        open={editTagOpen}
        onOpenChange={setEditTagOpen}
        onSave={handleEditImageTags}
        tags={
          tagsSelected?.map(tag => ({
            value: tag.id,
            label: tag.name,
            color: tag.color,
          })) ?? []
        }
      />
      <BulkAddTagsModal
        open={bulkAddTagsOpen}
        onOpenChange={setBulkAddTagsOpen}
        onSave={handleBulkAddTags}
      />
    </>
  );
}
