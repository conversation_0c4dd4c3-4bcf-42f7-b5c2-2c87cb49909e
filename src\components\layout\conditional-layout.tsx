'use client';

import { usePathname } from 'next/navigation';
import { AppLayout } from './app-layout';
import { PublicLayout } from './public-layout';
import { PUBLIC_ROUTES, Routes } from '@/lib/routes';
import { useMemo } from 'react';

interface ConditionalLayoutProps {
  children: React.ReactNode;
}

export function ConditionalLayout({ children }: ConditionalLayoutProps) {
  const pathname = usePathname();

  // Memoize the route checking to prevent unnecessary re-renders
  const isPublicRoute = useMemo(() => {
    return PUBLIC_ROUTES.includes(pathname as Routes);
  }, [pathname]);

  if (isPublicRoute) {
    return <PublicLayout>{children}</PublicLayout>;
  }

  return <AppLayout>{children}</AppLayout>;
}
