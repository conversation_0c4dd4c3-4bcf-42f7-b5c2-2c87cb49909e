'use client';

import * as React from 'react';
import { useState } from 'react';
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Tag } from '@/types/tag';
import { colorOptions } from './AddTagModal';

interface EditTagModalProps {
  tag: Tag | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSave: (tagData: Partial<Tag>) => void;
  typeOptions: { value: string; label: string }[];
}

interface ValidationErrors {
  name?: string;
  type?: string;
  color?: string;
}

export default function EditTagModal({
  tag,
  open,
  onOpenChange,
  onSave,
  typeOptions,
}: EditTagModalProps) {
  const [formData, setFormData] = React.useState<Partial<Tag>>({
    name: '',
    type: '',
    color: '',
  });
  const [errors, setErrors] = useState<ValidationErrors>({});
  const [touched, setTouched] = useState<
    Record<keyof ValidationErrors, boolean>
  >({
    name: false,
    type: false,
    color: false,
  });

  const validateField = (
    field: keyof Tag,
    value: string
  ): string | undefined => {
    switch (field) {
      case 'name':
        if (!value.trim()) return 'The tag name is required';
        break;
      case 'type':
        if (!value) return 'The tag type is required';
        break;
    }
    return undefined;
  };

  const validateForm = (): boolean => {
    const newErrors: ValidationErrors = {};
    let isValid = true;

    (Object.keys(formData) as Array<keyof ValidationErrors>).forEach(key => {
      const field = key;
      const error = validateField(field as keyof Tag, formData[field] || '');
      if (error) {
        newErrors[field] = error;
        isValid = false;
      }
    });

    setErrors(newErrors);
    return isValid;
  };

  const handleFieldChange = (field: keyof ValidationErrors, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    setTouched(prev => ({ ...prev, [field]: true }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const handleFieldBlur = (field: keyof ValidationErrors) => {
    setTouched(prev => ({ ...prev, [field]: true }));
    const error = validateField(field as keyof Tag, formData[field] || '');
    setErrors(prev => ({ ...prev, [field]: error }));
  };

  React.useEffect(() => {
    if (tag) {
      setFormData({
        name: tag.name,
        type: tag.type,
        color: tag.color,
      });
      // Reset validation state when tag changes
      setErrors({});
      setTouched({
        name: false,
        type: false,
        color: false,
      });
    }
  }, [tag]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!tag) return;

    setTouched({
      name: true,
      type: true,
      color: true,
    });

    if (!validateForm()) {
      return;
    }

    onSave({ ...tag, ...formData });
    onOpenChange(false);
  };

  const handleInputChange = (field: keyof ValidationErrors, value: string) => {
    handleFieldChange(field, value);
  };

  const resetForm = () => {
    if (tag) {
      setFormData({
        name: tag.name,
        type: tag.type,
        color: tag.color,
      });
    }
    setErrors({});
    setTouched({
      name: false,
      type: false,
      color: false,
    });
  };

  const handleOpenChange = (open: boolean) => {
    if (!open) {
      resetForm();
    }
    onOpenChange(open);
  };

  if (!tag) return null;

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-[329px]">
        <DialogHeader>
          <DialogTitle>Edit Tag</DialogTitle>
          <DialogDescription>Edit the tag’s details below.</DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="flex items-center space-x-4">
            <div className="flex-1">
              <Label
                htmlFor="name"
                className="text-sm font-medium text-foreground"
              >
                Tag name
              </Label>
              <Input
                id="name"
                value={formData.name}
                onChange={e => handleInputChange('name', e.target.value)}
                onBlur={() => handleFieldBlur('name')}
                placeholder="Enter tag name"
                className={`mt-1 ${
                  touched.name && errors.name
                    ? 'border-red-500 focus:border-red-500'
                    : ''
                }`}
              />
              {touched.name && errors.name && (
                <p className="text-sm text-red-500 mt-1">{errors.name}</p>
              )}
            </div>
          </div>

          <div className="space-y-2 w-full">
            <Label
              htmlFor="type"
              className="text-sm font-medium text-foreground"
            >
              Tag type
            </Label>
            <Select
              value={formData.type}
              onValueChange={value => handleFieldChange('type', value)}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select tag's type" />
              </SelectTrigger>
              <SelectContent>
                {typeOptions.map(type => (
                  <SelectItem
                    className="capitalize"
                    key={type.value}
                    value={type.value}
                  >
                    {type.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {touched.type && errors.type && (
              <p className="text-sm text-red-500 mt-1">{errors.type}</p>
            )}
          </div>

          <div className="space-y-2 w-full">
            <Label
              htmlFor="color"
              className="text-sm font-medium text-foreground"
            >
              Color (optional)
            </Label>
            <Select
              value={formData.color}
              onValueChange={value => handleFieldChange('color', value)}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Pick a color for this tag" />
              </SelectTrigger>
              <SelectContent>
                {colorOptions.map(color => (
                  <SelectItem
                    className="capitalize"
                    key={color.value}
                    value={color.value}
                  >
                    {color.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <DialogFooter className="mt-6">
            <Button
              type="button"
              variant="outline"
              onClick={() => handleOpenChange(false)}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={Object.keys(errors).some(
                key => errors[key as keyof ValidationErrors]
              )}
            >
              Save Changes
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
