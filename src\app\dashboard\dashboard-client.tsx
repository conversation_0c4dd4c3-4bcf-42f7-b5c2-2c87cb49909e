'use client';

import { useAuth } from '@/contexts/auth-context';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { ActivityBadge } from '@/components/ui/activity-badge';
import Link from 'next/link';
import { Routes } from '@/lib/routes';
import { Skeleton } from '@/components/ui/skeleton';
import { lazy, Suspense } from 'react';
import { Role } from '@/types/enum';

// Lazy load charts to reduce initial bundle size and improve performance
const DashboardCharts = lazy(() => import('./dashboard-charts'));

// Sample user activities
const recentActivities = [
  {
    id: 1,
    type: 'login',
    description: 'Signed in to the platform',
    timestamp: 'Just now',
    icon: '🔐',
  },
  {
    id: 2,
    type: 'profile_update',
    description: 'Updated profile information',
    timestamp: '2 hours ago',
    icon: '👤',
  },
  {
    id: 3,
    type: 'property_added',
    description: 'Added new property listing',
    timestamp: '1 day ago',
    icon: '🏠',
  },
  {
    id: 4,
    type: 'booking_made',
    description: 'Made a booking for location scouting',
    timestamp: '2 days ago',
    icon: '📅',
  },
  {
    id: 5,
    type: 'report_submitted',
    description: 'Submitted location scouting report',
    timestamp: '3 days ago',
    icon: '📋',
  },
];

// Sample statistics based on user role
const getRoleStats = (role: string) => {
  switch (role) {
    case 'admin':
      return [
        { label: 'Total Users', value: '1,247', change: '+12%', trend: 'up' },
        {
          label: 'Active Properties',
          value: '856',
          change: '+8%',
          trend: 'up',
        },
        {
          label: 'Total Revenue',
          value: '$45,230',
          change: '+15%',
          trend: 'up',
        },
        {
          label: 'System Health',
          value: '99.9%',
          change: '+0.1%',
          trend: 'up',
        },
      ];
    case 'property_owner':
      return [
        { label: 'My Properties', value: '12', change: '+2', trend: 'up' },
        { label: 'Active Bookings', value: '8', change: '+3', trend: 'up' },
        {
          label: 'Monthly Revenue',
          value: '$3,240',
          change: '+18%',
          trend: 'up',
        },
        { label: 'Avg. Rating', value: '4.8/5', change: '+0.2', trend: 'up' },
      ];
    case 'location_scout':
      return [
        {
          label: 'Searches This Month',
          value: '45',
          change: '+12',
          trend: 'up',
        },
        { label: 'Saved Locations', value: '23', change: '+5', trend: 'up' },
        { label: 'Reports Submitted', value: '8', change: '+2', trend: 'up' },
        { label: 'Success Rate', value: '92%', change: '+3%', trend: 'up' },
      ];
    default:
      return [];
  }
};

// Loading skeleton component
function DashboardSkeleton() {
  return (
    <div className="flex flex-1">
      <div className="w-full">
        {/* Statistics Cards Skeleton */}
        <div className="grid gap-6 mb-8 md:grid-cols-2 lg:grid-cols-4">
          {Array.from({ length: 4 }).map((_, index) => (
            <Card key={index} className="hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div className="space-y-2">
                    <Skeleton className="h-4 w-24" />
                    <Skeleton className="h-8 w-16" />
                  </div>
                  <Skeleton className="h-4 w-12" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Charts Section Skeleton */}
        <div className="grid gap-6 mb-8 lg:grid-cols-2">
          {Array.from({ length: 2 }).map((_, index) => (
            <Card key={index}>
              <CardHeader>
                <Skeleton className="h-6 w-32" />
                <Skeleton className="h-4 w-48" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-[300px] w-full" />
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Recent Activities and Quick Actions Skeleton */}
        <div className="grid gap-6 lg:grid-cols-3">
          <Card className="lg:col-span-2">
            <CardHeader>
              <Skeleton className="h-6 w-32" />
              <Skeleton className="h-4 w-48" />
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {Array.from({ length: 5 }).map((_, index) => (
                  <div key={index} className="flex items-center space-x-3 p-3">
                    <Skeleton className="h-8 w-8 rounded-full" />
                    <div className="flex-1 space-y-2">
                      <Skeleton className="h-4 w-48" />
                      <Skeleton className="h-3 w-24" />
                    </div>
                    <Skeleton className="h-6 w-20" />
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <Skeleton className="h-6 w-32" />
              <Skeleton className="h-4 w-48" />
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {Array.from({ length: 4 }).map((_, index) => (
                  <Skeleton key={index} className="h-10 w-full" />
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}

// Chart loading fallback
function ChartSkeleton() {
  return (
    <div className="grid gap-6 mb-8 lg:grid-cols-2">
      {Array.from({ length: 2 }).map((_, index) => (
        <Card key={index}>
          <CardHeader>
            <Skeleton className="h-6 w-32" />
            <Skeleton className="h-4 w-48" />
          </CardHeader>
          <CardContent>
            <Skeleton className="h-[300px] w-full" />
          </CardContent>
        </Card>
      ))}
    </div>
  );
}

export default function DashboardClient() {
  const { user, loading } = useAuth();
  const stats = getRoleStats(user?.role || '');

  // Show loading skeleton while auth is loading
  if (loading) {
    return <DashboardSkeleton />;
  }

  return (
    <div className="flex flex-1">
      <div className="w-full">
        {/* Statistics Cards */}
        <div className="grid gap-6 mb-8 md:grid-cols-2 lg:grid-cols-4">
          {stats.map((stat, index) => (
            <Card key={index} className="hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium">{stat.label}</p>
                    <p className="text-2xl font-bold mt-1">{stat.value}</p>
                  </div>
                  <div
                    className={`text-sm font-medium ${
                      stat.trend === 'up' ? 'text-green-600' : 'text-red-600'
                    }`}
                  >
                    {stat.change}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Charts Section - Lazy Loaded */}
        <Suspense fallback={<ChartSkeleton />}>
          <DashboardCharts />
        </Suspense>

        {/* Recent Activities and Quick Actions */}
        <div className="grid gap-6 lg:grid-cols-3">
          {/* Recent Activities */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle>Recent Activities</CardTitle>
              <CardDescription>Your latest actions and updates</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentActivities.map(activity => (
                  <div
                    key={activity.id}
                    className="flex items-center space-x-3 p-3 rounded-lg transition-colors"
                  >
                    <div className="text-2xl">{activity.icon}</div>
                    <div className="flex-1">
                      <p className="font-medium">{activity.description}</p>
                      <p className="text-sm ">{activity.timestamp}</p>
                    </div>
                    <ActivityBadge
                      activityType={activity.type}
                      className="text-xs"
                    >
                      {activity.type.replace('_', ' ')}
                    </ActivityBadge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
              <CardDescription>Common tasks and shortcuts</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <Link href={Routes.PROFILE}>
                  <Button
                    variant="outline"
                    className="w-full justify-start mb-3"
                  >
                    👤 Edit Profile
                  </Button>
                </Link>
                <Button variant="outline" className="w-full justify-start">
                  ⚙️ Settings
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  📊 View Analytics
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  💬 Help & Support
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Role-Specific Sections */}
        {user?.role === Role.ADMIN && (
          <Card className="mt-6">
            <CardHeader>
              <CardTitle>Admin Panel</CardTitle>
              <CardDescription>
                Administrative functions and system management
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                <Button variant="outline" className="h-20 flex-col">
                  <span className="text-2xl mb-2">👥</span>
                  Manage Users
                </Button>
                <Button variant="outline" className="h-20 flex-col">
                  <span className="text-2xl mb-2">⚙️</span>
                  System Settings
                </Button>
                <Button variant="outline" className="h-20 flex-col">
                  <span className="text-2xl mb-2">📋</span>
                  View Logs
                </Button>
                <Button variant="outline" className="h-20 flex-col">
                  <span className="text-2xl mb-2">💾</span>
                  Backup Data
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {user?.role === Role.VIEWER && (
          <Card className="mt-6">
            <CardHeader>
              <CardTitle>Property Management</CardTitle>
              <CardDescription>
                Manage your properties and bookings
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                <Button variant="outline" className="h-20 flex-col">
                  <span className="text-2xl mb-2">🏠</span>
                  Add Property
                </Button>
                <Button variant="outline" className="h-20 flex-col">
                  <span className="text-2xl mb-2">📋</span>
                  View Properties
                </Button>
                <Button variant="outline" className="h-20 flex-col">
                  <span className="text-2xl mb-2">📊</span>
                  Property Analytics
                </Button>
                <Button variant="outline" className="h-20 flex-col">
                  <span className="text-2xl mb-2">📅</span>
                  Manage Bookings
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {user?.role === Role.SCOUT && (
          <Card className="mt-6">
            <CardHeader>
              <CardTitle>Scouting Tools</CardTitle>
              <CardDescription>
                Location scouting features and tools
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                <Button variant="outline" className="h-20 flex-col">
                  <span className="text-2xl mb-2">🔍</span>
                  Search Locations
                </Button>
                <Button variant="outline" className="h-20 flex-col">
                  <span className="text-2xl mb-2">❤️</span>
                  Save Favorites
                </Button>
                <Button variant="outline" className="h-20 flex-col">
                  <span className="text-2xl mb-2">📝</span>
                  Submit Reports
                </Button>
                <Button variant="outline" className="h-20 flex-col">
                  <span className="text-2xl mb-2">📚</span>
                  View History
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
