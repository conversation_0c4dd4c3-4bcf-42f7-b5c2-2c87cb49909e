{"naming": {"components": "PascalCase", "hooks": "camelCase", "utils": "camelCase", "reservedRouteFiles": "lowercase"}, "patterns": {"**/page.tsx": "lowercase", "**/layout.tsx": "lowercase", "**/error.tsx": "lowercase", "**/loading.tsx": "lowercase", "**/not-found.tsx": "lowercase", "**/route.ts": "lowercase", "components/**/*.tsx": "PascalCase", "app/**/!(page|layout|error|loading|not-found|route).tsx": "PascalCase", "hooks/**/*.ts": "camelCase", "lib/**/*.ts": "camelCase"}}