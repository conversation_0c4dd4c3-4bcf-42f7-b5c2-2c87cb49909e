'use client';

import * as Sentry from '@sentry/nextjs';
import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import {
  AlertTriangle,
  CheckCircle,
  ExternalLink,
  Bug,
  Activity,
  Shield,
  Zap,
  Eye,
} from 'lucide-react';

class SentryExampleFrontendError extends Error {
  constructor(message: string | undefined) {
    super(message);
    this.name = 'SentryExampleFrontendError';
  }
}

export default function SentryExamplePage() {
  const [hasSentError, setHasSentError] = useState(false);
  const [isConnected, setIsConnected] = useState(true);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    async function checkConnectivity() {
      const result = await Sentry.diagnoseSdkConnectivity();
      setIsConnected(result !== 'sentry-unreachable');
    }
    checkConnectivity();
  }, []);

  const handleThrowError = async () => {
    setIsLoading(true);
    try {
      await Sentry.startSpan(
        {
          name: 'Example Frontend/Backend Span',
          op: 'test',
        },
        async () => {
          const res = await fetch('/nextapi/sentry-example-api');
          if (!res.ok) {
            setHasSentError(true);
          }
        }
      );
      throw new SentryExampleFrontendError(
        'This error is raised on the frontend of the example page.'
      );
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
    } catch (error) {
      setHasSentError(true);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center mb-4">
            <div className="p-3 bg-primary/10 rounded-full">
              <Bug className="h-8 w-8 text-primary" />
            </div>
          </div>
          <h1 className="text-3xl font-bold tracking-tight mb-2">
            Sentry Error Monitoring Test
          </h1>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            Test your Sentry integration by generating sample errors and
            monitoring performance. This page helps you verify that error
            reporting and performance monitoring are working correctly.
          </p>
        </div>

        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 mb-8">
          {/* Connection Status */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center gap-2 text-sm">
                <Activity className="h-4 w-4" />
                Connection Status
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-2">
                {isConnected ? (
                  <>
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <Badge
                      variant="secondary"
                      className="bg-green-100 text-green-800"
                    >
                      Connected
                    </Badge>
                  </>
                ) : (
                  <>
                    <AlertTriangle className="h-4 w-4 text-red-500" />
                    <Badge variant="destructive">Disconnected</Badge>
                  </>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Error Status */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center gap-2 text-sm">
                <Bug className="h-4 w-4" />
                Error Status
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-2">
                {hasSentError ? (
                  <>
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <Badge
                      variant="secondary"
                      className="bg-green-100 text-green-800"
                    >
                      Error Sent
                    </Badge>
                  </>
                ) : (
                  <>
                    <AlertTriangle className="h-4 w-4 text-yellow-500" />
                    <Badge variant="outline">No Errors</Badge>
                  </>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Environment */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center gap-2 text-sm">
                <Shield className="h-4 w-4" />
                Environment
              </CardTitle>
            </CardHeader>
            <CardContent>
              <Badge variant="outline">
                {process.env.NEXT_PUBLIC_SENTRY_ENVIRONMENT || 'development'}
              </Badge>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <div className="max-w-2xl mx-auto">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="h-5 w-5" />
                Test Error Reporting
              </CardTitle>
              <CardDescription>
                Click the button below to generate a sample error and test your
                Sentry integration. The error will be sent to your Sentry
                dashboard for monitoring.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Button
                onClick={handleThrowError}
                disabled={!isConnected || isLoading}
                className="w-full"
                size="lg"
              >
                {isLoading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                    Generating Error...
                  </>
                ) : (
                  <>
                    <Bug className="h-4 w-4 mr-2" />
                    Throw Sample Error
                  </>
                )}
              </Button>

              {!isConnected && (
                <Alert variant="destructive">
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    Network requests to Sentry are being blocked. This will
                    prevent errors from being captured. Try disabling your
                    ad-blocker to complete the test.
                  </AlertDescription>
                </Alert>
              )}

              {hasSentError && (
                <Alert>
                  <CheckCircle className="h-4 w-4" />
                  <AlertDescription>
                    Error successfully sent to Sentry! Check your dashboard to
                    see the captured error.
                  </AlertDescription>
                </Alert>
              )}
            </CardContent>
          </Card>

          <Separator className="my-8" />

          {/* Links */}
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-sm">
                  <Eye className="h-4 w-4" />
                  View Issues
                </CardTitle>
              </CardHeader>
              <CardContent>
                <Button variant="outline" className="w-full" asChild>
                  <a
                    target="_blank"
                    rel="noopener noreferrer"
                    href="https://scoutr-v5.sentry.io/issues/?project=****************"
                  >
                    <ExternalLink className="h-4 w-4 mr-2" />
                    Sentry Issues Page
                  </a>
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-sm">
                  <Shield className="h-4 w-4" />
                  Documentation
                </CardTitle>
              </CardHeader>
              <CardContent>
                <Button variant="outline" className="w-full" asChild>
                  <a
                    target="_blank"
                    rel="noopener noreferrer"
                    href="https://docs.sentry.io/platforms/javascript/guides/nextjs/"
                  >
                    <ExternalLink className="h-4 w-4 mr-2" />
                    Sentry Docs
                  </a>
                </Button>
              </CardContent>
            </Card>
          </div>

          {/* Features */}
          <div className="mt-8">
            <h2 className="text-xl font-semibold mb-4">
              What&apos;s Being Tested
            </h2>
            <div className="grid gap-3">
              <div className="flex items-center gap-3 p-3 rounded-lg border">
                <CheckCircle className="h-4 w-4 text-green-500 flex-shrink-0" />
                <div>
                  <p className="font-medium">Error Capture</p>
                  <p className="text-sm text-muted-foreground">
                    JavaScript errors are automatically captured and sent to
                    Sentry
                  </p>
                </div>
              </div>
              <div className="flex items-center gap-3 p-3 rounded-lg border">
                <CheckCircle className="h-4 w-4 text-green-500 flex-shrink-0" />
                <div>
                  <p className="font-medium">Performance Monitoring</p>
                  <p className="text-sm text-muted-foreground">
                    API calls and user interactions are tracked for performance
                    analysis
                  </p>
                </div>
              </div>
              <div className="flex items-center gap-3 p-3 rounded-lg border">
                <CheckCircle className="h-4 w-4 text-green-500 flex-shrink-0" />
                <div>
                  <p className="font-medium">Session Replay</p>
                  <p className="text-sm text-muted-foreground">
                    User sessions are recorded to help debug issues in context
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
