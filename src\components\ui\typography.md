# Typography System

A comprehensive typography system built with Tailwind CSS and class-variance-authority for consistent text styling across your application.

## Features

- **Semantic variants**: Display, headings, body text, and special text variants
- **Flexible styling**: Color, alignment, and weight options
- **Accessibility**: Proper HTML element mapping for screen readers
- **TypeScript support**: Full type safety with proper prop types
- **Responsive design**: Mobile-first approach with responsive typography
- **Theme support**: Works with your existing light/dark theme system

## Quick Start

```tsx
import { Typography, Heading, Body, Lead } from '@/components/ui/typography';

// Basic usage
<Typography variant="h1">Main Heading</Typography>
<Body>Regular paragraph text</Body>
<Lead>Introductory text that stands out</Lead>

// With convenience components
<Heading level={1}>Main Heading</Heading>
<Body size="lg">Large body text</Body>
```

## Variants

### Display Variants

For large, impactful headings:

- `display-2xl` - Extra large display text
- `display-xl` - Large display text
- `display-lg` - Medium display text
- `display-md` - Small display text
- `display-sm` - Extra small display text
- `display-xs` - Smallest display text

### Heading Variants

For section headings:

- `h1` through `h6` - Semantic heading levels

### Body Text Variants

For regular content:

- `body-xl` - Extra large body text
- `body-lg` - Large body text
- `body-md` - Default body text
- `body-sm` - Small body text
- `body-xs` - Extra small body text

### Special Variants

- `lead` - Introductory paragraphs
- `large` - Emphasized text
- `small` - Secondary information
- `muted` - Less important content
- `code` - Code blocks
- `inline-code` - Inline code
- `link` - Styled links
- `blockquote` - Quoted text
- `list` - Unordered lists

## Colors

- `default` - Default text color
- `header` - Header text color (used by default for headings)
- `sub-header` - Sub-header text color
- `muted` - Muted text color
- `primary` - Primary brand color
- `secondary` - Secondary color
- `accent` - Accent color
- `destructive` - Error/danger color
- `success` - Success color
- `warning` - Warning color
- `info` - Information color

## Alignment

- `left` - Left aligned (default)
- `center` - Center aligned
- `right` - Right aligned
- `justify` - Justified text

## Font Weights

- `thin` - Font weight 100
- `light` - Font weight 300
- `normal` - Font weight 400 (default)
- `medium` - Font weight 500
- `semibold` - Font weight 600
- `bold` - Font weight 700
- `extrabold` - Font weight 800
- `black` - Font weight 900

## Convenience Components

For common use cases, use these convenience components:

```tsx
import {
  Display,
  Heading,
  Body,
  Lead,
  Large,
  Small,
  Muted,
  Code,
  InlineCode,
  Link,
  Blockquote,
  List
} from '@/components/ui/typography';

<Display>Large display text</Display>
<Heading level={2}>Section heading</Heading>
<Body size="lg">Body text</Body>
<Lead>Introductory text</Lead>
<Large>Emphasized text</Large>
<Small>Small text</Small>
<Muted>Muted text</Muted>
<Code>Code block</Code>
<InlineCode>inline code</InlineCode>
<Link href="/path">Link text</Link>
<Blockquote>Quoted text</Blockquote>
<List>
  <li>List item 1</li>
  <li>List item 2</li>
</List>
```

## Custom HTML Elements

Override the default HTML element:

```tsx
<Typography variant="h1" as="div">Heading styled as div</Typography>
<Typography variant="body-md" as="span">Body text as span</Typography>
```

## Examples

See `typography-examples.tsx` for comprehensive usage examples.

## Migration from Existing Code

Replace existing text styling:

```tsx
// Before
<h1 className="text-4xl font-bold">Title</h1>
<p className="text-lg text-muted-foreground">Description</p>

// After
<Typography variant="h1">Title</Typography>
<Typography variant="body-lg" color="muted">Description</Typography>
```

## Best Practices

1. **Use semantic variants**: Choose variants that match the content's purpose
2. **Maintain hierarchy**: Use heading levels in order (h1 → h2 → h3)
3. **Consistent spacing**: Let the typography system handle spacing
4. **Accessibility**: The system automatically maps to appropriate HTML elements
5. **Theme support**: Colors automatically adapt to light/dark themes
