import { Skeleton } from '@/components/ui/skeleton';
export default function Loading() {
  return (
    <div className="w-full flex justify-center animate-pulse">
      <div className="space-y-6 xl:w-[1128px] p-6 xl:px-0">
        {/* Image Grid Skeleton */}
        <div className="grid gap-2 sm:gap-6 grid-cols-2 sm:grid-cols-4 grid-rows-2">
          {/* Main Image Skeleton */}
          <Skeleton className="row-span-2 col-span-2 rounded-xl overflow-hidden aspect-[552/337]" />

          {/* Smaller Images Skeletons */}
          {[...Array(4)].map((_, i) => (
            <Skeleton
              key={i}
              className="rounded-xl overflow-hidden sm:h-auto aspect-[263/157]"
            />
          ))}
        </div>

        {/* Header Section Skeleton */}
        <div className="flex justify-between items-start">
          <div className="pt-4 pb-3 flex-1 flex flex-col gap-2">
            {/* Title Skeleton */}
            <Skeleton className="h-8 rounded w-3/4" />
            {/* Address Skeleton */}
            <Skeleton className="h-4 rounded w-1/2" />
            {/* Tags Skeleton */}
            <div className="mt-auto flex justify-start gap-1">
              <Skeleton className="h-6 w-20 rounded-full" />
              <Skeleton className="h-6 w-24 rounded-full" />
            </div>
          </div>
          {/* Button Skeleton */}
          <Skeleton className="w-[155px] h-12 rounded-md" />
        </div>

        {/* Description Skeleton */}
        <div className="space-y-2">
          <Skeleton className="h-4 rounded w-full" />
          <Skeleton className="h-4 rounded w-11/12" />
          <Skeleton className="h-4 rounded w-10/12" />
        </div>

        {/* Location Offers Skeleton */}
        <div className="mt-8">
          {/* Offers Heading Skeleton */}
          <Skeleton className="h-6 rounded w-1/3 mb-6" />
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-y-4 sm:gap-y-6 gap-x-8">
            {[...Array(8)].map((_, i) => (
              <div key={i} className="flex items-center space-x-3">
                <Skeleton className="w-5 h-5 rounded-full" />
                <Skeleton className="h-4 rounded w-40" />
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
