'use client';

import { Skeleton } from '@/components/ui/skeleton';

export default function Loading() {
  return (
    <div className="w-full space-y-6 mt-4">
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
        {Array.from({ length: 6 }).map((_, index) => (
          <div
            key={index}
            className="border rounded-md shadow-sm bg-white flex flex-col"
          >
            <div className="h-40 rounded-md bg-gray-200 flex items-center justify-center">
              <div className="h-40 w-full rounded-t-md">
                <Skeleton className="h-full w-full" />
              </div>
            </div>
            <div className="p-4 flex-1 flex flex-col gap-2">
              <Skeleton className="h-5 w-3/5" />
              <Skeleton className="h-4 w-2/5" />
              <Skeleton className="h-4 w-1/2" />
              <div className="flex justify-end">
                <Skeleton className="h-6 w-20" />
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
