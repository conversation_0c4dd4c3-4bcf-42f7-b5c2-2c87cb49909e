{"naming": {"components": "PascalCase", "hooks": "camelCase", "utils": "camelCase", "reservedRouteFiles": "lowercase"}, "patterns": {"**/page.tsx": "lowercase", "**/layout.tsx": "lowercase", "**/error.tsx": "lowercase", "**/loading.tsx": "lowercase", "**/not-found.tsx": "lowercase", "**/route.ts": "lowercase", "components/**/*.tsx": "PascalCase", "app/**/!(page|layout|error|loading|not-found|route).tsx": "PascalCase", "hooks/**/*.ts": "camelCase", "lib/**/*.ts": "camelCase"}, "conventions": {"components": "Use PascalCase for all React components (e.g., UserProfile.tsx, ChangePasswordDialog.tsx)", "hooks": "Use camelCase with 'use' prefix for custom hooks (e.g., useAuth.ts, useLocalStorage.ts)", "utils": "Use camelCase for utility functions and helpers (e.g., utils.ts, apiClient.ts)", "routes": "Use lowercase for Next.js route files (e.g., page.tsx, layout.tsx, route.ts)", "examples": {"components": ["UserProfile.tsx", "ChangePasswordDialog.tsx", "DashboardCard.tsx"], "hooks": ["useAuth.ts", "useMobile.ts", "useLocalStorage.ts"], "utils": ["utils.ts", "auth.ts", "apiClient.ts"], "routes": ["page.tsx", "layout.tsx", "route.ts"]}}}