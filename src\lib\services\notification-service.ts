import { httpClient } from '../http-client';

export interface Notification {
  id: string;
  title: string;
  message: string;
  type: 'info' | 'warning' | 'error' | 'success';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  status: 'unread' | 'read' | 'archived';
  userId: string;
  relatedEntityType?: 'asset' | 'location' | 'user' | 'system';
  relatedEntityId?: string;
  actionUrl?: string;
  createdAt: string;
  readAt?: string;
}

export interface CreateNotificationData {
  title: string;
  message: string;
  type: 'info' | 'warning' | 'error' | 'success';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  userId: string;
  relatedEntityType?: 'asset' | 'location' | 'user' | 'system';
  relatedEntityId?: string;
  actionUrl?: string;
}

export interface NotificationPreferences {
  email: boolean;
  push: boolean;
  sms: boolean;
  types: {
    info: boolean;
    warning: boolean;
    error: boolean;
    success: boolean;
  };
  priorities: {
    low: boolean;
    medium: boolean;
    high: boolean;
    urgent: boolean;
  };
}

/**
 * Notification Service - Handles all notification-related API operations
 */
export class NotificationService {
  /**
   * Get user notifications
   */
  async getNotifications(): Promise<Notification[]> {
    return httpClient.get<Notification[]>('/notifications');
  }

  /**
   * Get unread notifications count
   */
  async getUnreadCount(): Promise<{ count: number }> {
    return httpClient.get<{ count: number }>('/notifications/unread-count');
  }

  /**
   * Mark notification as read
   */
  async markAsRead(id: string): Promise<void> {
    return httpClient.patch<void>(`/notifications/${id}/read`);
  }

  /**
   * Mark all notifications as read
   */
  async markAllAsRead(): Promise<void> {
    return httpClient.patch<void>('/notifications/mark-all-read');
  }

  /**
   * Archive notification
   */
  async archiveNotification(id: string): Promise<void> {
    return httpClient.patch<void>(`/notifications/${id}/archive`);
  }

  /**
   * Delete notification
   */
  async deleteNotification(id: string): Promise<void> {
    return httpClient.delete<void>(`/notifications/${id}`);
  }

  /**
   * Create notification (admin/system only)
   */
  async createNotification(
    notificationData: CreateNotificationData
  ): Promise<Notification> {
    return httpClient.post<Notification>('/notifications', notificationData);
  }

  /**
   * Get notification preferences
   */
  async getPreferences(): Promise<NotificationPreferences> {
    return httpClient.get<NotificationPreferences>(
      '/notifications/preferences'
    );
  }

  /**
   * Update notification preferences
   */
  async updatePreferences(
    preferences: Partial<NotificationPreferences>
  ): Promise<NotificationPreferences> {
    return httpClient.put<NotificationPreferences>(
      '/notifications/preferences',
      preferences
    );
  }

  /**
   * Get notifications by type
   */
  async getNotificationsByType(
    type: 'info' | 'warning' | 'error' | 'success'
  ): Promise<Notification[]> {
    return httpClient.get<Notification[]>(`/notifications?type=${type}`);
  }

  /**
   * Get notifications by priority
   */
  async getNotificationsByPriority(
    priority: 'low' | 'medium' | 'high' | 'urgent'
  ): Promise<Notification[]> {
    return httpClient.get<Notification[]>(
      `/notifications?priority=${priority}`
    );
  }

  /**
   * Get notifications by status
   */
  async getNotificationsByStatus(
    status: 'unread' | 'read' | 'archived'
  ): Promise<Notification[]> {
    return httpClient.get<Notification[]>(`/notifications?status=${status}`);
  }
}

// Export singleton instance
export const notificationService = new NotificationService();
