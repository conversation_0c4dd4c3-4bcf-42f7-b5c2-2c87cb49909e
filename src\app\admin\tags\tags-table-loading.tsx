'use client';

import { Card, CardContent } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';

export default function TagsTableLoading() {
  return (
    <Card className="py-0 rounded-md shadow-sm gap-0">
      <CardContent className="px-0">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="px-6 bg-[#FAFAFA] rounded-md flex items-center space-x-4">
                <Skeleton className="h-4 w-4" />
                <div>Tag name</div>
              </TableHead>
              <TableHead className="px-6 bg-[#FAFAFA]">Tag type</TableHead>
              <TableHead className="px-6 bg-[#FAFAFA] rounded-md"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {Array.from({ length: 6 }).map((_, i) => (
              <TableRow className="h-20" key={`s-${i}`}>
                <TableCell className="px-6">
                  <div className="flex items-center gap-4">
                    <Skeleton className="h-4 w-4" />
                    <Skeleton className="h-4 w-32" />
                  </div>
                </TableCell>
                <TableCell>
                  <Skeleton className="h-4 w-48" />
                </TableCell>
                <TableCell>
                  <div className="flex items-center justify-end gap-2">
                    <Skeleton className="h-4 w-10" />
                    <Skeleton className="h-4 w-10" />
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
}
