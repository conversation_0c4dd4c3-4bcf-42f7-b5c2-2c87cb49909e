'use client';

import { useState, useEffect, useCallback, useMemo } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Eye, EyeOff, Loader2, Check } from 'lucide-react';
import { Body } from '@/components/ui/typography';
import { CircleCheck } from 'lucide-react';
import { CircleXOutlineIcon } from '@/lib/icons';

interface PasswordFormProps {
  termEnable: boolean;
  buttonText: string;
  onSubmit: (password: string, confirmPassword: string) => Promise<void>;
  isLoading?: boolean;
  error?: string | null;
  showSignInLink?: boolean;
  signInLinkText?: string;
  signInLinkHref?: string;
}

export default function PasswordForm({
  termEnable,
  buttonText,
  onSubmit,
  isLoading = false,
  error = null,
  showSignInLink = true,
  signInLinkText = 'Already have an account?',
  signInLinkHref = '/sign-in',
}: PasswordFormProps) {
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [acceptTerms, setAcceptTerms] = useState(false);
  const [confirmPasswordError, setConfirmPasswordError] = useState<
    string | null
  >(null);
  const [passwordError, setPasswordError] = useState<string | null>(null);

  const canSubmit = useMemo(() => {
    return (
      password.length > 0 &&
      confirmPassword.length > 0 &&
      (termEnable ? acceptTerms : true) &&
      !passwordError &&
      !confirmPasswordError
    );
  }, [
    password,
    confirmPassword,
    termEnable,
    acceptTerms,
    passwordError,
    confirmPasswordError,
  ]);

  const getPasswordRequirements = (password: string) => {
    const minLength = 8;
    const hasUpperCase = /[A-Z]/.test(password);
    const hasLowerCase = /[a-z]/.test(password);
    const hasNumbers = /\d/.test(password);
    const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);

    return [
      {
        text: 'At least 8 characters',
        met: password.length >= minLength,
      },
      {
        text: 'One uppercase letter',
        met: hasUpperCase,
      },
      {
        text: 'One lowercase letter',
        met: hasLowerCase,
      },
      {
        text: 'One number',
        met: hasNumbers,
      },
      {
        text: 'One special character',
        met: hasSpecialChar,
      },
    ];
  };

  const validateConfirmPassword = (
    confirmPassword: string,
    password: string
  ) => {
    if (confirmPassword.length === 0) {
      return null; // Don't show error for empty field
    }
    if (confirmPassword !== password) {
      return 'Passwords do not match';
    }
    return null;
  };

  const validatePasswordRequirements = useCallback((password: string) => {
    if (password.length === 0) {
      return null; // Don't show error for empty field
    }
    const requirements = getPasswordRequirements(password);
    const unmetRequirements = requirements.filter(req => !req.met);
    if (unmetRequirements.length > 0) {
      return `Missing: ${unmetRequirements.map(req => req.text).join(', ')}`;
    }
    return null;
  }, []);

  // Real-time validation for password requirements
  useEffect(() => {
    const error = validatePasswordRequirements(password);
    setPasswordError(error);
  }, [password, validatePasswordRequirements]);

  // Real-time validation for confirm password
  useEffect(() => {
    const error = validateConfirmPassword(confirmPassword, password);
    setConfirmPasswordError(error);
  }, [confirmPassword, password]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (termEnable && !acceptTerms) {
      return;
    }

    if (passwordError) {
      return;
    }

    if (confirmPasswordError) {
      return;
    }

    await onSubmit(password, confirmPassword);
  };

  return (
    <form onSubmit={handleSubmit} className="w-full space-y-6">
      <div className="space-y-2">
        <Label htmlFor="password" className="text-header">
          Password*
        </Label>
        <div className="relative">
          <Input
            id="password"
            type={showPassword ? 'text' : 'password'}
            value={password}
            onChange={e => setPassword(e.target.value)}
            placeholder="Create a password"
            required
            className={`pr-10 focus-visible:ring-0 ${
              passwordError
                ? 'border-red-500 focus-visible:border-red-500'
                : password.length > 0 && !passwordError
                  ? 'border-green-500 focus-visible:border-green-500'
                  : 'border-border'
            }`}
          />
          <div className="absolute right-0 top-0 h-full flex items-center gap-1">
            {password.length > 0 && !passwordError && (
              <Check className="h-4 w-4 text-green-600" />
            )}
            <Button
              type="button"
              variant="ghost"
              size="icon"
              className="h-full px-3 hover:bg-transparent"
              onClick={() => setShowPassword(!showPassword)}
              tabIndex={-1}
            >
              {showPassword ? (
                <EyeOff className="h-4 w-4 text-foreground" />
              ) : (
                <Eye className="h-4 w-4 text-border" />
              )}
            </Button>
          </div>
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="confirmPassword" className="text-header">
          Confirm Password*
        </Label>
        <div className="relative">
          <Input
            id="confirmPassword"
            type={showConfirmPassword ? 'text' : 'password'}
            value={confirmPassword}
            onChange={e => setConfirmPassword(e.target.value)}
            placeholder="Confirm your password"
            required
            className={`pr-20 focus-visible:ring-0 ${
              confirmPasswordError
                ? 'border-red-500 focus-visible:border-red-500'
                : confirmPassword.length > 0 && !confirmPasswordError
                  ? 'border-green-500 focus-visible:border-green-500'
                  : 'border-border'
            }`}
          />
          <div className="absolute right-0 top-0 h-full flex items-center gap-1">
            {confirmPassword.length > 0 && !confirmPasswordError && (
              <Check className="h-4 w-4 text-green-600" />
            )}
            <Button
              type="button"
              variant="ghost"
              size="icon"
              className="h-full px-3 hover:bg-transparent"
              onClick={() => setShowConfirmPassword(!showConfirmPassword)}
              tabIndex={-1}
            >
              {showConfirmPassword ? (
                <EyeOff className="h-4 w-4 text-foreground" />
              ) : (
                <Eye className="h-4 w-4 text-border" />
              )}
            </Button>
          </div>
        </div>
        {confirmPasswordError && (
          <p className="text-sm text-red-600">{confirmPasswordError}</p>
        )}
      </div>

      {/* Password Requirements */}
      <Card className="shadow-none border-none bg-card-background gap-3 py-4">
        <CardHeader className="px-4 gap-0">
          <CardTitle className="text-sub-header text-sm font-normal">
            Password Requirements
          </CardTitle>
        </CardHeader>
        <CardContent className="px-4">
          <div className="space-y-2">
            {getPasswordRequirements(password).map((requirement, index) => (
              <div key={index} className="flex items-center gap-2">
                {requirement.met ? (
                  <CircleCheck className="h-4 w-4 text-foreground" />
                ) : (
                  <CircleXOutlineIcon className="h-4 w-4 text-border" />
                )}
                <span className="text-sm">{requirement.text}</span>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Terms and Conditions */}
      {termEnable && (
        <div className="flex gap-2">
          <Checkbox
            id="terms"
            checked={acceptTerms}
            onCheckedChange={checked => setAcceptTerms(checked === true)}
            className="mt-1"
          />
          <Label
            htmlFor="terms"
            className="text-sm text-foreground leading-relaxed"
          >
            Accept
            <span className="underline cursor-pointer">
              terms and conditions
            </span>
          </Label>
        </div>
      )}

      <Button
        type="submit"
        className="w-full bg-primary hover:bg-primary/90 text-primary-foreground py-3 h-12"
        disabled={isLoading || !canSubmit}
      >
        {isLoading ? (
          <>
            <Loader2 className="h-5 w-5 animate-spin mr-2" />
            {buttonText}...
          </>
        ) : (
          buttonText
        )}
      </Button>

      {error && <div className="text-sm text-red-600 text-center">{error}</div>}

      {showSignInLink && (
        <Body className="text-sm text-foreground text-center">
          {signInLinkText}{' '}
          <a href={signInLinkHref} className="underline cursor-pointer">
            Sign in
          </a>
        </Body>
      )}
    </form>
  );
}
