import { Tag } from './tag';

export interface Photo {
  id?: string;
  key?: string;
  url: string;
  order: number;
  tags: Tag[];
}

export interface Location {
  id: string;
  title: string;
  name: string;
  address: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
  latitude?: number;
  longitude?: number;
  description?: string;
  coverImageUrl: string | null;
  status: string;
  createdAt: string;
  updatedAt: string;
  avatar?: string;
  tags: Tag[];
  comments?: number;
  images: Photo[];
}

export interface LocationRequest {
  title: string;
  address: string;
  description: string;
  categories: string[];
  subCategory: string;
  style: string[];
  size: string;
  images: Photo[];
  latitude?: number;
  longitude?: number;
}
