/* eslint-disable @typescript-eslint/no-unused-vars */
import { FileUpload } from '@/types';
import { Plus } from 'lucide-react';
import { useRef, useCallback, useState, useEffect, useMemo } from 'react';
import { CheckedState } from '@radix-ui/react-checkbox';
import {
  closestCenter,
  DndContext,
  DragEndEvent,
  PointerSensor,
  useSensor,
  useSensors,
  Modifier,
} from '@dnd-kit/core';
import { GalleryItem } from './GalleryItem';
import {
  SortableContext,
  rectSortingStrategy,
  useSortable,
  arrayMove,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { Props, Sortable } from './Sortable';
import { GridContainer } from './GridContainer';
// Simple UUID generator
const generateUUID = () => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    const r = (Math.random() * 16) | 0;
    const v = c === 'x' ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
};

interface SortableItemProps {
  id: string;
  photo: FileUpload;
  onEdit?: () => void;
  onRemove?: () => void;
  onCheckedChange?: (checked: CheckedState) => void;
  onUploadComplete?: (file: FileUpload) => void;
  onUploadError?: () => void;
}

function SortableItem({
  id,
  photo,
  onEdit,
  onRemove,
  onCheckedChange,
  onUploadComplete,
  onUploadError,
}: SortableItemProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id,
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      {...attributes}
      {...listeners}
      className={`relative group cursor-grab active:cursor-grabbing w-full h-full ${
        isDragging ? 'z-50' : ''
      }`}
      onMouseDown={e => {
        // Prevent dragging when clicking on interactive elements
        const target = e.target as HTMLElement;
        if (
          target.closest('input[type="checkbox"]') ||
          target.closest('[role="menuitem"]') ||
          target.closest('button') ||
          target.closest('[data-radix-collection-item]')
        ) {
          e.stopPropagation();
        }
      }}
    >
      <GalleryItem
        photo={photo}
        onEdit={onEdit}
        onRemove={onRemove}
        onCheckedChange={onCheckedChange}
        onUploadComplete={onUploadComplete}
        onUploadError={onUploadError}
      />
    </div>
  );
}

const props: Partial<Props> = {
  adjustScale: false,
  Container: (props: { children: React.ReactNode }) => (
    <GridContainer {...props} columns={2} />
  ),
  strategy: rectSortingStrategy,
  wrapperStyle: () => ({
    width: '100%',
    height: '100%',
  }),
  isDisabled: (id: string) => !id,
  useDragOverlay: true, // Disable drag overlay for better performance
  dropAnimation: null,
};

interface PhotoGalleryProps {
  data: FileUpload[];
  className?: string;
  onChange?: (index: number, file: FileUpload) => void;
  onAdd?: (files: FileUpload[]) => void;
  onCheckedChange?: (index: number, checked: CheckedState) => void;
  onRemove?: (index: number) => void;
  onEdit?: (index: number) => void;
  onReorder?: (reorderedData: FileUpload[]) => void;
}

export default function PhotoGallery({
  data,
  className,
  onCheckedChange,
  onAdd,
  onRemove,
  onEdit,
  onChange,
  onReorder,
}: PhotoGalleryProps) {
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Local state to track the current order of photos
  const [localPhotos, setLocalPhotos] = useState<FileUpload[]>(data);

  // Update local state when data prop changes
  useEffect(() => {
    setLocalPhotos(data);
  }, [data]);

  // Custom modifier to handle grid layout properly
  const gridModifier: Modifier = ({ transform }) => {
    // Simplified modifier - let the grid handle the layout
    return transform;
  };

  // Configure sensors for better drag experience
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    })
  );

  const handleUploadComplete = useCallback(
    (index: number) => (file: FileUpload) => {
      onChange?.(index, file);
    },
    [onChange]
  );

  const handleBrowseClick = useCallback(() => {
    fileInputRef.current?.click();
  }, []);

  // Memoize the callback functions to prevent unnecessary re-renders
  const createEditHandler = useCallback(
    (index: number) => {
      return () => onEdit?.(index);
    },
    [onEdit]
  );

  const createRemoveHandler = useCallback(
    (index: number) => {
      return () => onRemove?.(index);
    },
    [onRemove]
  );

  const createCheckedChangeHandler = useCallback(
    (index: number) => {
      return (checked: CheckedState) => onCheckedChange?.(index, checked);
    },
    [onCheckedChange]
  );

  const handleAdd = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) =>
      onAdd?.(
        Array.from(e.target.files || []).map(file => ({
          file,
          url: '',
          key: '',
          order: 0,
          tags: [],
        }))
      ),
    [onAdd]
  );

  const handleDragEnd = useCallback(
    (event: DragEndEvent) => {
      const { active, over } = event;

      if (active.id !== over?.id) {
        const oldIndex = data.findIndex(item => item.key === active.id);
        const newIndex = data.findIndex(item => item.key === over?.id);

        if (oldIndex !== -1 && newIndex !== -1) {
          const newData = [...data];
          const [reorderedItem] = newData.splice(oldIndex, 1);
          newData.splice(newIndex, 0, reorderedItem);

          // Update order property for each item
          const reorderedWithOrder = newData.map((item, index) => ({
            ...item,
            order: index,
          }));

          onReorder?.(reorderedWithOrder);
        }
      }
    },
    [data, onReorder]
  );

  // Generate unique IDs for each photo if they don't have keys
  const photosWithIds = localPhotos.map((photo, index) => ({
    ...photo,
    key: photo.key || generateUUID(),
  }));

  // Add the "Add more" button as a special item in the sortable list
  const allItems = [
    ...photosWithIds.map(photo => photo.key),
    'add-more-button',
  ];

  return (
    <div className={className}>
      <Sortable
        {...props}
        onDragEnd={({ over, active }) => {
          // Prevent any drag operations involving the add button
          if (
            active.id === 'add-more-button' ||
            over?.id === 'add-more-button'
          ) {
            return;
          }

          // Only allow reordering between actual photo items
          if (over && active.id !== over.id) {
            const oldIndex = photosWithIds.findIndex(p => p.key === active.id);
            const newIndex = photosWithIds.findIndex(p => p.key === over.id);

            // Ensure both indices are valid photo indices (not the add button)
            if (oldIndex !== -1 && newIndex !== -1) {
              const reorderedData = arrayMove(localPhotos, oldIndex, newIndex);
              const reorderedWithOrder = reorderedData.map((item, index) => ({
                ...item,
                order: index,
              }));

              // Update local state immediately for UI responsiveness
              setLocalPhotos(reorderedData);

              // Notify parent component
              onReorder?.(reorderedWithOrder);
            }
          }
        }}
        wrapperStyle={({ index }) => {
          if (index === 0) {
            return {
              height: '100%',
              gridRowStart: 'span 2',
              gridColumnStart: 'span 2',
            };
          }

          return {
            width: '100%',
            height: '100%',
          };
        }}
        items={allItems}
        key={photosWithIds
          .map(p => p.key)
          .join(',')
          .concat(',add-more-button')}
        isDisabled={id => id === 'add-more-button'}
      >
        {(id, index) => {
          // Handle the "Add more" button
          if (id === 'add-more-button') {
            return (
              <div
                key="add-more-button"
                onClick={handleBrowseClick}
                className={`${index === 0 ? 'h-full' : 'h-[169px]'} w-full rounded-lg bg-[#E5E5E5] flex items-center justify-center gap-2 cursor-pointer hover:bg-gray-100 transition-colors`}
                style={{ pointerEvents: 'auto' }}
              >
                <Plus className="w-6 h-6 text-gray-500" />
                <span className="text-gray-600 font-medium">
                  Add more photos
                </span>
                <input
                  ref={fileInputRef}
                  type="file"
                  accept="image/jpeg,image/jpg,image/png"
                  multiple
                  onChange={handleAdd}
                  className="hidden"
                />
              </div>
            );
          }

          // Handle regular photo items
          const key = id as string;
          const photo = photosWithIds.find(p => p.key === key);
          if (!photo) {
            throw new Error('Photo not found');
          }
          const actualIndex = photosWithIds.findIndex(p => p.key === key);
          return (
            <GalleryItem
              key={photo.key}
              photo={photo}
              onEdit={createEditHandler(actualIndex)}
              onRemove={createRemoveHandler(actualIndex)}
              onCheckedChange={createCheckedChangeHandler(actualIndex)}
              onUploadComplete={handleUploadComplete(actualIndex)}
            />
          );
        }}
      </Sortable>
    </div>
  );
}
