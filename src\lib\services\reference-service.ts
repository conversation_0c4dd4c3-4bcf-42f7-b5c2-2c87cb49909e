import { Reference } from '@/types';
import { httpClient } from '../http-client';

export interface CreateReferenceData {
  projectName: string;
  productionHouse: string;
  shootDates: {
    from: string;
    to: string;
  };
  internalNotes?: string;
}

export interface UpdateReferenceData extends Partial<CreateReferenceData> {
  id: string;
}

export interface ReferenceFilters {
  search?: string;
  productionHouse?: string;
  status?: string;
  dateRange?: {
    from: string;
    to: string;
  };
  page?: number;
  limit?: number;
}

export interface ReferenceListResponse {
  data: Reference[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasMore: boolean;
  };
}

export class ReferenceService {
  async getReferences(
    filters: ReferenceFilters = {}
  ): Promise<ReferenceListResponse> {
    const params = new URLSearchParams();

    if (filters.search) params.append('search', filters.search);
    if (filters.productionHouse && filters.productionHouse !== 'all') {
      params.append('productionHouse', filters.productionHouse);
    }
    if (filters.status && filters.status !== 'all') {
      params.append('status', filters.status);
    }
    if (filters.dateRange?.from) {
      params.append('dateFrom', filters.dateRange.from);
    }
    if (filters.dateRange?.to) {
      params.append('dateTo', filters.dateRange.to);
    }
    if (filters.page) params.append('page', filters.page.toString());
    if (filters.limit) params.append('limit', filters.limit.toString());

    return httpClient.get<ReferenceListResponse>(
      `/references?${params.toString()}`
    );
  }

  async getReference(id: string): Promise<Reference> {
    return httpClient.get<Reference>(`/references/${id}`);
  }

  async createReference(data: CreateReferenceData): Promise<Reference> {
    return httpClient.post<Reference>('/references', data);
  }

  async updateReference(data: UpdateReferenceData): Promise<Reference> {
    const { id, ...updateData } = data;
    return httpClient.put<Reference>(`/references/${id}`, updateData);
  }

  async deleteReference(id: string): Promise<void> {
    return httpClient.delete(`/references/${id}`);
  }

  // Mock implementation for development
  async getReferencesMock(
    filters: ReferenceFilters = {}
  ): Promise<ReferenceListResponse> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));

    const mockData: Reference[] = [
      {
        id: '1',
        projectName: 'Downtown Coffee Shop Scene',
        productionHouse: 'Netflix Studios',
        shootDates: {
          from: '2025-01-15T00:00:00.000Z',
          to: '2025-01-16T00:00:00.000Z',
        },
        status: 'approved',
        internalNotes: 'Great location for urban scenes',
      },
      {
        id: '2',
        projectName: 'Modern Office Building',
        productionHouse: 'HBO Productions',
        shootDates: {
          from: '2025-01-15T00:00:00.000Z',
          to: '2025-01-16T00:00:00.000Z',
        },
        status: 'in_progress',
        internalNotes: 'Perfect for corporate settings',
      },
      {
        id: '3',
        projectName: 'Vintage Apartment Interior',
        productionHouse: 'Disney Studios',
        shootDates: {
          from: '2025-01-15T00:00:00.000Z',
          to: '2025-01-16T00:00:00.000Z',
        },
        status: 'feedback_received',
        internalNotes: 'Charming vintage aesthetic',
      },
      {
        id: '4',
        projectName: 'Industrial Warehouse',
        productionHouse: 'Amazon Studios',
        shootDates: {
          from: '2025-01-15T00:00:00.000Z',
          to: '2025-01-16T00:00:00.000Z',
        },
        status: 'feedback_received',
        internalNotes: 'Large open space for action scenes',
      },
      {
        id: '5',
        projectName: 'Suburban Family Home',
        productionHouse: 'Apple TV+',
        shootDates: {
          from: '2025-01-15T00:00:00.000Z',
          to: '2025-01-16T00:00:00.000Z',
        },
        status: 'approved',
        internalNotes: 'Perfect for family dramas',
      },
      {
        id: '6',
        projectName: 'Beach House Exterior',
        productionHouse: 'Warner Bros',
        shootDates: {
          from: '2025-01-15T00:00:00.000Z',
          to: '2025-01-16T00:00:00.000Z',
        },
        status: 'approved',
        internalNotes: 'Beautiful ocean views',
      },
    ];

    // Apply filters
    let filteredData = mockData;

    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      filteredData = filteredData.filter(
        ref =>
          ref.projectName.toLowerCase().includes(searchLower) ||
          ref.productionHouse.toLowerCase().includes(searchLower) ||
          (ref.internalNotes &&
            ref.internalNotes.toLowerCase().includes(searchLower))
      );
    }

    if (filters.productionHouse && filters.productionHouse !== 'all') {
      filteredData = filteredData.filter(
        ref => ref.productionHouse === filters.productionHouse
      );
    }

    if (filters.status && filters.status !== 'all') {
      filteredData = filteredData.filter(ref => ref.status === filters.status);
    }

    if (filters.dateRange?.from && filters.dateRange?.to) {
      const filterFrom = new Date(filters.dateRange.from);
      const filterTo = new Date(filters.dateRange.to);
      filteredData = filteredData.filter(ref => {
        const refFrom = new Date(ref.shootDates.from);
        const refTo = new Date(ref.shootDates.to);
        return (
          (refFrom >= filterFrom && refFrom <= filterTo) ||
          (refTo >= filterFrom && refTo <= filterTo) ||
          (refFrom <= filterFrom && refTo >= filterTo)
        );
      });
    }

    // Pagination
    const page = filters.page || 1;
    const limit = filters.limit || 6;
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedData = filteredData.slice(startIndex, endIndex);
    const totalPages = Math.ceil(filteredData.length / limit);

    return {
      data: paginatedData,
      pagination: {
        page,
        limit,
        total: filteredData.length,
        totalPages,
        hasMore: page < totalPages,
      },
    };
  }
}

export const referenceService = new ReferenceService();
