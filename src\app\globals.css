/* @import url('https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap'); */
@import 'tailwindcss';
@import 'tw-animate-css';

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-header: var(--header);
  --color-sub-header: var(--sub-header);
  --font-sans:
    'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
    'Helvetica Neue', Arial, sans-serif;
  --font-mono:
    'Inter', ui-monospace, SFMono-Regular, 'SF Mono', Consolas,
    'Liberation Mono', Menlo, monospace;

  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);

  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-badge-secondary: var(--badge-secondary);

  /* Popover */
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);

  /* Card */
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --color-card-background: var(--card-background);

  /* Navbar */
  --color-navbar-foreground: var(--navbar-foreground);

  /* Sidebar */
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--header);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);

  /* Sonner Toaster */
  --color-sonner-foreground: var(--sonner-foreground);

  /* Input */
  --color-input-placeholder: var(--input-placeholder);
  --color-input-label: var(--input-label);

  /* Dialog */
  --color-overlay: var(--overlay);
}

:root {
  --radius: 0.625rem;
  --background: #ffffff;
  --foreground: #525252;
  --header: #171717;
  --sub-header: #404040;

  --primary: #0f172a;
  --primary-foreground: #f8fafc;
  --primary-foreground: #f8fafc;
  --secondary: #f1f5f9;
  --secondary-foreground: #0f172a;
  --muted: #f1f5f9;
  --muted-foreground: #737373;
  --accent: #f1f5f9;
  --accent-foreground: #0f172a;

  --border: #d4d4d4;
  --input: #94a3b8;
  --input-placeholder: #adaebc;
  --ring: #94a3b8;
  --badge-secondary: #d9d9d9;

  /* Card */
  --card: #ffffff;
  --card-background: #fafafa;
  --card-foreground: #020617;

  /* Popover */
  --popover: #ffffff;
  --popover-foreground: #020617;

  /* Table */

  /* Sidebar */
  --sidebar-width: 22.5rem; /* 360px */
  --sidebar-border: #d4d4d4;
  --sidebar-foreground: #525252;
  --sidebar-accent: #ffffff;

  /* Navbar */
  --navbar-foreground: #404040;
  --navbar-height: 4rem; /* 64px */

  /* Sonner Toaster */
  --sonner-foreground: #020617;
  --sonner-border: #e2e8f0;

  /* Dialog */
  --overlay: #bfbfbf;

  /* Photo Gallery */
  --focused-outline-color: #4c9ffe;
  --box-shadow-border: 0 0 0 calc(1px / var(--scale-x, 1))
    rgba(63, 63, 68, 0.05);
  --box-shadow-common: 0 1px calc(3px / var(--scale-x, 1)) 0
    rgba(34, 33, 81, 0.15);
  --box-shadow: var(--box-shadow-border), var(--box-shadow-common);

  @media (min-width: 768px) {
    --navbar-height: 5.5rem; /* 88px */
  }
}

@layer base {
  * {
    @apply border-border outline-ring/50;
    font-family:
      'Inter',
      system-ui,
      -apple-system,
      BlinkMacSystemFont,
      'Segoe UI',
      Roboto,
      'Helvetica Neue',
      Arial,
      sans-serif;
  }
  body {
    @apply bg-background text-foreground;
    font-family:
      'Inter',
      system-ui,
      -apple-system,
      BlinkMacSystemFont,
      'Segoe UI',
      Roboto,
      'Helvetica Neue',
      Arial,
      sans-serif;
    transition:
      background-color 0.2s ease,
      color 0.2s ease;
  }

  /* Smooth theme transitions for all elements */
  * {
    transition:
      background-color 0.2s ease,
      border-color 0.2s ease,
      color 0.2s ease,
      box-shadow 0.2s ease;
  }

  /* Exclude certain elements from theme transitions */
  img,
  svg,
  video,
  canvas,
  iframe {
    transition: none;
  }
}

/* Typography utilities */
@layer utilities {
  /* Header and sub-header color utilities */
  .text-header {
    color: var(--header);
  }

  .text-sub-header {
    color: var(--sub-header);
  }
}

/* Body: Use flexbox for proper layout with footer */
body {
  display: flex;
  flex-direction: column;
  min-height: 100dvh;
  overflow: hidden;
}

/* Override Radix ScrollArea wrapper */
[data-radix-scroll-area-viewport] > div {
  min-width: 100% !important;
  display: block !important; /* instead of display: table */
  height: 100% !important;
}

/* Override Radix Toaster */
[data-sonner-toast] {
  padding: 27px !important;
  border: 1px solid var(--sonner-border) !important;
  box-shadow: 0px 4px 6px -4px #0000001a !important;
  box-shadow: 0px 10px 15px -3px #0000001a !important;
}
[data-sonner-toast] [data-close-button] {
  top: 14px !important;
  right: -2px !important;
  left: unset !important;
  border: none !important;
}
[data-sonner-toast] [data-close-button]:hover {
  background-color: transparent !important;
}
[data-sonner-toast] [data-title] {
  color: var(--sonner-foreground) !important;
  font-size: 14px !important;
  line-height: 16px !important;
  font-weight: 600 !important;
}

/* Google Maps */
.gm-style .gm-style-iw-tc::after,
.gm-style .gm-style-iw-tc::before {
  display: none !important;
}
.gm-style .gm-style-iw {
  padding: 0 !important;
  padding-bottom: 12px;
  font-family:
    'Inter',
    system-ui,
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    Roboto,
    'Helvetica Neue',
    Arial,
    sans-serif;
}

.pac-container {
  z-index: 9999 !important;
  box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.05);
}

.gm-style.disable-map {
  pointer-events: none !important;
}

.scroll-hidden::-webkit-scrollbar {
  display: none;
}

.scroll-hidden {
  -ms-overflow-style: none;
  /* IE/Edge */
  scrollbar-width: none;
  /* Firefox */
}
