'use client';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useAuth } from '@/contexts/auth-context';
import { formatDateRange, getDisplayText } from '@/lib/utils';
import { Plus } from 'lucide-react';
import { useCallback, useEffect, useState } from 'react';
import Image from 'next/image';
import Loading from './loading';
import InfiniteScroll from 'react-infinite-scroll-component';
import AddReferenceModal from '@/components/AddReferenceModal';
import { DateRangePicker } from '@/components/DateRangePicker';
import { DateRange } from 'react-day-picker';
import { toast } from 'sonner';
import { useRouter } from 'next/navigation';
import { SearchIcon } from '@/lib/icons';
import { Body, Heading, Typography } from '@/components/ui/typography';
import { referenceService, ReferenceFilters } from '@/lib/services';
import { Reference } from '@/types';

const productionHouseOptions = [
  {
    id: '1',
    name: 'Netflix Studios',
  },
  {
    id: '2',
    name: 'HBO Productions',
  },
  {
    id: '3',
    name: 'Disney Studios',
  },
  {
    id: '4',
    name: 'Amazon Studios',
  },
  {
    id: '5',
    name: 'Apple TV+',
  },
  {
    id: '6',
    name: 'Warner Bros',
  },
];

const statusOptions = [
  {
    value: 'waiting_for_feedback',
    name: 'Waiting for feedback',
  },
  {
    value: 'in_progress',
    name: 'In progress',
  },
  {
    value: 'feedback_received',
    name: 'Feedback received',
  },
  {
    value: 'approved',
    name: 'Approved',
  },
];

const EmptyReferenceList = (props: {
  setIsAddReferenceModalOpen: (isOpen: boolean) => void;
}) => {
  const { setIsAddReferenceModalOpen } = props;
  return (
    <div className="fixed inset-0 grid place-items-center items-center justify-center">
      <div className="space-y-12 flex flex-col items-center">
        <Image
          src="/assets/circle-placeholder.svg"
          alt="Empty reference list"
          width={168}
          height={168}
        />
        <div className="w-[441px] text-center">
          <Heading level={4} className="mb-2 text-center">
            No reference lists yet
          </Heading>
          <Body className="text-muted-foreground text-center">
            Start organizing your location references by creating your first
            reference list.
          </Body>
        </div>
        <Button
          onClick={() => setIsAddReferenceModalOpen(true)}
          variant="default"
          className="w-[178px] h-[42px]"
        >
          <Plus /> Create new list
        </Button>
      </div>
    </div>
  );
};

const NoReferenceListFound = () => {
  return (
    <div className="grid place-items-center items-center justify-center flex-1">
      <div className="space-y-12 flex flex-col items-center">
        <Image
          src="/assets/circle-placeholder.svg"
          alt="Empty reference list"
          width={168}
          height={168}
        />
        <div className="w-[441px] text-center">
          <Heading level={4} className="mb-2 text-center">
            Oops! No lists found
          </Heading>
          <Body className="text-muted-foreground text-center">
            Try a different name or adjust your filters to find what you’re
            looking for.
          </Body>
        </div>
      </div>
    </div>
  );
};

export default function ScoutDashboard() {
  const { user } = useAuth();
  const router = useRouter();
  const [referenceData, setReferenceData] = useState<Reference[]>([]);
  const [search, setSearch] = useState<string>('');
  const [status, setStatus] = useState<string>('all');
  const [productionHouse, setProductionHouse] = useState<string>('all');
  const [dateRange, setDateRange] = useState<DateRange | undefined>();
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isInitialLoading, setIsInitialLoading] = useState<boolean>(true);
  const [hasMore, setHasMore] = useState<boolean>(true);
  const [page, setPage] = useState<number>(1);
  const [totalPages, setTotalPages] = useState<number>(0);
  const [totalCount, setTotalCount] = useState<number>(0);
  const [isAddReferenceModalOpen, setIsAddReferenceModalOpen] =
    useState<boolean>(false);
  const [hasFilters, setHasFilters] = useState<boolean>(false);

  const fetchReference = useCallback(
    async (
      page: number,
      search?: string,
      productionHouse?: string,
      dateRange?: DateRange,
      status?: string,
      isLoadMore: boolean = false
    ) => {
      try {
        if (!isLoadMore) {
          setIsLoading(true);
        }

        const filters: ReferenceFilters = {
          page,
          limit: 6,
          search: search || undefined,
          productionHouse:
            productionHouse !== 'all' ? productionHouse : undefined,
          status: status !== 'all' ? status : undefined,
          dateRange:
            dateRange?.from && dateRange?.to
              ? {
                  from: dateRange.from.toISOString(),
                  to: dateRange.to.toISOString(),
                }
              : undefined,
        };

        const response = await referenceService.getReferencesMock(filters);

        setReferenceData(prev =>
          isLoadMore ? [...prev, ...response.data] : response.data
        );
        setTotalPages(response.pagination.totalPages);
        setTotalCount(response.pagination.total);
        setHasMore(response.pagination.hasMore);
        setHasFilters(
          !!(
            search ||
            (productionHouse && productionHouse !== 'all') ||
            (status && status !== 'all') ||
            dateRange
          )
        );

        if (page === 1) {
          setIsInitialLoading(false);
        }
      } catch (error) {
        console.error('Error fetching references:', error);
        toast.error('Failed to load references');
      } finally {
        setIsLoading(false);
      }
    },
    []
  );

  const fetchMoreData = useCallback(() => {
    if (page >= totalPages || !hasMore) return;
    const nextPage = page + 1;
    setPage(nextPage);
    fetchReference(nextPage, search, productionHouse, dateRange, status, true);
  }, [
    page,
    totalPages,
    hasMore,
    fetchReference,
    search,
    productionHouse,
    dateRange,
    status,
  ]);

  const handleAddReference = (data: {
    projectName: string;
    productionHouse: string;
    shootDates: { from: Date; to?: Date };
    internalNotes?: string;
  }) => {
    console.log('data:', data);
    fetchReference(page, search, productionHouse, dateRange, status);
    toast.success('List created successfully.');
    router.push(`/scout/reference/reference-id`);
  };
  const dateRangeChange = (dateRange: DateRange | undefined) => {
    setDateRange(dateRange);
  };

  useEffect(() => {
    // Reset to first page when filters change
    if (page !== 1) {
      setPage(1);
    } else {
      fetchReference(1, search, productionHouse, dateRange, status);
    }
  }, [search, productionHouse, dateRange, status, fetchReference, page]);

  useEffect(() => {
    // Handle page changes for pagination
    if (page > 1) {
      fetchReference(page, search, productionHouse, dateRange, status, true);
    }
  }, [page, fetchReference, search, productionHouse, dateRange, status]);

  return (
    <>
      <div className="w-full flex justify-center">
        <div className="space-y-6 xl:w-[1128px] p-6 xl:px-0 flex flex-col">
          <div>
            <h1 className="text-2xl font-bold mb-2">
              Welcome{totalCount > 0 ? ' back' : ''}, {user?.firstName ?? ''}
            </h1>
            <span className="">
              {totalCount > 0
                ? 'Review your reference lists or start a new one.'
                : 'Ready to create your first list?'}
            </span>
          </div>
          {isInitialLoading ? (
            <Loading />
          ) : totalCount === 0 && !hasFilters ? (
            <EmptyReferenceList
              setIsAddReferenceModalOpen={setIsAddReferenceModalOpen}
            />
          ) : (
            <div className="flex flex-col w-full space-y-6 flex-1">
              <div className="flex flex-col gap-9.5 lg:flex-row lg:items-center lg:justify-between">
                <div className="flex flex-col gap-3 lg:flex-row lg:items-center lg:flex-1">
                  <div className="relative flex-3">
                    <SearchIcon className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      placeholder="Search lists..."
                      value={search ?? ''}
                      onChange={e => setSearch(e.target.value)}
                      className="pl-9"
                    />
                  </div>
                  <div className="flex-5 flex flex-wrap items-center gap-2">
                    <Select
                      value={productionHouse}
                      onValueChange={v => setProductionHouse(v)}
                    >
                      <SelectTrigger
                        aria-label="Production House"
                        className="flex-1"
                      >
                        <SelectValue placeholder="Production House" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">Production house</SelectItem>
                        {productionHouseOptions.map((ph, index) => (
                          <SelectItem key={index} value={ph.id}>
                            {ph.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>

                    <DateRangePicker
                      onDateRangeChange={dateRangeChange}
                      className="basis-1/3"
                    />

                    <Select value={status} onValueChange={v => setStatus(v)}>
                      <SelectTrigger aria-label="Status" className="w-[137px]">
                        <SelectValue placeholder="Status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">Status</SelectItem>
                        {statusOptions.map((s, index) => (
                          <SelectItem key={index} value={s.value}>
                            {s.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <Button
                  onClick={() => setIsAddReferenceModalOpen(true)}
                  variant="default"
                  className="w-full lg:w-[178px] lg:h-[42px]"
                >
                  <Plus /> Create new list
                </Button>
              </div>
              {referenceData.length === 0 && !isLoading && hasFilters ? (
                <NoReferenceListFound />
              ) : (
                <div id="scrollableDiv" className="h-[72dvh] overflow-auto">
                  <InfiniteScroll
                    scrollableTarget="scrollableDiv"
                    dataLength={referenceData.length}
                    next={fetchMoreData}
                    hasMore={hasMore}
                    loader={
                      isLoading ? (
                        <Loading />
                      ) : (
                        <p className="text-center py-4">Loading...</p>
                      )
                    }
                  >
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                      {referenceData.map((reference, index) => {
                        return (
                          <div
                            key={index}
                            className="border rounded-lg shadow-sm bg-white flex flex-col"
                          >
                            <div className="h-40 bg-gray-200 flex items-center justify-center rounded-t-lg">
                              {reference.image ? (
                                <Image
                                  src={reference.image}
                                  alt="Location"
                                  fill
                                  className="object-cover rounded-t-lg"
                                />
                              ) : (
                                <span className="text-gray-500">
                                  Location Image
                                </span>
                              )}
                            </div>
                            <div className="p-4 flex-1 flex flex-col">
                              <Typography variant="h3" className="mb-2">
                                {reference.projectName}
                              </Typography>
                              <p className="text-sm text-gray-600">
                                {reference.productionHouse}
                              </p>
                              <p className="text-sm text-gray-500 mt-1">
                                {formatDateRange(
                                  new Date(reference.shootDates.from),
                                  new Date(reference.shootDates.to)
                                )}
                              </p>
                              {reference.internalNotes && (
                                <p className="text-xs text-gray-400 mt-1 line-clamp-2">
                                  {reference.internalNotes}
                                </p>
                              )}
                              <div className="mt-auto flex justify-end">
                                <Badge
                                  variant="secondary"
                                  className="capitalize"
                                >
                                  {getDisplayText(reference.status)}
                                </Badge>
                              </div>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </InfiniteScroll>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
      <AddReferenceModal
        productionHouseOptions={productionHouseOptions}
        open={isAddReferenceModalOpen}
        onOpenChange={setIsAddReferenceModalOpen}
        onSave={handleAddReference}
      />
    </>
  );
}
