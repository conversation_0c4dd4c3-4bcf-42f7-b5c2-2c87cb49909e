'use client';

import * as React from 'react';
import { useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import ConfirmDialog from './ConfirmDialog';
import { Tag } from '@/types/tag';

interface AddTagModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSave: (tagData: Partial<Tag>) => void;
  typeOptions: { value: string; label: string }[];
}

interface ValidationErrors {
  name?: string;
  type?: string;
  color?: string;
}

export const colorOptions = [
  { value: 'bg-red-500', label: 'Red' },
  { value: 'bg-green-500', label: 'Green' },
  { value: 'bg-blue-500', label: 'Blue' },
  { value: 'bg-yellow-500', label: 'Yellow' },
  { value: 'bg-orange-500', label: 'Orange' },
  { value: 'bg-purple-500', label: 'Purple' },
  { value: 'bg-pink-500', label: 'Pink' },
  { value: 'bg-teal-500', label: 'Teal' },
  { value: 'bg-cyan-500', label: 'Cyan' },
  { value: 'bg-gray-500', label: 'Gray' },
  { value: 'bg-black', label: 'Black' },
];

export default function AddTagModal({
  open,
  onOpenChange,
  onSave,
  typeOptions,
}: AddTagModalProps) {
  const [formData, setFormData] = React.useState<Partial<Tag>>({
    name: '',
    type: undefined,
    color: undefined,
  });
  const [errors, setErrors] = useState<ValidationErrors>({});
  const [touched, setTouched] = useState<
    Record<keyof ValidationErrors, boolean>
  >({
    name: false,
    type: false,
    color: false,
  });
  const [isDiscardTagDialogOpen, setIsDiscardTagDialogOpen] = useState(false);

  const validateField = (
    field: keyof Tag,
    value: string
  ): string | undefined => {
    switch (field) {
      case 'name':
        if (!value.trim()) return 'The tag name is required';
        break;
      case 'type':
        if (!value) return 'The tag type is required';
        break;
    }
    return undefined;
  };

  const validateForm = (): boolean => {
    const newErrors: ValidationErrors = {};
    let isValid = true;

    (Object.keys(formData) as Array<keyof ValidationErrors>).forEach(key => {
      const field = key;
      const error = validateField(field as keyof Tag, formData[field] || '');
      if (error) {
        newErrors[field] = error;
        isValid = false;
      }
    });

    setErrors(newErrors);
    return isValid;
  };

  const handleFieldChange = (field: keyof ValidationErrors, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    setTouched(prev => ({ ...prev, [field]: true }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    setTouched({
      name: true,
      type: true,
      color: true,
    });

    if (!validateForm()) {
      return;
    }
    resetForm();
    onSave(formData);
    onOpenChange(false);
  };

  const handleInputChange = (field: keyof ValidationErrors, value: string) => {
    handleFieldChange(field, value);
  };

  const resetForm = () => {
    setFormData({
      name: '',
      type: undefined,
      color: undefined,
    });
    setErrors({});
    setTouched({
      name: false,
      type: false,
      color: false,
    });
  };

  const handleOpenChange = (open: boolean) => {
    onOpenChange(open);
  };

  const handleDiscardTag = () => {
    resetForm();
  };

  return (
    <>
      <Dialog open={open} onOpenChange={handleOpenChange}>
        <DialogContent className="sm:max-w-[329px]">
          <DialogHeader>
            <DialogTitle>Create a new tag</DialogTitle>
            <DialogDescription>
              Add a tag to better describe and organize your locations.
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label
                htmlFor="name"
                className="text-sm font-medium text-foreground"
              >
                Tag name
              </Label>
              <Input
                id="name"
                value={formData.name}
                onChange={e => handleInputChange('name', e.target.value)}
                placeholder="Enter tag name"
                className={`mt-1 ${touched.name && errors.name ? 'border-red-500 focus:border-red-500' : ''}`}
              />
              {touched.name && errors.name && (
                <p className="text-sm text-red-500 mt-1">{errors.name}</p>
              )}
            </div>

            <div className="space-y-2 w-full">
              <Label
                htmlFor="type"
                className="text-sm font-medium text-foreground"
              >
                Tag type
              </Label>
              <Select
                value={formData.type}
                onValueChange={value => handleFieldChange('type', value)}
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select tag's type" />
                </SelectTrigger>
                <SelectContent>
                  {typeOptions.map(type => (
                    <SelectItem
                      className="capitalize"
                      key={type.value}
                      value={type.value}
                    >
                      {type.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {touched.type && errors.type && (
                <p className="text-sm text-red-500 mt-1">{errors.type}</p>
              )}
            </div>

            <div className="space-y-2 w-full">
              <Label
                htmlFor="color"
                className="text-sm font-medium text-foreground"
              >
                Color (optional)
              </Label>
              <Select
                value={formData.color}
                onValueChange={value => handleFieldChange('color', value)}
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Pick a color for this tag" />
                </SelectTrigger>
                <SelectContent>
                  {colorOptions.map(color => (
                    <SelectItem
                      className="capitalize"
                      key={color.value}
                      value={color.value}
                    >
                      {color.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <DialogFooter className="mt-6">
              <Button
                onClick={() => {
                  onOpenChange(false);
                  const hasFilled = Object.values(formData).some(
                    field => !!field
                  );
                  if (hasFilled) setIsDiscardTagDialogOpen(true);
                }}
                type="button"
                variant="outline"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={Object.keys(errors).some(
                  key => errors[key as keyof ValidationErrors]
                )}
              >
                Create tag
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
      <ConfirmDialog
        open={isDiscardTagDialogOpen}
        onOpenChange={setIsDiscardTagDialogOpen}
        title="Discard tag?"
        description="You’ve started filling in this tag’s details. If you cancel now, the information will be lost."
        confirmText="Remove"
        cancelText="Keep tag"
        onConfirm={handleDiscardTag}
        onCancel={() => onOpenChange(true)}
      />
    </>
  );
}
