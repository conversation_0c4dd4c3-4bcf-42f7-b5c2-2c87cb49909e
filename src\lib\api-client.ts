// Server-side utility function for making authenticated requests
// Use this in Next.js API routes when you need to call NestJS with an access token
export async function makeServerRequest<T>(
  endpoint: string,
  accessToken: string,
  options: RequestInit = {}
): Promise<T> {
  const baseUrl = process.env.NESTJS_API_URL || 'http://localhost:3001';
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

  try {
    const response = await fetch(`${baseUrl}${endpoint}`, {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${accessToken}`,
        ...options.headers,
      },
      signal: controller.signal,
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      throw new Error(`API request failed: ${response.statusText}`);
    }

    return response.json();
  } catch (error) {
    clearTimeout(timeoutId);
    throw error;
  }
}

// Export types for use in other files
export type {
  LoginCredentials,
  LoginResponse,
  AuthenticatedUser,
} from './services';
export type { User, UserProfileUpdate } from '@/types/user';
