export enum Routes {
  HOME = '/',
  DASHBOARD = '/dashboard',

  // Auth routes
  ACCEPT_INVITATION = '/accept-invitation',
  SETUP_PASSWORD = '/setup-password',
  SIGN_IN = '/sign-in',
  SIGN_UP = '/sign-up',
  FORGOT_PASSWORD = '/forgot-password',
  RESET_PASSWORD = '/reset-password',

  // Profile routes
  PROFILE = '/profile',

  // Not found route
  NOT_FOUND = '/404',

  // Admin routes
  ADMIN_PROPERTIES = '/admin/properties',
  ADMIN_REFERENCE_SITES = '/admin/reference-sites',
  ADMIN_CATEGORIES = '/admin/categories',
  ADMIN_USERS = '/admin/users',
  ADMIN_DASHBOARD = '/admin/dashboard',
  ADMIN_TAGS = '/admin/tags',
  ADMIN_LOCATIONS = '/admin/locations',
  ADMIN_CREATE_LOCATION = '/admin/locations/create-location',
  ADMIN_EDIT_LOCATION = '/admin/locations/edit-location',

  // Scout routes
  SCOUT_REFERENCE = '/scout/reference',
  SCOUT_DASHBOARD = '/scout/dashboard',

  // Viewer routes
  VIEWER_REFERENCE = '/viewer/reference',
  VIEWER_DASHBOARD = '/viewer/dashboard',

  // Assets routes
  ASSETS = '/assets',
  VIEW_REFERENCE = '/share',
}

export const PROTECTED_ROUTES = [
  Routes.DASHBOARD,
  Routes.PROFILE,
  Routes.ADMIN_PROPERTIES,
  Routes.ADMIN_REFERENCE_SITES,
  Routes.ADMIN_CATEGORIES,
  Routes.ADMIN_USERS,
  Routes.ADMIN_DASHBOARD,
  Routes.ADMIN_LOCATIONS,
  Routes.ADMIN_TAGS,
  Routes.SCOUT_REFERENCE,
  Routes.SCOUT_DASHBOARD,
  Routes.VIEWER_REFERENCE,
  Routes.VIEWER_DASHBOARD,
  Routes.VIEWER_REFERENCE,
];

export const AUTH_ROUTES = [
  Routes.SIGN_IN,
  Routes.SIGN_UP,
  Routes.FORGOT_PASSWORD,
  Routes.RESET_PASSWORD,
  Routes.ACCEPT_INVITATION,
];

export const PUBLIC_ROUTES = [Routes.SIGN_IN];

export const ADMIN_ROUTES = [
  Routes.ADMIN_PROPERTIES,
  Routes.ADMIN_REFERENCE_SITES,
  Routes.ADMIN_CATEGORIES,
  Routes.ADMIN_USERS,
  Routes.ADMIN_DASHBOARD,
  Routes.ADMIN_LOCATIONS,
  Routes.ADMIN_TAGS,
];

export const RESET_PASSWORD_ROUTES = [
  // Routes.FORGOT_PASSWORD,
  Routes.RESET_PASSWORD,
];

export const routeTitles: Record<Routes, string> = {
  [Routes.HOME]: 'Scoutr | Modern Web Application',
  [Routes.DASHBOARD]: 'Dashboard',
  [Routes.SIGN_IN]: 'Sign In',
  [Routes.SIGN_UP]: 'Sign Up',
  [Routes.FORGOT_PASSWORD]: 'Forgot Password',
  [Routes.RESET_PASSWORD]: 'Reset Password',
  [Routes.PROFILE]: 'Profile',
  [Routes.NOT_FOUND]: 'Page Not Found',
  [Routes.ADMIN_PROPERTIES]: 'Properties',
  [Routes.ADMIN_REFERENCE_SITES]: 'Reference Sites',
  [Routes.ADMIN_CATEGORIES]: 'Categories & Tags',
  [Routes.ADMIN_USERS]: 'Users',
  [Routes.ADMIN_DASHBOARD]: 'Dashboard',
  [Routes.ADMIN_LOCATIONS]: 'Locations',
  [Routes.ADMIN_CREATE_LOCATION]: 'Create Location',
  [Routes.ADMIN_EDIT_LOCATION]: 'Edit Location',
  [Routes.ADMIN_TAGS]: 'Tags',
  [Routes.ASSETS]: 'Asset Categorization',
  [Routes.ACCEPT_INVITATION]: 'Accept Invitation',
  [Routes.SETUP_PASSWORD]: 'Setup Password',
  [Routes.SCOUT_REFERENCE]: 'Reference Sites',
  [Routes.SCOUT_DASHBOARD]: 'Dashboard',
  [Routes.VIEWER_REFERENCE]: 'Reference Sites',
  [Routes.VIEW_REFERENCE]: 'Reference Sites',
  [Routes.VIEWER_DASHBOARD]: 'Dashboard',
};

export const routeDescriptions: Record<Routes, string> = {
  [Routes.HOME]:
    'A modern Next.js application with authentication, beautiful UI components, and everything you need to build amazing web applications.',
  [Routes.DASHBOARD]:
    'Your personal dashboard with analytics and quick actions',
  [Routes.SIGN_IN]: 'Sign in to your Scoutr account',
  [Routes.SIGN_UP]: 'Create a new Scoutr account',
  [Routes.FORGOT_PASSWORD]: 'Request a password reset for your Scoutr account',
  [Routes.RESET_PASSWORD]: 'Reset your Scoutr account password',
  [Routes.PROFILE]: 'Manage your Scoutr profile and account settings',
  [Routes.NOT_FOUND]: 'The page you are looking for could not be found',
  [Routes.ADMIN_PROPERTIES]: 'Manage properties and listings',
  [Routes.ADMIN_REFERENCE_SITES]: 'Manage reference sites and resources',
  [Routes.ADMIN_CATEGORIES]: 'Manage categories and tags',
  [Routes.ADMIN_USERS]: 'Manage user accounts and permissions',
  [Routes.ADMIN_DASHBOARD]: 'Manage user accounts and locations',
  [Routes.ADMIN_LOCATIONS]: 'Manage locations',
  [Routes.ADMIN_CREATE_LOCATION]: 'Create location',
  [Routes.ADMIN_EDIT_LOCATION]: 'Edit location',
  [Routes.ADMIN_TAGS]: 'Manage tags',
  [Routes.ASSETS]: 'Manage asset categorization',
  [Routes.ACCEPT_INVITATION]: 'Accept your invitation to join Scoutr',
  [Routes.SETUP_PASSWORD]:
    'Set up your password to complete your Scoutr account',
  [Routes.SCOUT_REFERENCE]: 'Manage reference sites and resources',
  [Routes.SCOUT_DASHBOARD]: 'Manage user accounts and locations',
  [Routes.VIEWER_REFERENCE]: 'Manage reference sites and resources',
  [Routes.VIEW_REFERENCE]: 'View reference',
  [Routes.VIEWER_DASHBOARD]: 'Manage user accounts and locations',
};

export function getPageTitle(route: Routes): string {
  const title = routeTitles[route];
  return title === routeTitles[Routes.HOME] ? title : `${title} | Scoutr`;
}

export function getPageDescription(route: Routes): string {
  return routeDescriptions[route];
}
