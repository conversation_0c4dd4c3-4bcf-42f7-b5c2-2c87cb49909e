'use client';
import React, { useState, useRef, useEffect } from 'react';
import { Map, Marker, useMapsLibrary } from '@vis.gl/react-google-maps';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogFooter,
  Di<PERSON>Header,
  DialogTitle,
} from './ui/dialog';
import LocationAutocomplete from './LocationAutoComplete';
import { Button } from './ui/button';

interface LocationPickerModalProps {
  open: boolean;
  defaultAddress?: {
    lat?: number;
    lng?: number;
    address?: string;
  } | null;
  onOpenChange: (open: boolean) => void;
  onChange: (
    data: { lat: number; lng: number; address?: string } | null
  ) => void;
}

const defaultCenter = { lat: 40.7128, lng: -74.006 };

export default function LocationPickerModal({
  open,
  defaultAddress,
  onOpenChange,
  onChange,
}: LocationPickerModalProps) {
  const [selected, setSelected] = useState<{
    lat?: number;
    lng?: number;
    address?: string;
  } | null>(defaultAddress ?? null);

  const inputRef = useRef<HTMLInputElement>(null);
  const [autocompleteInst, setAutocompleteInst] =
    useState<google.maps.places.Autocomplete | null>(null);

  const places = useMapsLibrary('places');

  useEffect(() => {
    if (!places || !inputRef.current || !window.google?.maps?.places) return;
    inputRef.current.setAttribute('autocomplete', 'off');

    const options: google.maps.places.AutocompleteOptions = {
      fields: ['geometry', 'formatted_address', 'name'],
    };

    const AutocompleteCtor = window.google.maps.places.Autocomplete;
    const inst = new AutocompleteCtor(inputRef.current, options);
    setAutocompleteInst(inst);

    return () => {
      setAutocompleteInst(null);
    };
  }, [places]);

  useEffect(() => {
    if (!autocompleteInst) return;
    const listener = autocompleteInst.addListener('place_changed', () => {
      const place = autocompleteInst.getPlace();
      if (place.geometry?.location) {
        const coords = {
          lat: place.geometry.location.lat(),
          lng: place.geometry.location.lng(),
          address: place.formatted_address,
        };
        setSelected(coords);
        onChange(coords);
      }
    });

    return () => {
      listener.remove();
    };
  }, [autocompleteInst, onChange]);

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const handleMapClick = async (e: any) => {
    if (!e.detail.latLng) return;
    const lat = e.detail.latLng.lat;
    const lng = e.detail.latLng.lng;

    let address: string | undefined;
    try {
      const geocoder = new google.maps.Geocoder();
      const res = await geocoder.geocode({ location: { lat, lng } });
      address = res.results[0]?.formatted_address;
    } catch (err) {
      console.error('Geocoder failed:', err);
    }

    const coords = { lat, lng, address };
    setSelected(coords);
    onChange(coords);
  };

  useEffect(() => {
    setSelected(defaultAddress ?? null);
  }, [defaultAddress, open]);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Choose address</DialogTitle>
        </DialogHeader>
        <div className="flex flex-col gap-2">
          <div>
            <LocationAutocomplete
              value={selected?.address || ''}
              onSelect={place => {
                if (place) {
                  const lat = place.geometry?.location?.lat();
                  const lng = place.geometry?.location?.lng();
                  const address = place.formatted_address || place.name;
                  if (typeof lat === 'number' && typeof lng === 'number') {
                    const coords = { lat, lng, address };
                    setSelected(coords);
                    onChange(coords);
                  }
                } else {
                  setSelected(null);
                }
              }}
              placeholder="Search address"
            />
          </div>

          <div className="h-[65vh] w-full">
            <Map
              style={{ width: '100%', height: '100%' }}
              center={
                selected &&
                selected.lat !== undefined &&
                selected.lng !== undefined
                  ? { lat: selected.lat, lng: selected.lng }
                  : defaultCenter
              }
              defaultZoom={12}
              onClick={handleMapClick}
            >
              {selected?.address &&
                typeof selected.lat === 'number' &&
                selected.lat !== 0 &&
                typeof selected.lng === 'number' &&
                selected.lng !== 0 && (
                  <Marker position={{ lat: selected.lat, lng: selected.lng }} />
                )}
            </Map>
          </div>
        </div>
        <DialogFooter className="sm:justify-end">
          <Button
            type="button"
            variant="outline"
            onClick={() => onOpenChange(false)}
          >
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
