'use client';

import { createContext, useContext, useEffect, useState } from 'react';
import { useSession, signIn, signOut } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { User } from 'next-auth';
import { authService } from '@/lib/services';
import { Routes } from '@/lib/routes';

interface AuthContextType {
  user: User | null;
  loading: boolean;
  signIn: (
    email: string,
    password: string,
    callbackUrl?: string
  ) => Promise<void>;
  signOut: () => Promise<void>;
  isAuthenticated: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const { data: session, status } = useSession();
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    if (status !== 'loading') {
      setLoading(false);
    }
  }, [status]);

  // Handle session errors (like RefreshAccessTokenError) by logging out the user
  useEffect(() => {
    if (session?.error === 'RefreshAccessTokenError') {
      console.log('Session refresh failed, logging out user automatically');
      handleSignOut();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [session?.error]);

  // Fallback: Check if user has invalid tokens (no access token but still authenticated)
  useEffect(() => {
    if (status === 'authenticated' && session?.user && !session.accessToken) {
      console.log(
        'User is authenticated but has no access token, forcing logout'
      );
      handleSignOut();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [status, session?.user, session?.accessToken]);

  // Simplified - NextAuth.js handles session management automatically

  const handleSignIn = async (
    email: string,
    password: string,
    callbackUrl?: string
  ) => {
    try {
      // Start the sign-in process immediately
      const result = await signIn('credentials', {
        email,
        password,
        redirect: false,
        callbackUrl: callbackUrl || '',
      });

      if (result?.error) {
        throw new Error('Invalid credentials');
      }

      // Determine redirect URL based on user role
      let redirectUrl = callbackUrl || '';

      // If no specific callback URL, determine based on role
      if (!redirectUrl) {
        // Let the middleware handle the role-based redirect from root
        redirectUrl = '/';
      }

      // Use replace instead of push for faster navigation
      router.replace(redirectUrl);

      // Force a router refresh to ensure the new session is picked up
      router.refresh();
    } catch (error) {
      throw error;
    }
  };

  const handleSignOut = async () => {
    try {
      // Call backend logout endpoint to invalidate tokens
      await authService.logout();
    } catch (error) {
      // Log error but don't prevent logout - user should still be signed out locally
      console.error('Failed to logout from backend:', error);
    }

    // Always sign out locally regardless of backend response
    await signOut({ redirect: false });
    router.push(Routes.SIGN_IN);
  };

  const value = {
    user: session?.user as User | null,
    loading,
    signIn: handleSignIn,
    signOut: handleSignOut,
    isAuthenticated: !!session,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
