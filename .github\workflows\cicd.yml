name: NextJS CI/CD

on:
  push:
    branches:
      - develop
  workflow_dispatch:

env:
  AWS_REGION: us-west-2
  ECR_REPOSITORY: scoutr/frontend-${{ github.ref_name }}

jobs:
  build-and-push:
    runs-on: ubuntu-latest
    environment: ${{ github.ref_name }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1

      - name: Build, tag, and push image to Amazon ECR
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          IMAGE_TAG: ${{ github.sha }}
        run: |
          echo '${{ vars.ENV_FILE }}' > .env
          docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG .
          docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG
          docker tag $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG $ECR_REGISTRY/$ECR_REPOSITORY:latest
          docker push $ECR_REGISTRY/$ECR_REPOSITORY:latest

  deploy:
    needs: build-and-push
    runs-on: ubuntu-latest
    environment: ${{ github.ref_name }}
    steps:
      - name: Setup SSH
        uses: webfactory/ssh-agent@v0.7.0
        with:
          ssh-private-key: ${{ secrets.CLOUD_SSH_KEY }}

      - name: Deploy django to ${{ github.ref_name }} server
        run: |
          ssh -o StrictHostKeyChecking=no ${{ secrets.CLOUD_USERNAME }}@${{ secrets.CLOUD_HOST }} "cd /home/<USER>/scoutr && docker image prune -f && docker compose pull web && docker compose down && docker compose up -d"
