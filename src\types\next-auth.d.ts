/* eslint-disable @typescript-eslint/no-unused-vars */
import NextAuth from 'next-auth';
import { Role } from './enum';

declare module 'next-auth' {
  interface User {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
    avatar: string;
    role: Role;
    accessToken?: string;
    refreshToken?: string;
  }

  interface Session {
    user: User;
    accessToken?: string;
    error?: string;
    isFresh?: boolean;
  }
}

declare module 'next-auth/jwt' {
  interface JWT {
    email?: string;
    firstName?: string;
    lastName?: string;
    role: Role;
    avatar: string;
    accessToken?: string;
    refreshToken?: string;
    accessTokenExpires?: number;
    refreshTokenExpires?: number;
    error?: string;
    isFresh?: boolean;
  }
}
