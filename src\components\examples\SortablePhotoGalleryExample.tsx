import React, { useState } from 'react';
import PhotoGallery from '../photo/PhotoGallery';
import { FileUpload } from '@/types';
import { CheckedState } from '@radix-ui/react-checkbox';

/**
 * Example component demonstrating the sortable PhotoGallery functionality
 */
export default function SortablePhotoGalleryExample() {
  const [photos, setPhotos] = useState<FileUpload[]>([
    {
      key: 'photo-1',
      url: 'https://via.placeholder.com/300x200/FF6B6B/FFFFFF?text=Photo+1',
      order: 0,
      tags: [],
    },
    {
      key: 'photo-2',
      url: 'https://via.placeholder.com/300x200/4ECDC4/FFFFFF?text=Photo+2',
      order: 1,
      tags: [],
    },
    {
      key: 'photo-3',
      url: 'https://via.placeholder.com/300x200/45B7D1/FFFFFF?text=Photo+3',
      order: 2,
      tags: [],
    },
    {
      key: 'photo-4',
      url: 'https://via.placeholder.com/300x200/96CEB4/FFFFFF?text=Photo+4',
      order: 3,
      tags: [],
    },
  ]);

  const handleAdd = (newPhotos: FileUpload[]) => {
    setPhotos(prev => [...prev, ...newPhotos]);
  };

  const handleReorder = (reorderedData: FileUpload[]) => {
    setPhotos(reorderedData);
    console.log('Photos reordered:', reorderedData);
  };

  const handleEdit = (index: number) => {
    console.log('Edit photo at index:', index);
  };

  const handleRemove = (index: number) => {
    setPhotos(prev => prev.filter((_, i) => i !== index));
  };

  const handleCheckedChange = (index: number, checked: CheckedState) => {
    setPhotos(prev =>
      prev.map((photo, i) =>
        i === index
          ? {
              ...photo,
              isChecked: checked === 'indeterminate' ? undefined : checked,
            }
          : photo
      )
    );
  };

  const handleChange = (index: number, file: FileUpload) => {
    setPhotos(prev =>
      prev.map((photo, i) => (i === index ? { ...photo, ...file } : photo))
    );
  };

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h2 className="text-2xl font-bold mb-4">
        Sortable Photo Gallery Example
      </h2>
      <p className="text-gray-600 mb-6">
        Drag and drop photos to reorder them. Click and drag anywhere on a photo
        to move it.
      </p>

      <PhotoGallery
        data={photos}
        onAdd={handleAdd}
        onReorder={handleReorder}
        onEdit={handleEdit}
        onRemove={handleRemove}
        onCheckedChange={handleCheckedChange}
        onChange={handleChange}
      />

      <div className="mt-8 p-4 bg-gray-100 rounded-lg">
        <h3 className="font-semibold mb-2">Current Photo Order:</h3>
        <div className="text-sm text-gray-600">
          {photos.map((photo, index) => (
            <div key={photo.key}>
              {index + 1}. {photo.key} {photo.isChecked ? '(Selected)' : ''}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
