'use client';

import { Sidebar, SidebarContent } from '@/components/ui/sidebar';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Button } from '@/components/ui/button';
import { Search, MapPin } from 'lucide-react';
import { useState } from 'react';
import { useSearchLocationFilters } from './search-location-context';
import type { LocationCategory } from './search-location-context';
import MultiSelect from '@/components/MultiSelect';

export default function SidebarFilter() {
  const { filters, setFilters } = useSearchLocationFilters();
  const [category, setCategory] = useState<string | null>(filters.category);

  return (
    <Sidebar
      className="border-r border-sidebar-border sticky top-0 h-full"
      style={{ '--sidebar-width': '22.5rem' } as React.CSSProperties}
    >
      <SidebarContent className="h-full p-4 space-y-6">
        <div className="space-y-2">
          <Label className="text-sm">What are you looking for?</Label>
          <div className="relative">
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="e.g. 'Warehouse', 'Coffee shop'"
              className="pl-9"
              value={filters.query}
              debounceMs={300}
              onChange={e =>
                setFilters(prev => ({ ...prev, query: e.target.value }))
              }
            />
          </div>
        </div>

        <div className="space-y-2">
          <Label className="text-sm">Where?</Label>
          <span className="text-xs text-muted-foreground">
            Enter city, state or area.
          </span>
          <div className="relative mt-1">
            <MapPin className="absolute right-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="e.g. 'Montreal, QC'"
              value={filters.where}
              debounceMs={300}
              onChange={e =>
                setFilters(prev => ({ ...prev, where: e.target.value }))
              }
            />
          </div>
        </div>

        <div className="space-y-3">
          <Label className="text-sm">Location category</Label>
          <div className="flex gap-2">
            {[
              { key: 'residential', label: 'Residential' },
              { key: 'commercial', label: 'Commercial' },
              { key: 'public', label: 'Public' },
            ].map(item => (
              <Button
                key={item.key}
                type="button"
                variant="outline"
                onClick={() => {
                  const nextCategory: LocationCategory =
                    category === item.key
                      ? null
                      : (item.key as LocationCategory);
                  setCategory(nextCategory);
                  setFilters(prev => ({
                    ...prev,
                    category: nextCategory,
                  }));
                }}
                className={`flex flex-col gap-1 rounded-md w-[98px] h-[97px] text-center ${item.key === category ? 'border-black' : ''}`}
              >
                <div className="w-11 h-11 bg-gray-300 rounded-full"></div>
                {item.label}
              </Button>
            ))}
          </div>
        </div>

        <div className="space-y-2">
          <Label className="text-sm">Sub-category</Label>
          <MultiSelect
            options={[
              { value: 'apartment', label: 'Apartment' },
              { value: 'restaurant', label: 'Restaurant' },
              { value: 'warehouse', label: 'Warehouse' },
              { value: 'office', label: 'Office' },
            ]}
            value={filters.subCategories}
            onChange={vals =>
              setFilters(prev => ({ ...prev, subCategories: vals }))
            }
            placeholder="e.g. ‘Apartment’, ‘Restaurant’"
          />
        </div>

        <div className="space-y-2">
          <Label className="text-sm">Location style</Label>
          <MultiSelect
            options={[
              { value: 'modern', label: 'Modern' },
              { value: 'rustic', label: 'Rustic' },
              { value: 'industrial', label: 'Industrial' },
              { value: 'vintage', label: 'Vintage' },
            ]}
            value={filters.style}
            onChange={vals => setFilters(prev => ({ ...prev, style: vals }))}
            placeholder="e.g. ‘Modern’, ‘Rustic’"
          />
        </div>

        <div className="space-y-3">
          <Label className="text-sm">Size</Label>
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="size-small"
                checked={filters.size.small}
                onCheckedChange={v =>
                  setFilters(prev => ({
                    ...prev,
                    size: { ...prev.size, small: Boolean(v) },
                  }))
                }
              />
              <Label htmlFor="size-small" className="text-sm">
                Small (&lt; 1,000 sq ft)
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="size-medium"
                checked={filters.size.medium}
                onCheckedChange={v =>
                  setFilters(prev => ({
                    ...prev,
                    size: { ...prev.size, medium: Boolean(v) },
                  }))
                }
              />
              <Label htmlFor="size-medium" className="text-sm">
                Medium (1,000–5,000 sq ft)
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="size-large"
                checked={filters.size.large}
                onCheckedChange={v =>
                  setFilters(prev => ({
                    ...prev,
                    size: { ...prev.size, large: Boolean(v) },
                  }))
                }
              />
              <Label htmlFor="size-large" className="text-sm">
                Large (&gt; 5,000 sq ft)
              </Label>
            </div>
          </div>
        </div>
      </SidebarContent>
    </Sidebar>
  );
}
