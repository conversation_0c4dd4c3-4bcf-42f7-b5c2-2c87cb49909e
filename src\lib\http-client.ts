import { getSession } from 'next-auth/react';

// HTTP Client with optimized token management
export class HttpClient {
  private baseUrl: string;
  private defaultHeaders: HeadersInit;
  private tokenCache: { token: string | null; timestamp: number } | null = null;
  private readonly CACHE_DURATION = 30 * 1000; // 30 seconds cache

  constructor(baseUrl?: string) {
    this.baseUrl =
      baseUrl ||
      process.env.NEXT_PUBLIC_NESTJS_API_URL ||
      'http://localhost:3001';
    this.defaultHeaders = {
      'Content-Type': 'application/json',
    };
  }

  /**
   * Get the current access token with caching to reduce session calls
   */
  private async getAccessToken(): Promise<string | null> {
    try {
      // Check if we have a valid cached token
      if (
        this.tokenCache &&
        Date.now() - this.tokenCache.timestamp < this.CACHE_DURATION
      ) {
        return this.tokenCache.token;
      }

      // Fetch fresh token from session
      const session = await getSession();
      const token = session?.accessToken || null;

      // Cache the token
      this.tokenCache = {
        token,
        timestamp: Date.now(),
      };

      return token;
    } catch (error) {
      console.error('Failed to get access token:', error);
      // Clear cache on error
      this.tokenCache = null;
      return null;
    }
  }

  /**
   * Clear the token cache (useful when token becomes invalid)
   */
  private clearTokenCache(): void {
    this.tokenCache = null;
  }

  /**
   * Create headers with automatic access token injection
   */
  private async createHeaders(
    customHeaders?: HeadersInit
  ): Promise<HeadersInit> {
    const accessToken = await this.getAccessToken();

    const headers: HeadersInit = {
      ...this.defaultHeaders,
      ...customHeaders,
    };

    // Automatically add Authorization header if access token is available
    if (accessToken) {
      (headers as Record<string, string>).Authorization =
        `Bearer ${accessToken}`;
    }

    return headers;
  }

  /**
   * Make an HTTP request with automatic token injection and better error handling
   */
  private async request<T>(
    endpoint: string,
    options: RequestInit = {},
    retryOn401 = true
  ): Promise<T> {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

    try {
      const headers = await this.createHeaders(options.headers);

      const response = await fetch(`${this.baseUrl}${endpoint}`, {
        ...options,
        headers,
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        // Handle 401 Unauthorized - clear cache and optionally retry once
        if (response.status === 401) {
          console.warn('Request failed with 401 - clearing token cache');
          this.clearTokenCache();

          // Retry once with fresh token if this is the first attempt
          if (retryOn401) {
            console.log('Retrying request with fresh token...');
            return this.request<T>(endpoint, options, false);
          }
        }

        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      // Handle empty responses (like 204 No Content)
      if (
        response.status === 204 ||
        response.headers.get('content-length') === '0'
      ) {
        return {} as T;
      }

      return response.json();
    } catch (error) {
      clearTimeout(timeoutId);

      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error('Request timeout - please try again');
      }

      throw error;
    }
  }

  /**
   * GET request
   */
  async get<T>(endpoint: string, headers?: HeadersInit): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'GET',
      headers,
    });
  }

  /**
   * POST request
   */
  async post<T>(
    endpoint: string,
    data?: unknown,
    headers?: HeadersInit
  ): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'POST',
      headers,
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  /**
   * PUT request
   */
  async put<T>(
    endpoint: string,
    data?: unknown,
    headers?: HeadersInit
  ): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      headers,
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  /**
   * PATCH request
   */
  async patch<T>(
    endpoint: string,
    data?: unknown,
    headers?: HeadersInit
  ): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'PATCH',
      headers,
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  /**
   * DELETE request
   */
  async delete<T>(
    endpoint: string,
    data?: unknown,
    headers?: HeadersInit
  ): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'DELETE',
      headers,
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  /**
   * Upload file with automatic token injection
   */
  async uploadFile<T>(
    endpoint: string,
    file: File,
    additionalData?: Record<string, unknown>,
    headers?: HeadersInit
  ): Promise<T> {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout for file uploads

    try {
      const accessToken = await this.getAccessToken();

      const formData = new FormData();
      formData.append('file', file);

      // Add additional data to form
      if (additionalData) {
        Object.entries(additionalData).forEach(([key, value]) => {
          formData.append(key, String(value));
        });
      }

      const requestHeaders: HeadersInit = {
        ...headers,
      };

      // Add Authorization header if access token is available
      if (accessToken) {
        (requestHeaders as Record<string, string>).Authorization =
          `Bearer ${accessToken}`;
      }

      const response = await fetch(`${this.baseUrl}${endpoint}`, {
        method: 'POST',
        headers: requestHeaders,
        body: formData,
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      return response.json();
    } catch (error) {
      clearTimeout(timeoutId);

      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error('Upload timeout - please try again');
      }

      throw error;
    }
  }
}

// Create singleton instance
export const httpClient = new HttpClient();

// Export individual methods for convenience
export const get = <T>(endpoint: string, headers?: HeadersInit) =>
  httpClient.get<T>(endpoint, headers);

export const post = <T>(
  endpoint: string,
  data?: unknown,
  headers?: HeadersInit
) => httpClient.post<T>(endpoint, data, headers);

export const put = <T>(
  endpoint: string,
  data?: unknown,
  headers?: HeadersInit
) => httpClient.put<T>(endpoint, data, headers);

export const patch = <T>(
  endpoint: string,
  data?: unknown,
  headers?: HeadersInit
) => httpClient.patch<T>(endpoint, data, headers);

export const deleteRequest = <T>(endpoint: string, headers?: HeadersInit) =>
  httpClient.delete<T>(endpoint, headers);

export const uploadFile = <T>(
  endpoint: string,
  file: File,
  additionalData?: Record<string, unknown>,
  headers?: HeadersInit
) => httpClient.uploadFile<T>(endpoint, file, additionalData, headers);
