'use client';

import { But<PERSON> } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Location } from '@/types';
import { Textarea } from './ui/textarea';
import { Avatar, AvatarFallback, AvatarImage } from './ui/avatar';
import { getUserInitials } from '@/lib/utils';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';
// import Image from 'next/image';

interface AddCommentModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  location: Location;
}

export function AddCommentModal({
  open,
  onOpenChange,
  location,
}: AddCommentModalProps) {
  const [comment, setComment] = useState<string>('');
  const handlePostComment = () => {
    console.log('handlePostComment', comment);
    onOpenChange(false);
    toast.success('The comment have been added successfully');
  };

  useEffect(() => {
    setComment('');
  }, [open]);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="p-0">
        <DialogHeader>
          <div className="flex items-start space-x-4 border-b p-4">
            <div className="w-12 h-12 flex items-center justify-center bg-gray-300 rounded-lg text-xs">
              Location
            </div>
            {/* <Image
              src={'https://picsum.photos/350/200?random=1'}
              alt="Cover Image"
              width={48}
              height={48}
              className="rounded-lg"
            /> */}
            <div className="space-y-1">
              <DialogTitle className="text-md font-normal">
                {location.name}
              </DialogTitle>
              <DialogDescription>{location.description}</DialogDescription>
            </div>
          </div>
        </DialogHeader>
        <div className="h-[400px] overflow-y-auto px-4">
          <div className="flex items-start gap-3">
            <Avatar className="h-8 w-8">
              <AvatarImage
                src={
                  'https://img.freepik.com/free-psd/3d-illustration-person-with-sunglasses_23-2149436188.jpg?semt=ais_hybrid&w=740&q=80'
                }
                alt="avatar"
              />
              <AvatarFallback className="text-xs">
                {getUserInitials('admin')}
              </AvatarFallback>
            </Avatar>
            <div className="space-y-1">
              <div className="flex items-center gap-2">
                <span className="font-normal text-[#171717] text-sm">
                  Sarah Chen
                </span>
                <span className="font-normal text-sm">2h ago</span>
              </div>
              <span className="font-normal text-sm">
                Perfect lighting setup here! The natural light from the east
                windows works great for morning shoots.
              </span>
            </div>
          </div>
        </div>
        <div className="p-4 border-t">
          <div className="flex items-start gap-3">
            <Avatar className="h-8 w-8">
              <AvatarImage
                src={
                  'https://img.freepik.com/free-psd/3d-illustration-person-with-sunglasses_23-2149436188.jpg?semt=ais_hybrid&w=740&q=80'
                }
                alt="avatar"
              />
              <AvatarFallback className="text-xs">
                {getUserInitials('admin')}
              </AvatarFallback>
            </Avatar>
            <Textarea
              id="comment"
              value={comment}
              onChange={e => setComment(e.target.value)}
              placeholder="Add a comment..."
              className="w-full min-h-[100px] flex-1"
            />
          </div>
          <div className="flex gap-4 pt-2 justify-end">
            <Button onClick={() => onOpenChange(false)} variant="outline">
              Close
            </Button>
            <Button disabled={!comment} onClick={handlePostComment}>
              Post
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
