'use client';

import { use, useCallback, useEffect, useState } from 'react';
import ReferenceListView from './reference-view';
import Loading from './loading';
import { toast } from 'sonner';
import { useNavbar } from '@/contexts/navbar-context';
import { SearchIcon, ShareIcon, EditIcon } from 'lucide-react';
import { MOCK_DATA } from '@/lib/services/location-service';
import { Location } from '@/types';

export default function ReferenceListFullScreenPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = use(params);
  const { setAdditionalItems, clearNavbarItems } = useNavbar();
  console.log(id);

  const [isLoading, setIsLoading] = useState(true);
  const [locations, setLocations] = useState<Location[]>([]);

  const fetchLocations = useCallback(() => {
    setIsLoading(true);
    setTimeout(() => {
      setLocations(MOCK_DATA);
      setIsLoading(false);
    }, 1000);
  }, []);

  const handleDeleteLocation = (data?: Location) => {
    console.log('delete location:', data);
    toast.success('Location removed successfully.');
    fetchLocations();
  };

  useEffect(() => {
    fetchLocations();
  }, [fetchLocations]);

  // Set navbar items for this page
  useEffect(() => {
    setAdditionalItems([
      {
        id: 'search-locations',
        label: 'Search Locations',
        icon: <SearchIcon className="h-4 w-4" />,
        href: `/scout/reference/${id}/search-locations`,
        variant: 'outline' as const,
      },
      {
        id: 'edit-reference',
        label: 'Edit Reference',
        icon: <EditIcon className="h-4 w-4" />,
        onClick: () => {
          // Handle edit reference
          console.log('Edit reference clicked');
        },
        variant: 'ghost' as const,
      },
      {
        id: 'share-reference',
        label: 'Share',
        icon: <ShareIcon className="h-4 w-4" />,
        onClick: () => {
          // Handle share reference
          console.log('Share reference clicked');
        },
        variant: 'ghost' as const,
      },
    ]);

    // Cleanup navbar items when component unmounts
    return () => {
      clearNavbarItems();
    };
  }, [id, setAdditionalItems, clearNavbarItems]);

  return isLoading ? (
    <Loading />
  ) : (
    <ReferenceListView
      referenceName="Downtown Coffee Shop Scene"
      productionHouse="Netflix Studios"
      dateRange="Jan 15–17, 2025"
      totalReferences={locations.length}
      status="in_progress"
      locations={locations}
      onDeleteLocation={handleDeleteLocation}
    />
  );
}
