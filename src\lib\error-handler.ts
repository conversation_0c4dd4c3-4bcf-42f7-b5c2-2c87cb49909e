/**
 * Error Handler Utility
 *
 * Provides utilities for handling and parsing error responses from the API
 */

/**
 * Extracts the user-friendly error message from various error formats
 *
 * @param error - The error object to parse
 * @param fallbackMessage - Default message if parsing fails
 * @returns The extracted error message
 */
export function extractErrorMessage(
  error: unknown,
  fallbackMessage: string = 'An error occurred'
): string {
  if (!error) {
    return fallbackMessage;
  }

  // If it's already a string, return it
  if (typeof error === 'string') {
    return error;
  }

  // If it's an Error object, try to parse the message
  if (error instanceof Error) {
    try {
      // The httpClient throws errors in format "HTTP 400: {JSON response}"
      // Extract the JSON part after the colon
      const jsonPart = error.message.split(': ').slice(1).join(': ');

      if (jsonPart) {
        const errorData = JSON.parse(jsonPart);

        // Return the message from the JSON response
        if (errorData.message) {
          return errorData.message;
        }

        // If no message field, try other common error fields
        if (errorData.error) {
          return errorData.error;
        }
      }
    } catch {
      // If JSON parsing fails, return the original error message
      return error.message;
    }
  }

  // If it's an object with a message property
  if (typeof error === 'object' && error !== null) {
    const errorObj = error as Record<string, unknown>;

    if (typeof errorObj.message === 'string') {
      return errorObj.message;
    }

    if (typeof errorObj.error === 'string') {
      return errorObj.error;
    }
  }

  return fallbackMessage;
}

/**
 * Error handler that can be used in try-catch blocks
 *
 * @param error - The error to handle
 * @param fallbackMessage - Default message if parsing fails
 * @returns Object with error message and logging info
 */
export function handleError(
  error: unknown,
  fallbackMessage: string = 'An error occurred'
): {
  message: string;
  logError: () => void;
} {
  const message = extractErrorMessage(error, fallbackMessage);

  return {
    message,
    logError: () => {
      console.error('Error details:', error);
    },
  };
}

/**
 * Async error handler for API calls
 *
 * @param apiCall - The async function to execute
 * @param fallbackMessage - Default message if parsing fails
 * @returns Promise that resolves to the API response or throws with parsed error
 */
export async function handleApiError<T>(
  apiCall: () => Promise<T>,
  fallbackMessage: string = 'An error occurred'
): Promise<T> {
  try {
    return await apiCall();
  } catch (error) {
    const errorMessage = extractErrorMessage(error, fallbackMessage);
    throw new Error(errorMessage);
  }
}
