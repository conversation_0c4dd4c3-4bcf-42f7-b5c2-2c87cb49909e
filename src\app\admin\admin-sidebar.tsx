import {
  Sidebar,
  SidebarContent,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
} from '@/components/ui/sidebar';
import { DashboardIcon, UsersIcon, LocationIcon, TagIcon } from '@/lib/icons';
import { Routes } from '@/lib/routes';
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import { useMemo, memo, useCallback } from 'react';
import { toast } from 'sonner';

interface SidebarItemProps {
  href: string;
  icon: React.ReactNode;
  children: React.ReactNode;
  isActive: boolean;
  exists?: boolean;
}

const SidebarItem = memo(function SidebarItem({
  href,
  icon,
  children,
  isActive,
  exists = true,
}: SidebarItemProps) {
  const router = useRouter();

  const handleClick = useCallback(
    (e: React.MouseEvent) => {
      if (!exists) {
        e.preventDefault();
        toast.info('This feature is coming soon!', {
          description: "We're working hard to bring you this functionality.",
        });
        // Navigate to admin dashboard instead
        router.push('/admin');
      }
    },
    [exists, router]
  );

  return (
    <SidebarMenuItem>
      <SidebarMenuButton
        asChild
        isActive={isActive}
        className="leading-4 data-[active=true]:font-normal pl-3 gap-3"
      >
        <Link href={exists ? href : '#'} onClick={handleClick}>
          {icon}
          <span>{children}</span>
        </Link>
      </SidebarMenuButton>
    </SidebarMenuItem>
  );
});

interface SidebarGroupProps {
  label: string;
  children: React.ReactNode;
}

const SidebarGroupComponent = memo(function SidebarGroupComponent({
  label,
  children,
}: SidebarGroupProps) {
  return (
    <SidebarGroup className="py-6 pl-3 pr-6 gap-2">
      <SidebarGroupLabel className="h-6 pl-3 text-base text-sidebar-accent-foreground leading-4">
        {label}
      </SidebarGroupLabel>
      <SidebarGroupContent>
        <SidebarMenu className="gap-2">{children}</SidebarMenu>
      </SidebarGroupContent>
    </SidebarGroup>
  );
});

export default function AdminSidebar() {
  const pathname = usePathname();

  const isActive = useCallback((href: string) => pathname === href, [pathname]);

  const adminItems = useMemo(
    () => [
      {
        href: Routes.ADMIN_DASHBOARD,
        icon: <DashboardIcon className="h-4 w-4" />,
        label: 'Dashboard',
        exists: true,
      },
      {
        href: Routes.ADMIN_USERS,
        icon: <UsersIcon className="h-4 w-4" />,
        label: 'Users',
        exists: true,
      },
      {
        href: Routes.ADMIN_LOCATIONS,
        icon: <LocationIcon className="h-4 w-4" />,
        label: 'Locations',
        exists: true,
      },
      {
        href: Routes.ADMIN_TAGS,
        icon: <TagIcon className="h-4 w-4" />,
        label: 'Tags',
        exists: true,
      },
    ],
    []
  );

  return (
    <Sidebar className="border-r border-sidebar-border sticky top-0 h-full xl:w-[22.5rem]">
      <SidebarContent className="h-full">
        {/* Admin Navigation Group */}
        <SidebarGroupComponent label="Main menu">
          {adminItems.map(item => (
            <SidebarItem
              key={item.href}
              href={item.href}
              icon={item.icon}
              isActive={isActive(item.href)}
              exists={item.exists}
            >
              {item.label}
            </SidebarItem>
          ))}
        </SidebarGroupComponent>
      </SidebarContent>
    </Sidebar>
  );
}
