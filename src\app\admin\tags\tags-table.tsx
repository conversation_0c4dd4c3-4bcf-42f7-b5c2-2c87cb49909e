import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { DeleteIcon, EditIcon } from '@/lib/icons';
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  SortingState,
  useReactTable,
} from '@tanstack/react-table';
import { useMemo, useState } from 'react';
import TagsTableLoading from './tags-table-loading';
import Image from 'next/image';
import { Checkbox } from '@/components/ui/checkbox';
import ConfirmDialog from '@/components/ConfirmDialog';
import EditTagModal from '@/components/EditTagModal';
import { toast } from 'sonner';
import { Tag } from '@/types/tag';
import { tagTypeOptions } from './tags-container';
import { useTagService } from '@/hooks/use-services';

interface TagsTableProps {
  data: Tag[];
  loading: boolean;
  onActionSuccess: () => void;
  checkedList: string[];
  onCheckedListChange: (data: string[]) => void;
}

export default function TagsTable({
  data,
  loading,
  onActionSuccess,
  checkedList,
  onCheckedListChange,
}: TagsTableProps) {
  const tagService = useTagService();
  const [sorting, setSorting] = useState<SortingState>([]);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState<boolean>(false);
  const [tagSelected, setTagSelected] = useState<Tag | null>(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState<boolean>(false);
  const columns: ColumnDef<Tag>[] = useMemo(() => {
    return [
      {
        accessorKey: 'name',
        header: ({ column }) => (
          <Button
            className="p-0"
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          >
            Tag name
          </Button>
        ),
        cell: ({ row }) => (
          <div className="flex items-center space-x-4">
            <Checkbox
              id={`checked-tag-${row.original.id}`}
              checked={checkedList.includes(row.original.id)}
              onCheckedChange={checked => {
                const tagId = row.original.id;
                if (checked) {
                  onCheckedListChange([...checkedList, tagId]);
                } else {
                  const result = checkedList.filter(p => p !== tagId);
                  onCheckedListChange(result);
                }
              }}
            />
            <div className="text-sm font-medium max-w-[107px] break-normal whitespace-normal">
              {row.original.name}
            </div>
          </div>
        ),
      },
      {
        accessorKey: 'type',
        header: ({ column }) => (
          <Button
            className="p-0"
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          >
            Tag type
          </Button>
        ),
        cell: ({ row }) => (
          <span className="capitalize">{row.original.type}</span>
        ),
      },
      {
        id: 'actions',
        header: '',
        cell: ({ row }) => {
          return (
            <div className="flex justify-end gap-2 opacity-0 group-hover/row:opacity-100 transition-opacity">
              <EditIcon
                onClick={() => {
                  setTagSelected(row.original);
                  setIsEditModalOpen(true);
                }}
                className="cursor-pointer"
              />
              <DeleteIcon
                onClick={() => {
                  setTagSelected(row.original);
                  setIsDeleteDialogOpen(true);
                }}
                className="cursor-pointer"
              />
            </div>
          );
        },
      },
    ];
  }, [checkedList, onCheckedListChange]);

  const table = useReactTable({
    data,
    columns,
    state: {
      sorting,
    },
    onSortingChange: setSorting,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
  });
  const handleConfirmDelete = async () => {
    if (tagSelected?.id) {
      try {
        await tagService.deleteTag(tagSelected.id);
        toast.success('Tag removed successfully.');
        onActionSuccess();
      } catch {
        toast.error('Failed to delete tag');
      } finally {
        setIsDeleteDialogOpen(false);
      }
    }
  };

  const handleSaveTag = async (tag: Partial<Tag>) => {
    if (tag.id) {
      try {
        await tagService.updateTag(tag.id, {
          name: tag.name,
          type: tag.type,
          color: tag.color,
        });
        toast.success('Tag updated successfully.');
        onActionSuccess();
      } catch {
        toast.error('Failed to update tag');
      } finally {
        setIsEditModalOpen(false);
      }
    }
  };
  return (
    <>
      {loading ? (
        <TagsTableLoading />
      ) : (
        <Card className="py-0 rounded-md shadow-sm gap-0">
          <CardContent className="px-0">
            <Table>
              <TableHeader>
                {table.getHeaderGroups().map(headerGroup => (
                  <TableRow key={headerGroup.id}>
                    {headerGroup.headers.map(header => (
                      <TableHead
                        className="px-6 bg-[#FAFAFA] rounded-md space-x-4"
                        key={header.id}
                      >
                        {header.id === 'name' && (
                          <Checkbox
                            id="checked-all-tag"
                            checked={
                              checkedList.length > 0 &&
                              data.every(tag => checkedList.includes(tag.id))
                            }
                            onCheckedChange={checked => {
                              if (checked) {
                                const allTagId = data.map(d => d.id);
                                onCheckedListChange(allTagId);
                              } else {
                                onCheckedListChange([]);
                              }
                            }}
                          />
                        )}
                        {header.isPlaceholder
                          ? null
                          : flexRender(
                              header.column.columnDef.header,
                              header.getContext()
                            )}
                      </TableHead>
                    ))}
                  </TableRow>
                ))}
              </TableHeader>
              <TableBody>
                {table.getRowModel().rows?.length ? (
                  table.getRowModel().rows.map(row => (
                    <TableRow key={row.id} className="group/row h-20">
                      {row.getVisibleCells().map(cell => (
                        <TableCell className="px-6" key={cell.id}>
                          {flexRender(
                            cell.column.columnDef.cell,
                            cell.getContext()
                          )}
                        </TableCell>
                      ))}
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell
                      colSpan={columns.length}
                      className="h-[551px] text-center"
                    >
                      <div className="h-full flex flex-col items-center justify-center gap-4">
                        <Image
                          src="/assets/circle-placeholder.svg"
                          alt="Circle Placeholder"
                          width={108}
                          height={108}
                        />
                        <span>No tags yet.</span>
                      </div>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      )}
      <ConfirmDialog
        open={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
        title="Remove tag?"
        description="This tag will be permanently deleted and removed from all locations where it is currently applied."
        confirmText="Remove"
        cancelText="Keep tag"
        onConfirm={handleConfirmDelete}
      />
      <EditTagModal
        tag={tagSelected}
        open={isEditModalOpen}
        onOpenChange={setIsEditModalOpen}
        onSave={handleSaveTag}
        typeOptions={tagTypeOptions}
      />
    </>
  );
}
