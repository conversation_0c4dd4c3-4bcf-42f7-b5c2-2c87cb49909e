'use client';

import * as React from 'react';
import { useMemo, useState } from 'react';
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  SortingState,
  useReactTable,
} from '@tanstack/react-table';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';

type AssetCategory = 'Hardware' | 'Software' | 'Network' | 'Cloud' | 'Other';
type AssetRisk = 'Low' | 'Medium' | 'High' | 'Critical';

type AssetRecord = {
  id: string;
  name: string;
  category: AssetCategory;
  owner: string;
  status: 'Active' | 'Inactive';
  risk: AssetRisk;
};

const MOCK_DATA: AssetRecord[] = Array.from({ length: 10 }).map((_, idx) => {
  const categories: AssetCategory[] = [
    'Hardware',
    'Software',
    'Network',
    'Cloud',
    'Other',
  ];
  const risks: AssetRisk[] = ['Low', 'Medium', 'High', 'Critical'];
  const statuses: Array<AssetRecord['status']> = ['Active', 'Inactive'];
  const category = categories[idx % categories.length];
  const risk = risks[idx % risks.length];
  const status = statuses[idx % statuses.length];
  return {
    id: String(idx + 1),
    name: `${category} Asset ${idx + 1}`,
    category,
    owner: `Owner ${((idx % 8) + 1).toString().padStart(2, '0')}`,
    status,
    risk,
  } satisfies AssetRecord;
});

const PAGE_SIZE = 10;

export default function AssetCategorizationTable() {
  const [sorting, setSorting] = useState<SortingState>([]);
  const [globalFilter, setGlobalFilter] = useState('');
  const [category, setCategory] = useState<string>('all');
  const [status, setStatus] = useState<string>('all');
  const [risk, setRisk] = useState<string>('all');

  const filteredData = useMemo(() => {
    const matchesCategory = (r: AssetRecord) =>
      category === 'all' || r.category === category;
    const matchesStatus = (r: AssetRecord) =>
      status === 'all' || r.status === status;
    const matchesRisk = (r: AssetRecord) => risk === 'all' || r.risk === risk;
    return MOCK_DATA.filter(
      r => matchesCategory(r) && matchesStatus(r) && matchesRisk(r)
    );
  }, [category, status, risk]);

  const columns: ColumnDef<AssetRecord>[] = [
    { accessorKey: 'id', header: 'ID' },
    {
      accessorKey: 'name',
      header: ({ column }) => (
        <Button
          className="p-0"
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          Asset
        </Button>
      ),
      cell: ({ row }) => (
        <span className="font-medium">{row.getValue('name') as string}</span>
      ),
    },
    {
      accessorKey: 'category',
      header: ({ column }) => (
        <Button
          className="p-0"
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          Category
        </Button>
      ),
      cell: ({ row }) => (
        <Badge variant="secondary">{row.getValue('category') as string}</Badge>
      ),
    },
    {
      accessorKey: 'owner',
      header: ({ column }) => (
        <Button
          className="p-0"
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          Owner
        </Button>
      ),
    },
    {
      accessorKey: 'status',
      header: ({ column }) => (
        <Button
          className="p-0"
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          Status
        </Button>
      ),
      cell: ({ row }) => (
        <Badge
          className={
            row.getValue('status') === 'Active'
              ? 'bg-green-500 hover:bg-green-600'
              : 'bg-red-500 hover:bg-red-600'
          }
        >
          {row.getValue('status') as string}
        </Badge>
      ),
    },
    {
      accessorKey: 'risk',
      header: ({ column }) => (
        <Button
          className="p-0"
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          Risk
        </Button>
      ),
      cell: ({ row }) => {
        const value = row.getValue('risk') as AssetRisk;
        const variant =
          value === 'Critical'
            ? 'destructive'
            : value === 'High'
              ? 'default'
              : 'secondary';
        return <Badge variant={variant}>{value}</Badge>;
      },
    },
  ];

  const table = useReactTable({
    data: filteredData,
    columns,
    state: {
      sorting,
      globalFilter,
      pagination: { pageIndex: 0, pageSize: PAGE_SIZE },
    },
    onSortingChange: setSorting,
    onGlobalFilterChange: setGlobalFilter,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
  });

  return (
    <div className="space-y-4 w-[150%]">
      <h1 className="text-2xl font-bold">Asset Categorization</h1>
      <div className="flex flex-col gap-2 sm:flex-row sm:items-center sm:justify-between">
        <Input
          placeholder="Search..."
          value={globalFilter ?? ''}
          onChange={e => setGlobalFilter(e.target.value)}
          className="max-w-sm"
        />

        <div className="flex flex-wrap items-center gap-2">
          <Select value={category} onValueChange={v => setCategory(v)}>
            <SelectTrigger aria-label="Category">
              <SelectValue placeholder="Category" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Categories</SelectItem>
              <SelectItem value="Hardware">Hardware</SelectItem>
              <SelectItem value="Software">Software</SelectItem>
              <SelectItem value="Network">Network</SelectItem>
              <SelectItem value="Cloud">Cloud</SelectItem>
              <SelectItem value="Other">Other</SelectItem>
            </SelectContent>
          </Select>

          <Select value={status} onValueChange={v => setStatus(v)}>
            <SelectTrigger aria-label="Status">
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Statuses</SelectItem>
              <SelectItem value="Active">Active</SelectItem>
              <SelectItem value="Inactive">Inactive</SelectItem>
            </SelectContent>
          </Select>

          <Select value={risk} onValueChange={v => setRisk(v)}>
            <SelectTrigger aria-label="Risk">
              <SelectValue placeholder="Risk" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Risks</SelectItem>
              <SelectItem value="Low">Low</SelectItem>
              <SelectItem value="Medium">Medium</SelectItem>
              <SelectItem value="High">High</SelectItem>
              <SelectItem value="Critical">Critical</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="rounded-2xl border shadow-sm">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map(headerGroup => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map(header => (
                  <TableHead key={header.id}>
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map(row => (
                <TableRow key={row.id}>
                  {row.getVisibleCells().map(cell => (
                    <TableCell key={cell.id}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      <div className="flex items-center justify-between space-x-2">
        <div className="text-sm text-muted-foreground">
          {table.getFilteredRowModel().rows.length} of {filteredData.length}{' '}
          results
        </div>

        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage()}
          >
            Previous
          </Button>

          <div className="flex items-center space-x-1">
            {Array.from({ length: table.getPageCount() }, (_, i) => (
              <Button
                key={i}
                variant={
                  table.getState().pagination.pageIndex === i
                    ? 'default'
                    : 'outline'
                }
                size="sm"
                onClick={() => table.setPageIndex(i)}
                className="w-8 h-8 p-0"
              >
                {i + 1}
              </Button>
            ))}
          </div>

          <Button
            variant="outline"
            size="sm"
            onClick={() => table.nextPage()}
            disabled={!table.getCanNextPage()}
          >
            Next
          </Button>
        </div>
      </div>
    </div>
  );
}
