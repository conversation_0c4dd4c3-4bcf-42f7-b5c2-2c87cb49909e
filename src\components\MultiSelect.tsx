'use client';

import * as React from 'react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { ChevronDown, X } from 'lucide-react';

export type MultiSelectOption = {
  value: string;
  label: string;
  disabled?: boolean;
};

export interface MultiSelectProps {
  options: MultiSelectOption[];
  value: string[];
  onChange: (next: string[]) => void;
  placeholder?: string;
  className?: string;
  emptyMessage?: string;
  maxBadgeCount?: number;
}
export default function MultiSelect({
  options,
  value,
  onChange,
  placeholder = 'Select',
  className,
  emptyMessage = 'No results found',
  maxBadgeCount = 3,
}: MultiSelectProps) {
  const [open, setOpen] = React.useState(false);

  const selectedSet = React.useMemo(() => new Set(value), [value]);
  const selectedOptions = React.useMemo(
    () => options.filter(o => selectedSet.has(o.value)),
    [options, selectedSet]
  );

  const toggle = React.useCallback(
    (val: string) => {
      if (selectedSet.has(val)) {
        onChange(value.filter(v => v !== val));
      } else {
        onChange([...value, val]);
      }
    },
    [onChange, selectedSet, value]
  );

  const clearAll = React.useCallback(() => onChange([]), [onChange]);

  const visibleBadges = selectedOptions.slice(0, maxBadgeCount);
  const overflowCount = Math.max(
    0,
    selectedOptions.length - visibleBadges.length
  );

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          type="button"
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn('w-full justify-between h-10', className)}
        >
          {selectedOptions.length === 0 ? (
            <span className="text-muted-foreground">{placeholder}</span>
          ) : (
            <div className="flex items-center gap-1 flex-1 overflow-hidden">
              {visibleBadges.map(opt => (
                <Badge
                  key={opt.value}
                  variant="secondary"
                  className="truncate max-w-[8rem]"
                >
                  {opt.label}
                </Badge>
              ))}
              {overflowCount > 0 && (
                <Badge variant="secondary">+{overflowCount}</Badge>
              )}
            </div>
          )}
          <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-60" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="p-0 w-[320px]" align="start">
        <Command loop>
          <div className="flex items-center gap-2 px-2 pt-2">
            <CommandInput placeholder="Search..." />
            {value.length > 0 && (
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={clearAll}
              >
                <X className="h-4 w-4" />
                <span className="sr-only">Clear</span>
              </Button>
            )}
          </div>
          <CommandList>
            <CommandEmpty className="py-6 text-center text-sm text-muted-foreground">
              {emptyMessage}
            </CommandEmpty>
            <CommandGroup>
              {options.map(opt => {
                const checked = selectedSet.has(opt.value);
                return (
                  <CommandItem
                    key={opt.value}
                    disabled={opt.disabled}
                    value={`${opt.label} ${opt.value}`}
                    onSelect={() => toggle(opt.value)}
                    className="gap-2"
                  >
                    <Checkbox checked={checked} aria-label={opt.label} />
                    <span className="truncate">{opt.label}</span>
                  </CommandItem>
                );
              })}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
