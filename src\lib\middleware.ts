import { withAuth } from 'next-auth/middleware';
import { NextResponse } from 'next/server';
import {
  ADMIN_ROUTES,
  AUTH_ROUTES,
  PROTECTED_ROUTES,
  RESET_PASSWORD_ROUTES,
  PUBLIC_ROUTES,
  Routes,
} from './routes';
import { Role } from '../types/enum';

export default withAuth(
  function middleware(req) {
    const token = req.nextauth.token;
    const isAuth = !!token;
    const userRole = token?.role as string;
    const pathname = req.nextUrl.pathname;

    // console.log('pathname', pathname);

    // Redirect old /api paths to new /nextapi paths
    if (pathname.startsWith('/api/')) {
      const newPath = pathname.replace('/api/', '/nextapi/');
      const newUrl = new URL(newPath, req.url);
      newUrl.search = req.nextUrl.search;
      return NextResponse.redirect(newUrl);
    }

    // Fast path for static assets and API routes
    if (pathname.startsWith('/_next') || pathname.startsWith('/nextapi')) {
      return NextResponse.next();
    }

    const isAuthPage = AUTH_ROUTES.some(path => pathname.startsWith(path));
    const isAdminPage = ADMIN_ROUTES.some(path => pathname.startsWith(path));
    const isResetPasswordPage = RESET_PASSWORD_ROUTES.some(path =>
      req.nextUrl.pathname.startsWith(path)
    );
    const isProtectedPage = PROTECTED_ROUTES.some(path =>
      pathname.startsWith(path)
    );
    const isPublicPage = PUBLIC_ROUTES.some(path => pathname.startsWith(path));

    // Check for NextAuth callback parameters that indicate a redirect is in progress
    const hasNextAuthParams =
      req.nextUrl.searchParams.has('callbackUrl') ||
      req.nextUrl.searchParams.has('error') ||
      req.nextUrl.searchParams.has('state');

    // Don't interfere with NextAuth redirects
    if (hasNextAuthParams) {
      return NextResponse.next();
    }

    // Allow public pages to pass through without authentication checks
    if (isPublicPage) {
      return NextResponse.next();
    }

    // Only redirect authenticated users away from auth pages if they're not in a redirect flow
    if (isAuth && isAuthPage && !isResetPasswordPage) {
      const redirectUrl =
        userRole === Role.ADMIN ? Routes.ADMIN_DASHBOARD : Routes.DASHBOARD;
      return NextResponse.redirect(new URL(redirectUrl, req.url));
    }

    // Protect protected routes - only allow authenticated users
    if (isProtectedPage && !isAuth) {
      return NextResponse.redirect(new URL(Routes.SIGN_IN, req.url));
    }

    // Protect admin routes - only allow admin users
    if (isAdminPage && isAuth && userRole !== Role.ADMIN) {
      return NextResponse.redirect(new URL(Routes.DASHBOARD, req.url));
    }

    if (pathname === '/' && isAuth) {
      return NextResponse.redirect(
        new URL(
          token.role === Role.ADMIN ? Routes.ADMIN_DASHBOARD : Routes.DASHBOARD,
          req.url
        )
      );
    }

    // For all other routes, authentication is required
    return NextResponse.next();
  },
  {
    callbacks: {
      authorized: ({ token, req }) => {
        const { pathname } = req.nextUrl;
        const isProtectedPage = PROTECTED_ROUTES.some(path =>
          pathname.startsWith(path)
        );
        const isPublicPage = PUBLIC_ROUTES.some(path =>
          pathname.startsWith(path)
        );

        // Allow public pages without authentication
        if (isPublicPage) {
          return true;
        }

        if (isProtectedPage && !token) {
          return false;
        }
        return true;
      },
    },
    pages: {
      signIn: '/sign-in',
    },
  }
);

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - assets (public assets like logos, images, etc.)
     */
    '/((?!api|_next/static|_next/image|favicon.ico|assets).*)',
  ],
};
