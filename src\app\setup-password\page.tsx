'use client';

import { useSearchPara<PERSON>, useRouter } from 'next/navigation';
import { useState, useEffect } from 'react';
import { toastError, toastSuccess } from '@/lib/toast';
import { Heading } from '@/components/ui/typography';
import { Body } from '@/components/ui/typography';
import Image from 'next/image';
import PasswordForm from '@/components/PasswordForm';
import { authService } from '@/lib/services/auth-service';
import { Routes } from '@/lib/routes';
import { handleError } from '@/lib/error-handler';

export default function SetupPasswordPage() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const token = searchParams.get('token');

  useEffect(() => {
    if (error) {
      toastError(error, {
        id: 'setup-password',
      });
    }
  }, [error]);

  const handleSubmit = async (password: string) => {
    if (!token) {
      setError('Invalid setup link. Please check your invitation email.');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      await authService.acceptInvitation({
        token,
        password,
      });

      // Show success toast
      toastSuccess('Account created successfully.');
      router.push(Routes.SIGN_IN);
    } catch (err) {
      const { message } = handleError(
        err,
        'An error occurred while setting password'
      );
      setError(message);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex flex-col items-center w-full h-full">
      <div className="mx-auto px-4 py-16 flex-1 flex flex-col items-center justify-center">
        <div className="mx-auto flex justify-between max-w-[936px] gap-32">
          <div className="flex flex-col items-center justify-center max-w-[456px] px-9 py-10">
            <Heading level={4} className="text-header font-medium mb-2">
              Set up your password
            </Heading>
            <Body className="text-center mb-8">
              Choose a strong password to secure your account.
            </Body>

            <PasswordForm
              termEnable={true}
              buttonText="Create Account"
              onSubmit={handleSubmit}
              isLoading={isLoading}
              error={error}
            />
          </div>
          <div className="flex justify-center">
            <Image
              src="/assets/circle-placeholder.svg"
              alt="Password Setup"
              width={360}
              height={360}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
