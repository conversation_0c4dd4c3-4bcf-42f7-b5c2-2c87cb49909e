# NextAuth.js + NestJS JWT Integration Guide

This guide shows how to configure NextAuth.js to work with a NestJS backend that uses JWT authentication.

## Overview

The integration allows your Next.js frontend to:

- Authenticate users through your NestJS backend
- Handle JWT token refresh automatically
- Make authenticated API calls to your NestJS endpoints
- Maintain secure sessions with proper token management

## Environment Variables

Add these to your `.env.local` file:

```env
# NextAuth Configuration
NEXTAUTH_SECRET=your-nextauth-secret-key-here
NEXTAUTH_URL=http://localhost:3000

# NestJS API Configuration
NESTJS_API_URL=http://localhost:3001
NEXT_PUBLIC_NESTJS_API_URL=http://localhost:3001
```

## NestJS Backend Requirements

Your NestJS backend should have these endpoints:

### 1. Login Endpoint

```
POST /auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response:**

```json
{
  "access_token": "jwt-access-token",
  "refresh_token": "jwt-refresh-token",
  "user": {
    "id": "1",
    "email": "<EMAIL>",
    "name": "User Name",
    "role": "admin",
    "avatar": "https://example.com/avatar.jpg"
  }
}
```

### 2. Refresh Token Endpoint

```
POST /auth/refresh
Content-Type: application/json

{
  "refresh_token": "jwt-refresh-token"
}
```

**Response:**

```json
{
  "access_token": "new-jwt-access-token",
  "refresh_token": "new-jwt-refresh-token"
}
```

### 3. Protected Endpoints

All protected endpoints should:

- Accept `Authorization: Bearer <access_token>` header
- Return 401 for invalid/expired tokens
- Return user data or business logic

## How It Works

### 1. Authentication Flow

1. User submits credentials on sign-in page
2. NextAuth.js calls your NestJS `/auth/login` endpoint
3. NestJS validates credentials and returns JWT tokens
4. NextAuth.js stores tokens securely and creates a session
5. User is redirected to the dashboard

### 2. Token Refresh

1. NextAuth.js automatically detects when access token expires
2. It calls your NestJS `/auth/refresh` endpoint with refresh token
3. New tokens are stored and session is updated
4. User continues seamlessly without re-authentication

### 3. API Calls

1. Components use `useSession()` to get current session
2. Server-side routes use `getServerSession()` to get session
3. Access token is automatically included in API calls
4. NextAuth.js handles token refresh transparently

## Usage Examples

### Client-Side Component

```tsx
'use client';

import { useSession } from 'next-auth/react';

export default function MyComponent() {
  const { data: session } = useSession();

  const fetchData = async () => {
    const response = await fetch('/api/protected-endpoint', {
      headers: {
        'Content-Type': 'application/json',
      },
    });
    // NextAuth.js automatically includes the access token
  };

  return (
    <div>
      <p>Welcome, {session?.user?.name}!</p>
      <p>Role: {session?.user?.role}</p>
    </div>
  );
}
```

### Server-Side API Route

```tsx
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

export async function GET() {
  const session = await getServerSession(authOptions);

  if (!session?.accessToken) {
    return Response.json({ error: 'Unauthorized' }, { status: 401 });
  }

  // Make authenticated request to NestJS
  const response = await fetch(`${process.env.NESTJS_API_URL}/api/data`, {
    headers: {
      Authorization: `Bearer ${session.accessToken}`,
    },
  });

  const data = await response.json();
  return Response.json(data);
}
```

### Protected Route Middleware

```tsx
// middleware.ts
export { default } from 'next-auth/middleware';

export const config = {
  matcher: ['/dashboard/:path*', '/profile/:path*'],
};
```

## Security Features

- **Automatic token refresh**: No manual token management needed
- **Secure token storage**: Tokens stored in HTTP-only cookies
- **CSRF protection**: Built-in protection against CSRF attacks
- **Session validation**: Automatic session validation on each request
- **Error handling**: Graceful handling of authentication errors

## Troubleshooting

### Common Issues

1. **CORS Errors**: Ensure your NestJS backend allows requests from your Next.js domain
2. **Token Expiry**: Check that your JWT expiration times are reasonable (1 hour for access, 7 days for refresh)
3. **Session Not Persisting**: Verify `NEXTAUTH_SECRET` is set and consistent
4. **API Calls Failing**: Check that access tokens are being included in requests

### Debug Mode

Enable debug mode in development:

```tsx
// lib/auth.ts
export const authOptions: NextAuthOptions = {
  // ... other config
  debug: process.env.NODE_ENV === 'development',
};
```

## Migration from Mock Auth

If you're migrating from the mock authentication:

1. Update your sign-in form to use the new credentials
2. Remove any hardcoded user data
3. Update your API routes to use the new session structure
4. Test the authentication flow end-to-end

## Next Steps

1. Set up your NestJS backend with JWT authentication
2. Configure the environment variables
3. Test the authentication flow
4. Update your components to use the new session structure
5. Add protected routes using NextAuth.js middleware
