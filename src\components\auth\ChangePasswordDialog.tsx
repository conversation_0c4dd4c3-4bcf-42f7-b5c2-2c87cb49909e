'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  <PERSON>alogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { toast } from 'sonner';
import { Key } from 'lucide-react';

interface ChangePasswordDialogProps {
  trigger?: React.ReactNode;
  onSuccess?: () => void;
}

interface PasswordData {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

interface ValidationErrors {
  currentPassword?: string;
  newPassword?: string;
  confirmPassword?: string;
}

export function ChangePasswordDialog({
  trigger,
  onSuccess,
}: ChangePasswordDialogProps) {
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [passwordData, setPasswordData] = useState<PasswordData>({
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
  });
  const [errors, setErrors] = useState<ValidationErrors>({});
  const [touched, setTouched] = useState<Record<keyof PasswordData, boolean>>({
    currentPassword: false,
    newPassword: false,
    confirmPassword: false,
  });

  // Validation function
  const validateField = (
    field: keyof PasswordData,
    value: string
  ): string | undefined => {
    switch (field) {
      case 'currentPassword':
        if (!value) return 'Current password is required';
        if (value.length < 1) return 'Current password is required';
        break;
      case 'newPassword':
        if (!value) return 'New password is required';
        if (value.length < 6) return 'Password must be at least 6 characters';
        if (value === passwordData.currentPassword)
          return 'New password must be different from current password';
        break;
      case 'confirmPassword':
        if (!value) return 'Please confirm your new password';
        if (value !== passwordData.newPassword) return 'Passwords do not match';
        break;
    }
    return undefined;
  };

  // Validate all fields
  const validateForm = (): boolean => {
    const newErrors: ValidationErrors = {};
    let isValid = true;

    Object.keys(passwordData).forEach(key => {
      const field = key as keyof PasswordData;
      const error = validateField(field, passwordData[field]);
      if (error) {
        newErrors[field] = error;
        isValid = false;
      }
    });

    setErrors(newErrors);
    return isValid;
  };

  // Handle field change with validation
  const handleFieldChange = (field: keyof PasswordData, value: string) => {
    setPasswordData(prev => ({ ...prev, [field]: value }));

    // Mark field as touched
    setTouched(prev => ({ ...prev, [field]: true }));

    // Clear error for this field
    setErrors(prev => ({ ...prev, [field]: undefined }));
  };

  // Validate field on blur
  const handleFieldBlur = (field: keyof PasswordData) => {
    setTouched(prev => ({ ...prev, [field]: true }));
    const error = validateField(field, passwordData[field]);
    setErrors(prev => ({ ...prev, [field]: error }));
  };

  // Debounced validation for new password
  useEffect(() => {
    if (touched.newPassword && passwordData.newPassword) {
      const timer = setTimeout(() => {
        const error = validateField('newPassword', passwordData.newPassword);
        setErrors(prev => ({ ...prev, newPassword: error }));
      }, 500);

      return () => clearTimeout(timer);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [passwordData.newPassword, touched.newPassword]);

  // Validate confirm password when new password changes
  useEffect(() => {
    if (touched.confirmPassword && passwordData.confirmPassword) {
      const error = validateField(
        'confirmPassword',
        passwordData.confirmPassword
      );
      setErrors(prev => ({ ...prev, confirmPassword: error }));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    passwordData.newPassword,
    passwordData.confirmPassword,
    touched.confirmPassword,
  ]);

  const handleSubmit = async () => {
    // Mark all fields as touched
    setTouched({
      currentPassword: true,
      newPassword: true,
      confirmPassword: true,
    });

    if (!validateForm()) {
      return;
    }

    setLoading(true);
    try {
      const response = await fetch('/nextapi/profile/change-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          currentPassword: passwordData.currentPassword,
          newPassword: passwordData.newPassword,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to change password');
      }

      toast.success('Password changed successfully!');
      setOpen(false);
      setPasswordData({
        currentPassword: '',
        newPassword: '',
        confirmPassword: '',
      });
      setErrors({});
      setTouched({
        currentPassword: false,
        newPassword: false,
        confirmPassword: false,
      });

      onSuccess?.();
    } catch (error) {
      console.error('Error changing password:', error);
      toast.error(
        error instanceof Error ? error.message : 'Failed to change password'
      );
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    setOpen(false);
    setPasswordData({
      currentPassword: '',
      newPassword: '',
      confirmPassword: '',
    });
    setErrors({});
    setTouched({
      currentPassword: false,
      newPassword: false,
      confirmPassword: false,
    });
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {trigger || (
          <Button variant="outline" className="flex items-center space-x-2">
            <Key className="h-4 w-4" />
            <span>Change Password</span>
          </Button>
        )}
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Change Password</DialogTitle>
          <DialogDescription>
            Enter your current password and choose a new one.
          </DialogDescription>
        </DialogHeader>
        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="current-password">Current Password</Label>
            <Input
              id="current-password"
              type="password"
              value={passwordData.currentPassword}
              onChange={e =>
                handleFieldChange('currentPassword', e.target.value)
              }
              onBlur={() => handleFieldBlur('currentPassword')}
              placeholder="Enter your current password"
              className={
                touched.currentPassword && errors.currentPassword
                  ? 'border-red-500'
                  : ''
              }
            />
            {touched.currentPassword && errors.currentPassword && (
              <p className="text-sm text-red-500">{errors.currentPassword}</p>
            )}
          </div>
          <div className="space-y-2">
            <Label htmlFor="new-password">New Password</Label>
            <Input
              id="new-password"
              type="password"
              value={passwordData.newPassword}
              onChange={e => handleFieldChange('newPassword', e.target.value)}
              onBlur={() => handleFieldBlur('newPassword')}
              placeholder="Enter your new password"
              className={
                touched.newPassword && errors.newPassword
                  ? 'border-red-500'
                  : ''
              }
            />
            {touched.newPassword && errors.newPassword && (
              <p className="text-sm text-red-500">{errors.newPassword}</p>
            )}
          </div>
          <div className="space-y-2">
            <Label htmlFor="confirm-password">Confirm New Password</Label>
            <Input
              id="confirm-password"
              type="password"
              value={passwordData.confirmPassword}
              onChange={e =>
                handleFieldChange('confirmPassword', e.target.value)
              }
              onBlur={() => handleFieldBlur('confirmPassword')}
              placeholder="Confirm your new password"
              className={
                touched.confirmPassword && errors.confirmPassword
                  ? 'border-red-500'
                  : ''
              }
            />
            {touched.confirmPassword && errors.confirmPassword && (
              <p className="text-sm text-red-500">{errors.confirmPassword}</p>
            )}
          </div>
          <div className="flex space-x-3 pt-4">
            <Button
              onClick={handleSubmit}
              disabled={loading}
              className="flex-1"
            >
              {loading ? 'Changing...' : 'Change Password'}
            </Button>
            <Button variant="outline" onClick={handleCancel} disabled={loading}>
              Cancel
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
