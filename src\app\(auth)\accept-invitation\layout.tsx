import type { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Accept Invitation | Scoutr',
  description:
    'Accept your invitation to join Scoutr and complete your account setup',
  openGraph: {
    title: 'Accept Invitation | Scoutr',
    description:
      'Accept your invitation to join Scoutr and complete your account setup',
  },
};

export default function InvitationLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return <>{children}</>;
}
