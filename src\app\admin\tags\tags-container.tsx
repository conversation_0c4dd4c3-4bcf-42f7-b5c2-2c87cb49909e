'use client';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { SearchIcon } from '@/lib/icons';
import { ChevronLeft } from 'lucide-react';
import { ChevronRight } from 'lucide-react';
import { Plus } from 'lucide-react';
import { useCallback, useEffect, useMemo, useState } from 'react';
import Pagination from '@/components/Pagination';
import TagsTable from './tags-table';
import AddTagModal from '@/components/AddTagModal';
import { toast } from 'sonner';
import { Tag } from '@/types/tag';
import { useTagService } from '@/hooks/use-services';
import { ChevronDown } from 'lucide-react';
import ConfirmDialog from '@/components/ConfirmDialog';
import { TagType } from '@/types/enum';

const LIMIT = 6;

export const tagTypeOptions = [
  { value: TagType.CATEGORY, label: 'Category' },
  { value: TagType.SUB_CATEGORY, label: 'Sub-category' },
  { value: TagType.STYLE, label: 'Style' },
];

export default function TagsContainer() {
  const tagService = useTagService();
  const [search, setSearch] = useState<string>('');
  const [type, setType] = useState<string>('all');
  const [page, setPage] = useState<number>(1);
  const [loading, setLoading] = useState<boolean>(true);
  const [totalPage, setTotalPage] = useState<number>(1);
  const [tagsData, setTagsData] = useState<Tag[]>([]);
  const [isAddTagModalOpen, setIsAddTagModalOpen] = useState<boolean>(false);
  const [checkedList, setCheckedList] = useState<string[]>([]);
  const [isBulkDeleteDialogOpen, setIsBulkDeleteDialogOpen] =
    useState<boolean>(false);

  const handleAddTag = async (tag: Partial<Tag>) => {
    try {
      const { name, color } = tag;
      await tagService.createTag({ name, type: tag.type, color });
      setIsAddTagModalOpen(false);
      toast.success('Tag created successfully.');
      fetchTags(page, search, type);
    } catch {
      toast.error('Failed to add new tag');
    }
  };

  const handleBulkDelete = async () => {
    try {
      if (!checkedList.length) return;
      await tagService.bulkDeleteTags(checkedList);
      toast.success('Bulk delete tags successfully.');
      setCheckedList([]);
      setPage(1);
      fetchTags(1, search, type);
    } catch {
      toast.error('Failed to bulk delete tags');
    }
  };

  const fetchTags = useCallback(
    async (page: number, search?: string, type?: string) => {
      try {
        setLoading(true);
        const response = await tagService.getTags({
          page,
          search,
          type,
          limit: LIMIT,
        });
        setTagsData(response.data);
        setTotalPage(response.meta.totalPages);
      } catch {
        toast.error('Failed to get tags');
      } finally {
        setLoading(false);
      }
    },
    [tagService]
  );

  const tagsMain = useMemo(
    () => (
      <TagsTable
        data={tagsData}
        loading={loading}
        onActionSuccess={() => {
          setPage(1);
          fetchTags(1, search, type);
        }}
        checkedList={checkedList}
        onCheckedListChange={setCheckedList}
      />
    ),
    [checkedList, fetchTags, loading, search, tagsData, type]
  );

  useEffect(() => {
    fetchTags(page, search, type);
  }, [fetchTags, page, search, type]);

  return (
    <>
      <div className="space-y-6">
        <h1 className="text-2xl font-bold">Tags</h1>
        <div className="text-base">
          Add, update, and organize all locations.
        </div>
        <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
          <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:flex-1">
            <div className="relative w-full md:w-[180px] xl:w-[256px]">
              <SearchIcon className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search by name..."
                value={search ?? ''}
                onChange={e => setSearch(e.target.value)}
                className="pl-9"
              />
            </div>
            <div className="flex flex-wrap items-center gap-4">
              <Select value={type} onValueChange={v => setType(v)}>
                <SelectTrigger aria-label="Tags">
                  <SelectValue placeholder="All Tags" />
                </SelectTrigger>
                <SelectContent>
                  {[{ value: 'all', label: 'All Tags' }, ...tagTypeOptions].map(
                    type => (
                      <SelectItem
                        className="capitalize"
                        key={type.value}
                        value={type.value}
                      >
                        {type.label}
                      </SelectItem>
                    )
                  )}
                </SelectContent>
              </Select>
            </div>
          </div>
          <div className="flex items-center gap-4">
            <Button
              variant="outline"
              className="w-[160px] h-[42px] flex justify-between items-center"
              onClick={() => {
                if (checkedList.length) setIsBulkDeleteDialogOpen(true);
                else toast.info('Please select one or more tags.');
              }}
            >
              Bulk Actions <ChevronDown />
            </Button>
            <Button
              variant="default"
              className="w-[205px] h-[42px]"
              onClick={() => setIsAddTagModalOpen(true)}
            >
              <Plus /> Create new tag
            </Button>
          </div>
        </div>
        {tagsMain}
        <div className="mt-6 flex items-center justify-center">
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPage(Math.max(page - 1, 1))}
              disabled={page === 1}
              className="w-9 h-9 p-0"
            >
              <ChevronLeft />
            </Button>
            <Pagination page={page} totalPage={totalPage} setPage={setPage} />
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPage(Math.min(page + 1, totalPage))}
              disabled={page === totalPage}
              className="w-9 h-9 p-0"
            >
              <ChevronRight />
            </Button>
          </div>
        </div>
      </div>
      <AddTagModal
        open={isAddTagModalOpen}
        onOpenChange={setIsAddTagModalOpen}
        onSave={handleAddTag}
        typeOptions={tagTypeOptions}
      />
      <ConfirmDialog
        open={isBulkDeleteDialogOpen}
        onOpenChange={setIsBulkDeleteDialogOpen}
        title="Bulk delete tags?"
        description="These tags will be permanently deleted and removed from all locations where they are currently applied."
        confirmText="Remove"
        cancelText="Keep tags"
        onConfirm={handleBulkDelete}
      />
    </>
  );
}
