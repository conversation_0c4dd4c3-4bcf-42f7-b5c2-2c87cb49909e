import { NextAuthOptions } from 'next-auth';
import { JWT } from 'next-auth/jwt';
import CredentialsProvider from 'next-auth/providers/credentials';
import { Role } from '@/types/enum';
import { authService, type AuthenticatedUser } from './services';

export const authOptions: NextAuthOptions = {
  providers: [
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' },
      },
      async authorize(credentials): Promise<AuthenticatedUser | null> {
        if (!credentials?.email || !credentials?.password) {
          return null;
        }

        try {
          // Use the authentication service for complete flow
          const authenticatedUser = await authService.authenticate({
            email: credentials.email,
            password: credentials.password,
          });

          return authenticatedUser;
        } catch {
          return null;
        }
      },
    }),
  ],
  session: {
    strategy: 'jwt',
    maxAge: 24 * 60 * 60, // 24 hours
    updateAge: 5 * 60, // 5 minutes - check more frequently but don't refresh unless needed
  },
  secret: process.env.NEXTAUTH_SECRET,
  callbacks: {
    async redirect({ url, baseUrl }) {
      // If url is a relative path, make it absolute
      if (url.startsWith('/')) {
        return `${baseUrl}${url}`;
      }
      // If url is on the same origin, allow it
      if (new URL(url).origin === baseUrl) {
        return url;
      }
      // Default redirect to home page (middleware will handle role-based routing)
      return baseUrl;
    },
    async jwt({ token, user, account }): Promise<JWT> {
      // Initial sign in - store all user data and tokens
      if (account && user) {
        const authenticatedUser = user as AuthenticatedUser;
        return {
          ...token,
          sub: user.id,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          accessToken: authenticatedUser.accessToken,
          refreshToken: authenticatedUser.refreshToken,
          role: user.role,
          avatar: user.avatar,
          accessTokenExpires: authenticatedUser.accessTokenExpires,
          refreshTokenExpires: authenticatedUser.refreshTokenExpires,
        };
      }

      // Return previous token if the access token has not expired yet
      // Add 5 minute buffer to prevent edge cases
      const fiveMinutesInMs = 5 * 60 * 1000;
      if (Date.now() < (token.accessTokenExpires as number) - fiveMinutesInMs) {
        return token;
      }

      // Access token has expired, try to refresh it
      return refreshAccessToken(token);
    },
    async session({ session, token }) {
      if (token) {
        session.user.id = token.sub!;
        session.user.email = token.email as string;
        session.user.firstName = token.firstName as string;
        session.user.lastName = token.lastName as string;
        session.user.role = token.role as Role;
        session.user.avatar = token.avatar as string;
        session.accessToken = token.accessToken as string;
        session.error = token.error as string;
      }
      return session;
    },
  },
  pages: {
    signIn: '/sign-in',
  },
  debug: process.env.NODE_ENV === 'development',
  useSecureCookies: process.env.NODE_ENV === 'production',
};

// Simplified token refresh function - single attempt, fail fast
async function refreshAccessToken(token: JWT): Promise<JWT> {
  try {
    if (!token.refreshToken) {
      throw new Error('No refresh token available');
    }

    console.log('Refreshing access token...');
    const refreshed = await authService.refreshToken(token.refreshToken as string);

    console.log('Token refresh successful');
    return {
      ...token,
      accessToken: refreshed.accessToken,
      accessTokenExpires: refreshed.accessTokenExpireTime * 1000,
      refreshToken: refreshed.refreshToken ?? token.refreshToken,
      refreshTokenExpires: refreshed.refreshTokenExpireTime * 1000,
      error: undefined,
    };
  } catch (error) {
    console.error('Token refresh failed:', error);

    // Return token with error to trigger logout
    return {
      ...token,
      accessToken: undefined,
      refreshToken: undefined,
      accessTokenExpires: 0,
      refreshTokenExpires: 0,
      error: 'RefreshAccessTokenError',
    };
  }
}

// Re-export the authentication service for convenience
export { authService } from './services';
