'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Routes } from '@/lib/routes';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Heading, Body } from '@/components/ui/typography';
import Image from 'next/image';
import { toastSuccess, toastError } from '@/lib/toast';
import { authService } from '@/lib/services/auth-service';
import { handleError } from '@/lib/error-handler';

const forgotPasswordSchema = z.object({
  email: z
    .string()
    .min(1, 'Email is required')
    .email('Please enter a valid email address'),
});

type ForgotPasswordFormData = z.infer<typeof forgotPasswordSchema>;

export default function ForgotPasswordPage() {
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const router = useRouter();

  const {
    register,
    handleSubmit,
    formState: { errors, isValid },
  } = useForm<ForgotPasswordFormData>({
    resolver: zodResolver(forgotPasswordSchema),
    mode: 'onChange',
  });

  const onSubmit = async (data: ForgotPasswordFormData) => {
    setLoading(true);

    try {
      await authService.forgotPassword({ email: data.email });
      setSuccess(true);
      toastSuccess('Reset link sent if email exists.');
    } catch (error) {
      const { message } = handleError(
        error,
        'An error occurred while sending reset link'
      );
      toastError(message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="flex flex-col items-center justify-center w-full h-full">
      <div className="mx-auto px-4 py-16 flex-1 flex flex-col items-center justify-center">
        <div className="mx-auto flex justify-between max-w-[1032px] w-full gap-54">
          <div className="flex flex-col items-center justify-between max-w-[456px]">
            {success ? (
              <Card className="w-full max-w-md shadow-none border-none gap-3 py-4">
                <CardHeader className="text-center">
                  <CardTitle className="text-2xl font-bold">
                    Check Your Email
                  </CardTitle>
                  <CardDescription>
                    We&apos;ve sent you a password reset link.
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <p className="text-center text-sm text-gray-600">
                    Please check your email and click the link to reset your
                    password. If you don&apos;t see it, check your spam folder.
                  </p>
                  <Button
                    onClick={() => router.push(Routes.SIGN_IN)}
                    className="w-full"
                  >
                    Back to Sign In
                  </Button>
                </CardContent>
              </Card>
            ) : (
              <>
                <div className="max-w-[395px] mb-12">
                  <Heading
                    level={4}
                    className="text-header text-center font-medium mb-2"
                  >
                    Forgot your password?
                  </Heading>
                  <Body className="text-center">
                    No worries! It happens to the best of us. Enter your email
                    address and we&apos;ll send you a link to reset your
                    password.
                  </Body>
                </div>
                <form
                  onSubmit={handleSubmit(onSubmit)}
                  className="space-y-12 max-w-[456px] w-full"
                >
                  <div className="space-y-2">
                    <Label htmlFor="email" className="text-sub-header">
                      Email
                    </Label>
                    <Input
                      id="email"
                      type="email"
                      placeholder="Enter your email"
                      {...register('email')}
                      className={
                        errors.email
                          ? 'border-red-500 focus:border-red-500'
                          : ''
                      }
                    />
                    {errors.email && (
                      <Alert variant="destructive" className="py-2">
                        <AlertDescription className="text-sm">
                          {errors.email.message}
                        </AlertDescription>
                      </Alert>
                    )}
                  </div>

                  <Button
                    type="submit"
                    className="w-full"
                    disabled={loading || !isValid}
                  >
                    {loading ? 'Sending...' : 'Send reset link'}
                  </Button>
                </form>
                <div className="mt-6 text-center text-sm text-gray-600">
                  <p>
                    Remember your password?{' '}
                    <Link href={Routes.SIGN_IN} className="underline">
                      Back to sign in
                    </Link>
                  </p>
                </div>
              </>
            )}
          </div>
          <div className="flex justify-center">
            <Image
              src="/assets/circle-placeholder.svg"
              alt="Password Setup"
              width={360}
              height={360}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
