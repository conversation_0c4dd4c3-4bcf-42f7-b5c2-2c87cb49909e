'use client';

import * as React from 'react';
import {
  Di<PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import SimpleMultiSelect from './SimpleMultiSelect';
import { FormEvent, useCallback, useEffect, useState } from 'react';
import { toast } from 'sonner';
import { useTagService } from '@/hooks/use-services';
import { TagOption } from '@/types/tag';

interface BulkAddTagsModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSave: (tags: TagOption[]) => void;
}

export default function BulkAddTagsModal({
  open,
  onOpenChange,
  onSave,
}: BulkAddTagsModalProps) {
  const tagService = useTagService();
  const [tagIds, setTagIds] = useState<string[]>([]);
  const [tagOption, setTagOption] = useState<
    {
      value: string;
      label: string;
      color?: string;
    }[]
  >([]);

  const handleSubmit = (e: FormEvent) => {
    e.preventDefault();
    if (!tagIds.length) return;
    const result = tagIds.map(tagId => {
      const tag = tagOption.find(tag => tag.value === tagId);
      return tag
        ? { value: tag.value, label: tag.label, color: tag.color }
        : ({} as TagOption);
    });
    onSave(result);
    onOpenChange(false);
  };

  const fetchStyleTags = useCallback(async () => {
    try {
      const response = await tagService.getTags({ type: 'style' });
      setTagOption(
        response.data.map(data => ({
          value: data.id,
          label: data.name,
          color: data.color,
        }))
      );
    } catch {
      toast.error('Failed to get tag styles');
    }
  }, [tagService]);

  useEffect(() => {
    fetchStyleTags();
  }, [fetchStyleTags]);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[329px]">
        <DialogHeader>
          <DialogTitle>Bulk add tags</DialogTitle>
          <DialogDescription className="text-sm">
            Add tags to multiple locations.
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label
              htmlFor="type"
              className="text-sm font-medium text-foreground"
            >
              Tags
            </Label>
            <SimpleMultiSelect
              options={tagOption}
              value={tagIds}
              onChange={setTagIds}
              placeholder="Select tags"
              groupLabel="Style"
              className="w-full sm:w-[279px]"
            />
          </div>

          <DialogFooter className="mt-6">
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={tagIds.length === 0}>
              Save Changes
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
