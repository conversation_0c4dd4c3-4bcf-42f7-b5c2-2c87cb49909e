'use client';

import React, { useRef, useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { AlertCircle, Upload, X } from 'lucide-react';

export interface FileValidation {
  maxSize?: number; // in bytes
  allowedTypes?: string[]; // MIME types
  maxFiles?: number; // for multiple files
}

export interface FileInputProps {
  onFileSelect: (files: File[]) => void;
  onValidationError?: (error: string) => void;
  validation?: FileValidation;
  accept?: string;
  multiple?: boolean;
  disabled?: boolean;
  placeholder?: string;
  className?: string;
  children?: React.ReactNode;
}

export function FileInput({
  onFileSelect,
  onValidationError,
  validation = {},
  accept,
  multiple = false,
  disabled = false,
  placeholder = 'Select files...',
  className = '',
  children,
}: FileInputProps) {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [validationError, setValidationError] = useState<string>('');

  const {
    maxSize = 10 * 1024 * 1024, // 10MB default
    allowedTypes = ['image/jpeg', 'image/jpg', 'image/png'], // JPG/PNG default
    maxFiles = 1,
  } = validation;

  const validateFile = (file: File): string | null => {
    // Check file type
    if (allowedTypes.length > 0 && !allowedTypes.includes(file.type)) {
      return `File "${file.name}" is not a supported format. Allowed types: ${allowedTypes
        .map(type => type.split('/')[1].toUpperCase())
        .join(', ')}`;
    }

    // Check file size
    if (file.size > maxSize) {
      const maxSizeMB = Math.round(maxSize / (1024 * 1024));
      return `File "${file.name}" is too large. Maximum size: ${maxSizeMB}MB`;
    }

    return null;
  };

  const validateFiles = (
    files: FileList | null
  ): { valid: File[]; errors: string[] } => {
    if (!files || files.length === 0) {
      return { valid: [], errors: [] };
    }

    const fileArray = Array.from(files);
    const errors: string[] = [];
    const validFiles: File[] = [];

    // Check max files limit
    if (fileArray.length > maxFiles) {
      errors.push(`Too many files selected. Maximum allowed: ${maxFiles}`);
      return { valid: [], errors };
    }

    // Validate each file
    fileArray.forEach(file => {
      const error = validateFile(file);
      if (error) {
        errors.push(error);
      } else {
        validFiles.push(file);
      }
    });

    return { valid: validFiles, errors };
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    const { valid, errors } = validateFiles(files);

    // Clear previous state
    setValidationError('');
    setSelectedFiles([]);

    if (errors.length > 0) {
      const errorMessage = errors.join('; ');
      setValidationError(errorMessage);
      onValidationError?.(errorMessage);
      return;
    }

    if (valid.length > 0) {
      setSelectedFiles(valid);
      onFileSelect(valid);
    }
  };

  const handleButtonClick = () => {
    fileInputRef.current?.click();
  };

  const clearFiles = () => {
    setSelectedFiles([]);
    setValidationError('');
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getAcceptString = (): string => {
    if (accept) return accept;
    return allowedTypes.join(',');
  };

  return (
    <div className={`space-y-3 ${className}`}>
      {/* Hidden file input */}
      <Input
        ref={fileInputRef}
        type="file"
        accept={getAcceptString()}
        multiple={multiple}
        onChange={handleFileChange}
        disabled={disabled}
        className="hidden"
      />

      {/* Custom file input button */}
      {children ? (
        <div onClick={handleButtonClick} className="cursor-pointer">
          {children}
        </div>
      ) : (
        <Button
          type="button"
          variant="outline"
          onClick={handleButtonClick}
          disabled={disabled}
          className="w-full justify-center"
        >
          <Upload className="mr-2 h-4 w-4" />
          {placeholder}
        </Button>
      )}

      {/* Selected files display */}
      {selectedFiles.length > 0 && (
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-gray-700">
              Selected Files ({selectedFiles.length})
            </span>
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={clearFiles}
              className="h-6 w-6 p-0"
            >
              <X className="h-3 w-3" />
            </Button>
          </div>
          <div className="space-y-1">
            {selectedFiles.map((file, index) => (
              <div
                key={index}
                className="flex items-center justify-between rounded-md bg-gray-50 p-2 text-sm"
              >
                <div className="flex-1 min-w-0">
                  <p className="truncate font-medium text-gray-900">
                    {file.name}
                  </p>
                  <p className="text-gray-500">{formatFileSize(file.size)}</p>
                </div>
                <div className="ml-2 flex-shrink-0">
                  <span className="inline-flex items-center rounded-full bg-green-100 px-2 py-1 text-xs font-medium text-green-800">
                    Valid
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Validation error display */}
      {validationError && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{validationError}</AlertDescription>
        </Alert>
      )}
    </div>
  );
}

// Example usage components
export function ImageUploadInput({
  onFileSelect,
  onValidationError,
  maxSize = 10 * 1024 * 1024, // 10MB
  multiple = false,
}: {
  onFileSelect: (files: File[]) => void;
  onValidationError?: (error: string) => void;
  maxSize?: number;
  multiple?: boolean;
}) {
  return (
    <FileInput
      onFileSelect={onFileSelect}
      onValidationError={onValidationError}
      validation={{
        maxSize,
        allowedTypes: ['image/jpeg', 'image/jpg', 'image/png'],
        maxFiles: multiple ? 10 : 1,
      }}
      accept="image/jpeg,image/jpg,image/png"
      multiple={multiple}
      placeholder={multiple ? 'Select images...' : 'Select an image...'}
    />
  );
}

export function DocumentUploadInput({
  onFileSelect,
  onValidationError,
  maxSize = 50 * 1024 * 1024, // 50MB
  multiple = false,
}: {
  onFileSelect: (files: File[]) => void;
  onValidationError?: (error: string) => void;
  maxSize?: number;
  multiple?: boolean;
}) {
  return (
    <FileInput
      onFileSelect={onFileSelect}
      onValidationError={onValidationError}
      validation={{
        maxSize,
        allowedTypes: [
          'application/pdf',
          'application/msword',
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
          'text/plain',
        ],
        maxFiles: multiple ? 5 : 1,
      }}
      accept=".pdf,.doc,.docx,.txt"
      multiple={multiple}
      placeholder={multiple ? 'Select documents...' : 'Select a document...'}
    />
  );
}
