import * as React from 'react';
import { useState, useEffect } from 'react';

import { cn } from '@/lib/utils';
import { useDebouncedCallback } from '@/hooks/use-debounce';

export interface InputProps
  extends Omit<React.ComponentProps<'input'>, 'onChange'> {
  /**
   * Debounce delay in milliseconds. If provided, the onChange callback will be debounced.
   */
  debounceMs?: number;
  /**
   * Callback that will be debounced if debounceMs is provided.
   * This will be called instead of the regular onChange when debouncing is enabled.
   */
  onChange?: (event: React.ChangeEvent<HTMLInputElement>) => void;
}

function Input({
  className,
  type,
  value,
  debounceMs,
  onChange,
  ...props
}: InputProps) {
  const [internalValue, setInternalValue] = useState(value || '');

  // Sync internal value with external value prop
  useEffect(() => {
    setInternalValue(value || '');
  }, [value]);

  const debouncedOnChange = useDebouncedCallback((...args: unknown[]) => {
    const event = args[0] as React.ChangeEvent<HTMLInputElement>;
    onChange?.(event);
  }, debounceMs || 0);

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = event.target.value;

    // Update internal value immediately for responsive UI
    setInternalValue(newValue);

    // If debouncing is enabled, call the debounced onChange
    if (debounceMs && onChange) {
      debouncedOnChange(event);
    } else if (!debounceMs && onChange) {
      // If no debouncing, call onChange immediately
      onChange(event);
    }
  };

  return (
    <input
      type={type}
      data-slot="input"
      className={cn(
        'file:text-foreground text-sub-header placeholder:text-[var(--input-placeholder)] selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-10.5 w-full min-w-0 rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm',
        'focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]',
        'aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive',
        className
      )}
      value={internalValue}
      onChange={handleChange}
      {...props}
    />
  );
}

export { Input };
