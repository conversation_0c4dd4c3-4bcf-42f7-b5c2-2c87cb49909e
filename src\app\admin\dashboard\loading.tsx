'use client';

import { Card, CardContent } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { ArrowRightIcon } from '@/lib/icons';
import { Plus } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { UsersIcon, LocationIcon } from '@/lib/icons';

export default function LoadingSkeleton() {
  return (
    <>
      <div className="flex flex-1">
        <div className="w-full">
          <div className="grid gap-8 mb-8 grid-cols-2">
            <Card className="hover:shadow-lg transition-shadow py-0">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div className="space-y-2">
                    <p className="text-sm font-medium">Total Users</p>
                    <Skeleton className="h-4 w-24" />
                  </div>
                  <div className="w-10 h-10 bg-[#F5F5F5] flex items-center justify-center rounded-lg">
                    <UsersIcon color="#525252" className="w-5 h-4" />
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card className="hover:shadow-lg transition-shadow py-0">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div className="space-y-2">
                    <p className="text-sm font-medium">Active Locations</p>
                    <Skeleton className="h-4 w-24" />
                  </div>
                  <div className="w-10 h-10 bg-[#F5F5F5] flex items-center justify-center rounded-lg">
                    <LocationIcon color="#525252" className="w-3 h-4" />
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card className="hover:shadow-lg transition-shadow py-0">
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center">
                    <div className="w-10 h-10 bg-[#F5F5F5] flex items-center justify-center rounded-lg">
                      <UsersIcon color="#525252" className="w-5 h-4" />
                    </div>
                    <p className="text-sm font-medium ml-3">Users</p>
                  </div>
                  <Badge variant="secondary">
                    <Skeleton className="h-4 w-24" />
                  </Badge>
                </div>
                {Array.from({ length: 3 }).map((_, index) => (
                  <Card
                    key={index}
                    className="hover:shadow-lg transition-shadow py-0 mt-4 border"
                  >
                    <CardContent className="px-3 py-[15px] rounded-lg">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <Skeleton className="h-10 w-10 rounded-full mr-3" />
                          <div className="space-y-2">
                            <Skeleton className="h-4 w-24" />
                            <Skeleton className="h-4 w-16" />
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
                <div className="flex items-center justify-between mt-7">
                  <div className="flex items-center gap-2 cursor-pointer">
                    <span>View all users</span>
                    <ArrowRightIcon className="w-4 h-4" />
                  </div>
                  <Button variant="default" className="w-[127px] h-9">
                    <Plus /> Create new
                  </Button>
                </div>
              </CardContent>
            </Card>
            <Card className="hover:shadow-lg transition-shadow py-0">
              <CardContent className="p-6">
                <div className="flex items-center mb-6">
                  <div className="w-10 h-10 bg-[#F5F5F5] flex items-center justify-center rounded-lg">
                    <LocationIcon color="#525252" className="w-3 h-4" />
                  </div>
                  <p className="text-sm font-medium ml-3">Locations</p>
                </div>
                {Array.from({ length: 3 }).map((_, index) => (
                  <Card
                    key={index}
                    className="hover:shadow-lg transition-shadow py-0 mt-4"
                  >
                    <CardContent className="px-3 py-[15px] rounded-lg">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <Skeleton className="h-10 w-10 rounded-lg mr-3" />
                          <div className="space-y-2">
                            <Skeleton className="h-4 w-24" />
                            <Skeleton className="h-4 w-16" />
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
                <div className="flex items-center justify-between mt-7">
                  <div className="flex items-center gap-2 cursor-pointer">
                    <span>View all locations</span>
                    <ArrowRightIcon className="w-4 h-4" />
                  </div>
                  <Button variant="default" className="w-[127px] h-9">
                    <Plus /> Create new
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </>
  );
}
