'use client';

import * as React from 'react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Check, ChevronDown } from 'lucide-react';

export type SimpleOption = {
  value: string;
  label: string;
  disabled?: boolean;
};

export interface SimpleMultiSelectProps {
  options: SimpleOption[];
  value: string[];
  onChange: (next: string[]) => void;
  placeholder?: string;
  className?: string;
  groupLabel?: string;
}

export default function SimpleMultiSelect({
  options,
  value,
  onChange,
  placeholder = 'Select',
  className,
  groupLabel,
}: SimpleMultiSelectProps) {
  const [open, setOpen] = React.useState(false);
  const selectedSet = React.useMemo(() => new Set(value), [value]);

  const toggle = React.useCallback(
    (val: string) => {
      if (selectedSet.has(val)) {
        onChange(value.filter(v => v !== val));
      } else {
        onChange([...value, val]);
      }
    },
    [onChange, selectedSet, value]
  );

  const triggerText = React.useMemo(() => {
    if (value.length === 0) return placeholder;
    if (value.length === 1) {
      const opt = options.find(o => o.value === value[0]);
      return opt?.label ?? placeholder;
    }
    const selected = options
      .filter(opt => value.includes(opt.value))
      .map(p => p.label);
    return selected.join(', ');
  }, [options, placeholder, value]);

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          type="button"
          variant="outline"
          aria-expanded={open}
          className={cn('w-full justify-between h-10 border-input', className)}
        >
          <span
            className={cn(
              value.length === 0 && 'text-muted-foreground',
              'truncate max-w-3/4'
            )}
          >
            {triggerText}
          </span>
          <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-60" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="p-0 w-[320px]" align="start">
        <div className="py-2">
          {groupLabel && (
            <div className="px-3 pb-1 text-xs font-medium text-muted-foreground">
              {groupLabel}
            </div>
          )}
          <div className="max-h-64 overflow-auto">
            {options.map(opt => {
              const selected = selectedSet.has(opt.value);
              return (
                <button
                  key={opt.value}
                  type="button"
                  disabled={opt.disabled}
                  onClick={() => toggle(opt.value)}
                  className={cn(
                    'w-full flex items-center gap-2 px-3 py-2 text-sm hover:bg-accent disabled:opacity-50 disabled:pointer-events-none',
                    selected && 'bg-accent/60'
                  )}
                >
                  <Check
                    className={cn(
                      'h-4 w-4 opacity-0',
                      selected && 'opacity-100'
                    )}
                  />
                  <span className="truncate">{opt.label}</span>
                </button>
              );
            })}
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
}
