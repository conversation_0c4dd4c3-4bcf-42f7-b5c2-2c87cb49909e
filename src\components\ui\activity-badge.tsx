import * as React from 'react';
import { Badge } from './badge';
import { cn } from '@/lib/utils';

interface ActivityBadgeProps extends React.ComponentProps<typeof Badge> {
  activityType?:
    | 'login'
    | 'profile_update'
    | 'property_added'
    | 'booking_made'
    | 'report_submitted'
    | string;
}

const activityBadgeVariants: Record<string, string> = {
  login: 'bg-blue-100 text-blue-800 border-blue-200',
  profile_update: 'bg-green-100 text-green-800 border-green-200',
  property_added: 'bg-purple-100 text-purple-800 border-purple-200',
  booking_made: 'bg-orange-100 text-orange-800 border-orange-200',
  report_submitted: 'bg-indigo-100 text-indigo-800 border-indigo-200',
};

export function ActivityBadge({
  className,
  activityType = 'login',
  variant = 'outline',
  ...props
}: ActivityBadgeProps) {
  return (
    <Badge
      variant={variant}
      className={cn(
        'text-xs font-medium',
        activityBadgeVariants[activityType] || activityBadgeVariants.login,
        className
      )}
      {...props}
    />
  );
}
