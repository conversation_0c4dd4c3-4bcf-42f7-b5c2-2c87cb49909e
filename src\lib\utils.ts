import { clsx, type ClassValue } from 'clsx';
import { twMerge } from 'tailwind-merge';
import { format } from 'date-fns';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * Generates user initials from a full name
 * @param name - The full name of the user
 * @returns The initials (up to 2 characters)
 * @example
 * getUserInitials('<PERSON>') // Returns 'JD'
 * getUserInitials('Alice') // Returns 'A'
 * getUserInitials('<PERSON>') // Returns 'MJ'
 */
export function getUserInitials(name: string): string {
  if (!name || typeof name !== 'string') {
    return 'U';
  }

  return name
    .split(' ')
    .map(word => word[0])
    .join('')
    .toUpperCase()
    .slice(0, 2);
}

export function formatDateRange(from: Date, to: Date) {
  if (
    from.getMonth() !== to.getMonth() ||
    from.getFullYear() !== to.getFullYear()
  ) {
    return `${format(from, 'LLL d, yyyy')} – ${format(to, 'LLL d, yyyy')}`;
  }

  return `${format(from, 'LLL d')}–${format(to, 'd, yyyy')}`;
}

export function getDisplayText(value: string): string {
  if (!value) return '';

  return value
    .split('_')
    .map((word, index) =>
      index === 0
        ? word.charAt(0).toUpperCase() + word.slice(1)
        : word.toLowerCase()
    )
    .join(' ');
}

/**
 * Fetch a remote image URL and convert it into a File instance.
 */
export async function fileFromImageUrl(
  url: string,
  filename?: string
): Promise<File> {
  const res = await fetch(url, { cache: 'no-store' });
  if (!res.ok) throw new Error(`Failed to fetch image: ${res.status}`);
  const blob = await res.blob();
  const name = filename ?? url.split('/').pop() ?? 'image.jpg';
  const type = blob.type || 'image/jpeg';
  return new File([blob], name, { type });
}
