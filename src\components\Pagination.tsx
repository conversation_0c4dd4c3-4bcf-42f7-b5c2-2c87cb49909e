import { Button } from './ui/button';

interface PaginationProps {
  page: number;
  totalPage: number;
  setPage: (page: number) => void;
}
export default function Pagination({
  page,
  totalPage,
  setPage,
}: PaginationProps) {
  return (
    <div className="flex items-center space-x-1">
      {(() => {
        const generatePaginationItems = (
          currentPage: number,
          totalPages: number
        ) => {
          const pageNumbers = new Set<number>();
          const siblingCount = 1;
          if (totalPages > 0) {
            pageNumbers.add(1);
            if (totalPages > 1) {
              pageNumbers.add(totalPages);
            }
          }
          for (
            let i = currentPage - siblingCount;
            i <= currentPage + siblingCount;
            i++
          ) {
            if (i > 1 && i < totalPages) {
              pageNumbers.add(i);
            }
          }
          const sortedPageNumbers = Array.from(pageNumbers).sort(
            (a, b) => a - b
          );
          const finalPaginationItems: (number | 'ellipsis')[] = [];
          let lastPageAdded: number | null = null;
          for (const p of sortedPageNumbers) {
            if (lastPageAdded !== null && p > lastPageAdded + 1) {
              finalPaginationItems.push('ellipsis');
            }
            finalPaginationItems.push(p);
            lastPageAdded = p;
          }
          return finalPaginationItems;
        };

        const paginationItems = generatePaginationItems(page, totalPage);

        return paginationItems.map((item, index) =>
          item === 'ellipsis' ? (
            <span
              key={`ellipsis-${index}`}
              className="w-10 h-10 flex items-center justify-center text-gray-500"
            >
              ...
            </span>
          ) : (
            <Button
              key={item}
              variant={page === item ? 'default' : 'outline'}
              size="sm"
              onClick={() => setPage(item)}
              className="w-10 h-10 p-0"
            >
              {item}
            </Button>
          )
        );
      })()}
    </div>
  );
}
