'use client';
import { Location } from '@/types';
import { Button } from './ui/button';
import { CommentIcon, TrashIcon } from '@/lib/icons';
import { Badge } from './ui/badge';
import { Plus } from 'lucide-react';
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from './ui/carousel';
import Image from 'next/image';
import { Heading } from './ui/typography';

interface LocationCardProps {
  location: Location;
  onDelete?: (data: Location) => void;
  isReferenceListFullScreen?: boolean;
  onAddLocation?: (data: Location) => void;
  onViewDetail?: (data: Location) => void;
}

export default function LocationCard({
  location,
  onDelete,
  isReferenceListFullScreen = false,
  onAddLocation,
  onViewDetail,
}: LocationCardProps) {
  return (
    <div className="relative border rounded-lg shadow-sm bg-white flex flex-col group">
      <div className="relative bg-gray-200 flex items-center justify-center rounded-t-lg">
        <Carousel className="relative">
          <CarouselContent>
            {Array.from({ length: 5 }).map((_, index) => (
              <CarouselItem key={index}>
                <div className="relative w-full h-[200px]">
                  <Image
                    src={`https://picsum.photos/350/200?random=${index}`}
                    alt="Location"
                    width={350}
                    height={200}
                    className="w-full h-full object-cover rounded-t-lg"
                  />
                </div>
              </CarouselItem>
            ))}
          </CarouselContent>
          <CarouselPrevious className="absolute top-1/2 left-2 -translate-y-1/2 z-10 w-8.5 h-10 disabled:opacity-0 opacity-0 transition-opacity group-hover:opacity-100 group-hover:disabled:opacity-50 bg-white shadow-[0px_10px_15px_0px_rgba(0,0,0,0.1),0px_4px_6px_0px_rgba(0,0,0,0.1)]" />
          <CarouselNext className="absolute top-1/2 right-2 -translate-y-1/2 z-10 w-8.5 h-10 disabled:opacity-0 opacity-0 transition-opacity group-hover:opacity-100 group-hover:disabled:opacity-50 bg-white shadow-[0px_10px_15px_0px_rgba(0,0,0,0.1),0px_4px_6px_0px_rgba(0,0,0,0.1)]" />
        </Carousel>
      </div>
      <div className="px-4 pt-4 pb-3 flex-1 flex flex-col gap-1">
        <div className="flex justify-between items-center">
          <Heading level={6} className="mb-1">
            {location.name}
          </Heading>
          {isReferenceListFullScreen && (
            <TrashIcon
              onClick={() => onDelete?.(location)}
              className="opacity-0 group-hover:opacity-100 transition-opacity cursor-pointer"
            />
          )}
        </div>
        <p className="text-sm text-gray-600">{location.address}</p>

        <div className="mt-auto flex justify-start gap-1">
          {location.tags?.map((tag, ind) => (
            <Badge
              key={`tag-${ind}`}
              variant="secondary"
              className="capitalize"
            >
              {tag.name}
            </Badge>
          ))}
        </div>

        <div className="mt-4 flex items-center justify-between gap-2 opacity-0 group-hover:opacity-100 transition-opacity bg-white z-50">
          {isReferenceListFullScreen ? (
            <Button variant="default" className="h-[36px] flex-1">
              <CommentIcon />{' '}
              {location.comments ? 'View Comment' : 'Add Comment'}
            </Button>
          ) : (
            <Button
              onClick={() => onAddLocation?.(location)}
              variant="default"
              className="h-[36px] flex-1"
            >
              <Plus /> Quick add
            </Button>
          )}
          <Button
            onClick={() => onViewDetail?.(location)}
            variant="outline"
            className="m-w-[143px] flex-1 h-[36px]"
          >
            View details
          </Button>
        </div>
      </div>
      {isReferenceListFullScreen && (
        <span className="absolute left-0 bottom-6 ml-4 text-sm">
          {location.comments
            ? location.comments > 1
              ? `${location.comments} Comments`
              : `${location.comments} Comment`
            : ''}
        </span>
      )}
    </div>
  );
}
