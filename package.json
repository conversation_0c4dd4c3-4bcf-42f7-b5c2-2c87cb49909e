{"name": "scoutr-frontend", "version": "0.1.0", "private": true, "engines": {"node": ">=22.0.0"}, "scripts": {"dev": "next dev --turbopack", "build": "next build", "build:start": "next build && next start -p 3000 -H myapp.local", "start": "next start -p 3000 -H myapp.local", "lint": "next lint", "lint:fix": "next lint --fix", "format": "prettier --write .", "format:check": "prettier --check .", "prepare": "husky"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix --config eslint.config.mjs --cache --cache-location node_modules/.cache/eslint/ --max-warnings 0", "prettier --write --ignore-unknown"], "*.{json,css,md,yml,yaml}": ["prettier --write --ignore-unknown"]}, "dependencies": {"@auth/prisma-adapter": "^2.10.0", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "^5.2.1", "@radix-ui/react-alert-dialog": "^1.1.15", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-hover-card": "^1.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.15", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.8", "@radix-ui/react-scroll-area": "^1.2.10", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.6", "@radix-ui/react-tabs": "^1.1.13", "@radix-ui/react-tooltip": "^1.2.8", "@sentry/nextjs": "^10.5.0", "@tanstack/react-table": "^8.21.3", "@types/bcryptjs": "^2.4.6", "@vis.gl/react-google-maps": "^1.5.5", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "classnames": "^2.5.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "embla-carousel-react": "^8.6.0", "lodash": "^4.17.21", "lucide-react": "^0.537.0", "next": "15.4.6", "next-auth": "^4.24.11", "next-themes": "^0.4.6", "nextjs-toploader": "^3.8.16", "react": "19.1.0", "react-day-picker": "^9.8.1", "react-dom": "19.1.0", "react-hook-form": "^7.62.0", "react-infinite-scroll-component": "^6.1.0", "recharts": "^2.15.4", "sonner": "^2.0.7", "tailwind-merge": "^3.3.1", "zod": "^4.0.15"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/lodash": "^4.17.20", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "critters": "^0.0.23", "eslint": "^9", "eslint-config-next": "15.4.6", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.5.4", "husky": "^9.1.7", "lint-staged": "^16.1.5", "prettier": "^3.6.2", "tailwindcss": "^4", "tw-animate-css": "^1.3.6", "typescript": "^5"}}