'use client';
import { useCallback, useEffect, useMemo, useState } from 'react';
import {
  LocationFilters,
  useSearchLocationFilters,
  defaultFilters,
} from './search-location-context';
import isEqual from 'lodash/isEqual';
import LocationsGridView from './locations-grid-view';
import { Location } from '@/types';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import LocationsMapView from './location-map-view';
import { useNavbar } from '@/contexts/navbar-context';
import { useSearchLocationPanel } from './search-location-context';
import { ChevronDown } from 'lucide-react';
import { useParams, useRouter } from 'next/navigation';
import { LocationMarkerIcon } from '@/lib/icons';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent } from '@/components/ui/tabs';
import { MOCK_DATA } from '@/lib/services/location-service';

export default function SearchLocationsPage() {
  const { filters } = useSearchLocationFilters();
  const { setAdditionalItems, clearNavbarItems } = useNavbar();
  const { togglePanel } = useSearchLocationPanel();
  const params = useParams();
  const router = useRouter();
  const [locations, setLocations] = useState<Location[]>([]);
  const [page, setPage] = useState<number>(1);
  const [totalPage, setTotalPage] = useState<number>(0);
  const [hasMore, setHasMore] = useState<boolean>(true);
  const [, setIsLoading] = useState<boolean>(true);
  const [view, setView] = useState<'grid' | 'map'>('grid');

  const fetchLocations = useCallback(
    (page: number, filters: LocationFilters) => {
      console.log(page, filters);
      setIsLoading(true);
      setTimeout(() => {
        setLocations(prev =>
          page === 1 ? MOCK_DATA : [...prev, ...MOCK_DATA]
        );
        const totalPage = 10;
        setTotalPage(totalPage);
        if (!MOCK_DATA.length || page >= totalPage) {
          setHasMore(false);
        }
        setIsLoading(false);
      }, 500);
    },
    []
  );

  const fetchMoreData = useCallback(() => {
    if (page >= totalPage) return;
    const nextPage = page + 1;
    setPage(nextPage);
  }, [page, totalPage]);

  const handleAddLocation = (data: Location) => {
    console.log(data);
    toast.success('Location added successfully.');
  };

  const hasFilters = useMemo(() => {
    return !isEqual(defaultFilters, filters);
  }, [filters]);

  useEffect(() => {
    fetchLocations(page, filters);
  }, [fetchLocations, page, filters]);

  // Set navbar items for this page
  useEffect(() => {
    // const referenceId = params.id as string;

    setAdditionalItems([
      {
        id: 'all-lists',
        label: 'All Lists',
        icon: <ChevronDown className="h-4 w-4" />,
        onClick: () => {
          // Handle export results
          console.log('All lists clicked');
        },
        variant: 'ghost',
        align: 'left',
        iconPosition: 'right',
      },
      {
        id: 'reference-list',
        label: 'Reference List',
        icon: <LocationMarkerIcon className="h-6 w-6 text-black size-6" />,
        onClick: () => {
          togglePanel();
        },
        variant: 'outline',
        align: 'right',
        badge: <Badge className="bg-badge-secondary text-black">3</Badge>,
        className: 'text-foreground gap-4',
      },
    ]);

    // Cleanup navbar items when component unmounts
    return () => {
      clearNavbarItems();
    };
  }, [params.id, setAdditionalItems, clearNavbarItems, router, togglePanel]);

  return (
    <div className="space-y-2">
      {!hasFilters ? (
        <div className="w-full flex h-[calc(100vh-168px)] items-center justify-center">
          <div className="space-y-12 flex flex-col items-center">
            <div>
              <LocationMarkerIcon className="h-40 w-40 text-black size-40" />
            </div>
            <div className="w-[441px] text-center">
              <h1 className="text-2xl font-bold mb-2">
                Start looking for locations
              </h1>
              <span className="text-base">
                Enter what you’re looking for, where, and when.
              </span>
            </div>
          </div>
        </div>
      ) : (
        <>
          <div className="mb-10 flex justify-between items-end">
            <div>
              <h1 className="text-2xl font-bold mb-2">Results</h1>
              {locations.length ? (
                <span className="text-base">
                  Showing 338 available locations.
                </span>
              ) : (
                <span className="text-base">
                  Showing 0 available locations.
                </span>
              )}
            </div>
            <div className="mt-6 flex items-center gap-2">
              <div className="inline-flex rounded-md p-1 bg-secondary gap-1">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setView('grid')}
                  className={`
                    w-10 h-6 hover:bg-white
                    ${
                      view === 'map'
                        ? 'border-0 shadow-none text-[#64748B] '
                        : 'bg-white'
                    }`}
                >
                  Grid
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setView('map')}
                  className={`
                    w-10 h-6 hover:bg-white
                    ${
                      view === 'grid'
                        ? 'border-0 shadow-none text-[#64748B] '
                        : 'bg-white'
                    }`}
                >
                  Map
                </Button>
              </div>
            </div>
          </div>
          {locations.length === 0 && (
            <div className="w-full flex h-[calc(100vh-280px)] items-center justify-center">
              <div className="space-y-12 flex flex-col items-center">
                <div>
                  <LocationMarkerIcon className="h-40 w-40 text-black size-40" />
                </div>
                <div className="w-[441px] text-center">
                  <h1 className="text-2xl font-bold mb-2">
                    Oops! No locations found
                  </h1>
                  <span className="text-base">
                    Try a different name or adjust your filters to find what
                    you’re looking for.
                  </span>
                </div>
              </div>
            </div>
          )}
          <Tabs defaultValue={'grid'} value={view}>
            <TabsContent value={'grid'}>
              <LocationsGridView
                locations={locations}
                fetchMoreData={fetchMoreData}
                hasMore={hasMore}
                onAddLocation={handleAddLocation}
                referenceId={params.id as string}
              />
            </TabsContent>
            <TabsContent value={'map'}>
              <LocationsMapView locations={locations} />
            </TabsContent>
          </Tabs>
          {/* {locations.length && isGridView && (
            <LocationsGridView
              locations={locations}
              fetchMoreData={fetchMoreData}
              hasMore={hasMore}
              onAddLocation={handleAddLocation}
            />
          )}
          {locations.length && !isGridView && <LocationsMapView locations={locations} />} */}
        </>
      )}
    </div>
  );
}
