import { Role } from './enum';

// Base User interface - used throughout the application
export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  name?: string;
  avatar: string;
  role: Role;
  // Optional fields for different contexts
  status?: 'active' | 'disabled' | 'pending';
  invitedBy?: string;
  createdAt?: string;
  updatedAt?: string;
}

// Extended User interface for authentication context
export interface AuthUser extends User {
  password: string;
}

// Utility type for user display names
export type UserDisplayName = Pick<User, 'firstName' | 'lastName'>;

// Utility type for user profile updates
export type UserProfileUpdate = Partial<
  Pick<User, 'firstName' | 'lastName' | 'email' | 'avatar'>
>;
