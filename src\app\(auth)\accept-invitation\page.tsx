'use client';

import { useSearchParams, useRouter } from 'next/navigation';
import { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Loader2 } from 'lucide-react';
import Image from 'next/image';
import { Heading } from '@/components/ui/typography';
import { Body } from '@/components/ui/typography';
import { InfoIcon } from 'lucide-react';
import { authService } from '@/lib/services/auth-service';
import { handleError } from '@/lib/error-handler';

export default function InvitationPage() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [error, setError] = useState<string | null>(null);
  const invitationToken = searchParams.get('token');

  useEffect(() => {
    if (!invitationToken) {
      setError(
        'Invalid invitation link. Please check your email for the correct link.'
      );
      return;
    }

    // Fetch user info from invitation token using authService
    const fetchUserInfo = async () => {
      setError(null);

      try {
        await authService.getInvitationUserInfo(invitationToken);

        // Redirect to setup-password page on success
        router.push(`/setup-password?token=${invitationToken}`);
      } catch (err) {
        const { message } = handleError(
          err,
          'An error occurred while fetching user information'
        );
        setError(message);
      }
    };

    fetchUserInfo();
  }, [invitationToken, router]);

  if (error) {
    return (
      <div className="flex flex-col items-center w-full h-full">
        <div className="mx-auto px-4 py-16 flex-1 flex flex-col items-center justify-center">
          <div className="mx-auto flex justify-between max-w-[936px] gap-32">
            <div className="flex flex-col items-center justify-center max-w-[456px] px-9 py-10">
              <Heading level={4} className="text-header font-medium mb-2">
                Oh no! Link not working
              </Heading>
              <Body className="text-center">
                Looks like this invitation has expired or isn&apos;t valid
                anymore. Don&apos;t worry, you can easily request a new one.
              </Body>
              <Card className="shadow-none border-none bg-card-background mt-6 text-foreground">
                <CardContent>
                  <div className="flex gap-2">
                    <InfoIcon className="w-4 h-4" />
                    <div className="flex flex-col">
                      <p className="text-sm text-header">What to do next:</p>
                      <p className="text-sm">
                        Ask the administrator to send you a fresh invitation
                        link.
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
              <Button className="mt-12 w-full" onClick={() => window.close()}>
                Close tab
              </Button>
            </div>
            <div className="flex justify-center">
              <Image
                src="/assets/circle-placeholder.svg"
                alt="Invitation Steps"
                width={360}
                height={360}
              />
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col items-center w-full h-full">
      <div className="container mx-auto px-4 py-48 flex-1 flex flex-col items-center justify-center">
        <Loader2 className="w-10 h-10 animate-spin" />
      </div>
    </div>
  );
}
