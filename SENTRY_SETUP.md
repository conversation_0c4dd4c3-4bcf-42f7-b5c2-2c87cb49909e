# Sentry Integration Guide

This document provides a comprehensive guide for the Sentry integration in the Scoutr Web App.

## Overview

Sentry is a powerful error monitoring and performance tracking tool that helps you identify, triage, and resolve errors in real-time. This integration provides:

- **Error Monitoring**: Automatic capture of JavaScript errors
- **Performance Monitoring**: Track application performance and user experience
- **Session Replay**: Video-like reproduction of user sessions when errors occur
- **Custom Error Reporting**: Manual error and message reporting
- **User Context**: Associate errors with specific users
- **Environment Tracking**: Separate errors by environment (development, staging, production)

## Configuration Files

### 1. Sentry Configuration Files

- `sentry.server.config.ts` - Server-side Sentry configuration
- `sentry.client.config.ts` - Client-side Sentry configuration
- `sentry.edge.config.ts` - Edge runtime Sentry configuration

### 2. Environment Variables

Create a `.env.local` file with the following variables:

```bash
# Sentry Configuration
NEXT_PUBLIC_SENTRY_DSN=https://your-dsn-here
NEXT_PUBLIC_SENTRY_ENVIRONMENT=development

# For production builds (CI/CD)
SENTRY_AUTH_TOKEN=your-auth-token-here
```

### 3. Next.js Configuration

The `next.config.ts` file has been updated with Sentry configuration for:

- Source map uploading
- Performance monitoring
- Error tracking
- Session replay

## Components

### ErrorBoundary Component

Located at `src/components/ErrorBoundary.tsx`, this component:

- Catches React component errors
- Reports errors to Sentry automatically
- Provides a user-friendly error UI
- Allows error recovery

### Sentry Utilities

Located at `src/lib/sentry.ts`, provides utility functions for:

- Manual error reporting
- Performance monitoring
- User context setting
- Breadcrumb tracking
- API call monitoring

## Usage Examples

### 1. Basic Error Reporting

```typescript
import { captureError, captureMessage } from '@/lib/sentry';

// Report an error
try {
  // Some risky operation
} catch (error) {
  captureError(error, { context: 'user-action' });
}

// Report a message
captureMessage('User completed onboarding', 'info');
```

### 2. Performance Monitoring

```typescript
import { monitorApiCall } from '@/lib/sentry';

// Monitor API calls
const userData = await monitorApiCall(
  () => fetch('/nextapi/user'),
  '/nextapi/user',
  'GET'
);

// For custom performance monitoring, use Sentry's built-in span API
import * as Sentry from '@sentry/nextjs';

const span = Sentry.startSpan({
  name: 'User Login',
  op: 'auth',
});

try {
  await loginUser(credentials);
  span.setStatus('ok');
} catch (error) {
  span.setStatus('internal_error');
  throw error;
} finally {
  span.finish();
}
```

### 3. User Context

```typescript
import { setUser, clearUser } from '@/lib/sentry';

// Set user context when user logs in
setUser({
  id: 'user-123',
  email: '<EMAIL>',
  username: 'john_doe',
});

// Clear user context when user logs out
clearUser();
```

### 4. Custom Error Boundary

```typescript
import ErrorBoundary from '@/components/ErrorBoundary';

function CustomErrorFallback({ error, resetError }) {
  return (
    <div>
      <h2>Something went wrong!</h2>
      <button onClick={resetError}>Try again</button>
    </div>
  );
}

<ErrorBoundary fallback={CustomErrorFallback}>
  <YourComponent />
</ErrorBoundary>
```

## Testing

### 1. Test Error Reporting

Visit `/sentry-example-page` to test error reporting:

- Click "Throw Sample Error" to generate a test error
- Check your Sentry dashboard to see the error

### 2. Test Performance Monitoring

```typescript
// Add this to any component to test performance monitoring
import * as Sentry from '@sentry/nextjs';

useEffect(() => {
  const span = Sentry.startSpan({
    name: 'Component Load',
    op: 'ui',
  });

  // Simulate some work
  setTimeout(() => {
    span.finish();
  }, 1000);
}, []);
```

## Production Deployment

### 1. Environment Variables

Set the following environment variables in your production environment:

```bash
NEXT_PUBLIC_SENTRY_DSN=https://your-production-dsn
NEXT_PUBLIC_SENTRY_ENVIRONMENT=production
SENTRY_AUTH_TOKEN=your-auth-token
```

### 2. Source Maps

Source maps are automatically uploaded during the build process when `SENTRY_AUTH_TOKEN` is set.

### 3. Performance Optimization

In production, Sentry automatically:

- Reduces sampling rates for performance data
- Filters out development errors
- Optimizes bundle size

## Best Practices

### 1. Error Handling

- Always wrap async operations in try-catch blocks
- Use the ErrorBoundary component for React components
- Provide meaningful error messages to users

### 2. Performance Monitoring

- Monitor critical user journeys
- Track API response times
- Monitor page load performance

### 3. User Context

- Set user context when users log in
- Clear user context when users log out
- Include relevant user information for debugging

### 4. Environment Management

- Use different DSNs for different environments
- Set appropriate sampling rates for each environment
- Filter out development errors in production

## Troubleshooting

### Common Issues

1. **Errors not appearing in Sentry**
   - Check your DSN configuration
   - Verify network connectivity
   - Check browser console for Sentry errors

2. **Source maps not uploading**
   - Verify `SENTRY_AUTH_TOKEN` is set
   - Check build logs for upload errors
   - Ensure `.env.sentry-build-plugin` is not in `.gitignore`

3. **Performance data not showing**
   - Check sampling rates
   - Verify transaction names and operations
   - Check Sentry dashboard filters

### Debug Mode

Enable debug mode in development:

```typescript
// In sentry.client.config.ts
debug: process.env.NODE_ENV === 'development';
```

## Resources

- [Sentry Next.js Documentation](https://docs.sentry.io/platforms/javascript/guides/nextjs/)
- [Sentry JavaScript SDK](https://docs.sentry.io/platforms/javascript/)
- [Performance Monitoring](https://docs.sentry.io/product/performance/)
- [Session Replay](https://docs.sentry.io/product/session-replay/)

## Support

For issues with Sentry integration:

1. Check the Sentry documentation
2. Review the configuration files
3. Check the browser console for errors
4. Contact the development team
