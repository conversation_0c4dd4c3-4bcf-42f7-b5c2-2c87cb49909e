'use client';

import { X } from 'lucide-react';
import Image from 'next/image';

export default function FullscreenModal({
  src,
  onClose,
}: {
  src: string;
  onClose: () => void;
}) {
  return (
    <div className="fixed inset-0 z-50 bg-black/90 flex items-center justify-center">
      <button
        onClick={onClose}
        className="absolute top-4 right-4 text-white hover:text-gray-300 cursor-pointer z-50"
      >
        <X size={32} />
      </button>
      <div className="relative w-full h-auto xl:h-full xl:w-auto aspect-[900/600]">
        <Image src={src} alt="Fullscreen" fill className="object-cover" />
      </div>
    </div>
  );
}
