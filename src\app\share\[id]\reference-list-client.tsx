'use client';

import { ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import PreviewLocation from '@/components/PreviewLocation';
import { ShareIcon } from '@/lib/icons';
import { Reference, Location, Role } from '@/types';
import { formatDateRange } from '@/lib/utils';
import DetailScreen from '@/components/DetailScreen';
import { useMemo, useState } from 'react';
import { useAuth } from '@/contexts/auth-context';
import ConfirmDialog from '@/components/ConfirmDialog';
import { toast } from 'sonner';
import { AddCommentModal } from '@/components/AddCommentModal';

interface ReferenceListClientProps {
  data: Reference;
}

export default function ReferenceListClient({
  data,
}: ReferenceListClientProps) {
  const { user } = useAuth();
  const [isDetailScreenOpen, setIsDetailScreenOpen] = useState(false);
  const [selectedLocation, setSelectedLocation] = useState<Location>();
  const [isSubmitted, setIsSubmitted] = useState<boolean>(false);
  const [submitFeedbackDialogOpen, setSubmitFeedbackDialogOpen] =
    useState<boolean>(false);
  const [addCommentDialogOpen, setAddCommentDialogOpen] =
    useState<boolean>(false);
  const [locationSelected, setLocationSelected] = useState<Location>();
  const [requestAccessDialogOpen, setRequestAccessDialogOpen] =
    useState<boolean>(false);
  const submitFeedback = () => {
    try {
      console.log('submitFeedback');
      setIsSubmitted(true);
      toast.success('Feedback submitted successfully.');
    } catch {
      toast.error('Feedback submitted failed');
    }
  };
  const handleComment = (data: Location) => {
    console.log('handleComment', data);
    setLocationSelected(data);
    setAddCommentDialogOpen(true);
  };
  const viewer = useMemo(() => user?.role === Role.VIEWER, [user?.role]);
  return (
    <>
      <div className="w-full p-4 flex justify-center">
        <div className="w-full sm:w-[unset] sm:max-w-6xl">
          {/* Header */}
          <div className="flex flex-col sm:flex-row items-start sm:items-center justify-start sm:justify-between my-4 sm:py-11">
            <div className="flex items-center gap-4 mb-4 sm:mb-0">
              <div className="cursor-pointer">
                <ArrowLeft className="h-5 w-5" />
              </div>
              <div>
                <h1 className="text-lg font-semibold">
                  {data.projectName} preview
                </h1>
                <p className="text-sm text-gray-600">
                  {data.productionHouse} •{' '}
                  {formatDateRange(
                    new Date(data.shootDates.from),
                    new Date(data.shootDates.to)
                  )}{' '}
                  • {data.locations?.length ?? 0}{' '}
                  {data.locations?.length && data.locations.length > 1
                    ? 'references'
                    : 'reference'}
                </p>
              </div>
            </div>
            {viewer ? (
              <Button
                variant="default"
                className="w-full sm:w-[201px] h-[41px]"
                disabled={isSubmitted}
                onClick={() => setSubmitFeedbackDialogOpen(true)}
              >
                Submit Feedback
              </Button>
            ) : (
              <Button
                variant="default"
                className="w-full sm:w-[201px] h-[41px] gap-2"
              >
                <ShareIcon className="h-4 w-4" />
                Share List
              </Button>
            )}
          </div>

          {data.locations &&
            data.locations.map(location => (
              <PreviewLocation
                key={location.id}
                data={location}
                onSelect={location => {
                  setSelectedLocation(location);
                  setIsDetailScreenOpen(true);
                }}
                onComment={handleComment}
                viewer={viewer}
                isSubmitted={isSubmitted}
              />
            ))}
        </div>
      </div>
      <DetailScreen
        open={isDetailScreenOpen}
        images={selectedLocation?.images.map(image => image.url) ?? []}
        onOpenChange={setIsDetailScreenOpen}
      />
      <ConfirmDialog
        open={submitFeedbackDialogOpen}
        onOpenChange={setSubmitFeedbackDialogOpen}
        title="Submit feedback?"
        description="Once you submit, your feedback will be sent to the scout."
        confirmText="Submit"
        cancelText="Keep editing"
        onConfirm={submitFeedback}
        onCancel={() => setSubmitFeedbackDialogOpen(false)}
      />
      {locationSelected && (
        <AddCommentModal
          location={locationSelected}
          open={addCommentDialogOpen}
          onOpenChange={setAddCommentDialogOpen}
        />
      )}
      <ConfirmDialog
        open={requestAccessDialogOpen}
        onOpenChange={setRequestAccessDialogOpen}
        title="Don’t have an account?"
        description="You need access to view this reference list. Once you send your request, an admin will review it and grant you access if approved."
        confirmText="Request Access"
        cancelText="Cancel"
        onConfirm={() => {
          toast.success('Request sent successfully.');
        }}
        onCancel={() => setRequestAccessDialogOpen(false)}
      />
    </>
  );
}
