/* eslint-disable @typescript-eslint/no-unused-vars */
// Examples of how to use the new HTTP client with automatic token injection

import {
  httpClient,
  get,
  post,
  put,
  deleteRequest,
  uploadFile,
} from './http-client';
import { makeServerRequest } from './api-client';
import {
  useAuthService,
  useUserService,
  useLocationService,
  useAssetService,
  useNotificationService,
} from '../hooks/use-services';
import { User } from '@/types/user';
import { Role } from '@/types/enum';

// ========================================
// Example 1: Using the HTTP client directly
// ========================================

export async function exampleDirectHttpClient() {
  try {
    // GET request - access token automatically added
    const users = await httpClient.get<User[]>('/users');
    console.log('Users:', users);

    // POST request - access token automatically added
    const newUser = await httpClient.post<User>('/users', {
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      role: 'Admin',
    });
    console.log('Created user:', newUser);

    // PUT request - access token automatically added
    const updatedUser = await httpClient.put<User>(`/users/${newUser.id}`, {
      firstName: 'Jane',
      lastName: 'Doe',
    });
    console.log('Updated user:', updatedUser);

    // DELETE request - access token automatically added
    await httpClient.delete<void>(`/users/${newUser.id}`);
    console.log('User deleted');
  } catch (error) {
    console.error('HTTP client error:', error);
  }
}

// ========================================
// Example 2: Using convenience functions
// ========================================

export async function exampleConvenienceFunctions() {
  try {
    // Using individual functions - access token automatically added
    const users = await get<User[]>('/users');
    const user = await get<User>('/users/123');

    const newUser = await post<User>('/users', {
      firstName: 'Alice',
      lastName: 'Smith',
      email: '<EMAIL>',
    });

    const updatedUser = await put<User>(`/users/${newUser.id}`, {
      firstName: 'Alice Updated',
    });

    await deleteRequest<void>(`/users/${newUser.id}`);
  } catch (error) {
    console.error('Convenience functions error:', error);
  }
}

// ========================================
// Example 3: Using individual service hooks (Recommended)
// ========================================

export function ExampleServiceHooks() {
  const authService = useAuthService();
  const userService = useUserService();
  const locationService = useLocationService();
  const assetService = useAssetService();
  const notificationService = useNotificationService();

  const handleUserOperations = async () => {
    try {
      // User operations
      const profile = await userService.getProfile();
      const allUsers = await userService.getUsers();
      const newUser = await userService.createUser({
        firstName: 'Bob',
        lastName: 'Johnson',
        email: '<EMAIL>',
        role: 'Scout' as Role,
      });

      console.log('User operations completed:', { profile, allUsers, newUser });
    } catch (error) {
      console.error('User operations failed:', error);
    }
  };

  const handleLocationOperations = async () => {
    try {
      // Location operations
      const locationList = await locationService.getLocations(1);
      // const newLocation = await locationService.createLocation({
      //   title: 'New Office',
      //   address: '123 Main St',
      //   description: 'A new office location',
      //   tagIds: [],
      //   images: [],
      // });

      console.log('Location operations completed:', {
        locations: locationList,
        // newLocation,
      });
    } catch (error) {
      console.error('Location operations failed:', error);
    }
  };

  const handleAssetOperations = async () => {
    try {
      // Asset operations
      const assetList = await assetService.getAssets();
      const categories = await assetService.getAssetCategories();
      const newAsset = await assetService.createAsset({
        name: 'Laptop',
        category: 'Electronics',
        locationId: 'location-123',
        status: 'available',
        condition: 'excellent',
      });

      console.log('Asset operations completed:', {
        assets: assetList,
        categories,
        newAsset,
      });
    } catch (error) {
      console.error('Asset operations failed:', error);
    }
  };

  const handleNotificationOperations = async () => {
    try {
      // Notification operations
      const notificationList = await notificationService.getNotifications();
      const unreadCount = await notificationService.getUnreadCount();

      console.log('Notification operations completed:', {
        notifications: notificationList,
        unreadCount,
      });
    } catch (error) {
      console.error('Notification operations failed:', error);
    }
  };

  return {
    handleUserOperations,
    handleLocationOperations,
    handleAssetOperations,
    handleNotificationOperations,
  };
}

// ========================================
// Example 4: Authentication and mixed operations
// ========================================

export function ExampleAuthAndMixedOperations() {
  const authService = useAuthService();
  const userService = useUserService();
  const locationService = useLocationService();
  const assetService = useAssetService();
  const notificationService = useNotificationService();

  const handleAuthOperations = async () => {
    try {
      // Authentication operations
      const loginResponse = await authService.login({
        email: '<EMAIL>',
        password: 'password123',
      });

      const userProfile = await authService.getUserProfile(
        loginResponse.accessToken
      );

      console.log('Auth operations completed:', { loginResponse, userProfile });
    } catch (error) {
      console.error('Auth operations failed:', error);
    }
  };

  const handleMixedOperations = async () => {
    try {
      // Mix operations from different services
      const [profile, locations, assets, notifications] = await Promise.all([
        userService.getProfile(),
        locationService.getLocations(1),
        assetService.getAssets(),
        notificationService.getNotifications(),
      ]);

      console.log('Mixed operations completed:', {
        profile,
        locations,
        assets,
        notifications,
      });
    } catch (error) {
      console.error('Mixed operations failed:', error);
    }
  };

  return {
    handleAuthOperations,
    handleMixedOperations,
  };
}

// ========================================
// Example 4: File upload with automatic token
// ========================================

export async function exampleFileUpload() {
  try {
    const fileInput = document.getElementById('file-input') as HTMLInputElement;
    const file = fileInput?.files?.[0];

    if (!file) {
      throw new Error('No file selected');
    }

    // File upload with automatic token injection
    const result = await uploadFile<{ url: string; filename: string }>(
      '/upload/avatar',
      file,
      {
        userId: '123',
        type: 'avatar',
      }
    );

    console.log('File uploaded:', result);
  } catch (error) {
    console.error('File upload error:', error);
  }
}

// ========================================
// Example 5: Custom headers with automatic token
// ========================================

export async function exampleCustomHeaders() {
  try {
    // Custom headers are merged with automatic token injection
    const result = await httpClient.get<{ data: string }>('/api/data', {
      'X-Custom-Header': 'custom-value',
      Accept: 'application/json',
    });

    console.log('Result with custom headers:', result);
  } catch (error) {
    console.error('Custom headers error:', error);
  }
}

// ========================================
// Example 6: Server-side API calls (Next.js API routes)
// ========================================

export async function exampleServerSideApiCall() {
  // This would be used in a Next.js API route like /api/users/route.ts
  try {
    // Get access token from session (in a real API route)
    const accessToken = 'your-access-token-here';

    // Make authenticated request to NestJS
    const users = await makeServerRequest<User[]>('/users', accessToken);
    const user = await makeServerRequest<User>('/users/123', accessToken);

    // POST request
    const newUser = await makeServerRequest<User>('/users', accessToken, {
      method: 'POST',
      body: JSON.stringify({
        firstName: 'Server',
        lastName: 'User',
        email: '<EMAIL>',
      }),
    });

    console.log('Server-side API calls completed:', { users, user, newUser });
  } catch (error) {
    console.error('Server-side API error:', error);
  }
}

// ========================================
// Example 7: Error handling
// ========================================

export async function exampleErrorHandling() {
  try {
    const result = await httpClient.get('/api/protected-endpoint');
    console.log('Success:', result);
  } catch (error) {
    if (error instanceof Error) {
      if (error.message.includes('401')) {
        console.log('Unauthorized - token may be expired');
        // Handle token expiration
      } else if (error.message.includes('timeout')) {
        console.log('Request timeout');
        // Handle timeout
      } else {
        console.log('Other error:', error.message);
      }
    }
  }
}

// ========================================
// Benefits of the new domain service architecture:
// ========================================

/*
✅ DOMAIN-DRIVEN DESIGN
- Each service handles a specific domain (users, locations, assets, notifications)
- Clear separation of concerns and responsibilities
- Easy to find and maintain domain-specific functionality
- Scalable architecture that grows with your application

✅ CLIENT-SIDE DATA FETCHING (Recommended)
- Real-time data updates as users navigate
- Better user experience with faster subsequent loads
- Reduced server load on your Next.js app
- Automatic token injection for all requests
- Smart caching capabilities with React Query/SWR

✅ SIMPLIFIED USAGE
- Direct service access: userService.getProfile() instead of api.users.getProfile()
- No unnecessary wrapper classes or delegation
- Domain-specific methods grouped logically
- Easy to discover available operations
- Consistent naming conventions across services

✅ AUTOMATIC TOKEN INJECTION
- No need to manually pass access tokens
- Tokens are automatically retrieved from NextAuth session
- Authorization header is automatically added

✅ TYPE SAFETY
- Full TypeScript support with domain-specific types
- Generic type parameters for responses
- Proper error typing and validation

✅ TIMEOUT HANDLING
- Automatic request timeouts
- Configurable timeout durations
- Proper cleanup on abort

✅ ERROR HANDLING
- Consistent error messages
- HTTP status code handling
- Timeout error detection

✅ FLEXIBILITY
- Custom headers support
- File upload capabilities
- Multiple HTTP methods (GET, POST, PUT, PATCH, DELETE)
- Easy to add new domain services

✅ MAINTENANCE
- Single source of truth for each domain
- Easy to update domain-specific logic
- Centralized token management
- Modular architecture for better testing
- Removed redundant ApiClient wrapper
*/
