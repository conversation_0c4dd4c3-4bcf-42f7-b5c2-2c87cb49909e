'use client';

import { useEffect, useRef, useState } from 'react';
import { useMapsLibrary } from '@vis.gl/react-google-maps';
import { Input } from './ui/input';

interface LocationAutocompleteProps {
  onSelect: (place: google.maps.places.PlaceResult | null) => void;
  placeholder?: string;
  value?: string;
  onValueChange?: (next: string) => void;
}

function AutocompleteInput({
  onSelect,
  placeholder,
  value: valueProp,
}: LocationAutocompleteProps) {
  const inputRef = useRef<HTMLInputElement>(null);
  const placesLib = useMapsLibrary('places');
  const autocompleteRef = useRef<google.maps.places.Autocomplete | null>(null);
  const placesServiceRef = useRef<google.maps.places.PlacesService | null>(
    null
  );
  const autocompleteServiceRef =
    useRef<google.maps.places.AutocompleteService | null>(null);
  const [value, setValue] = useState<string>(valueProp ?? '');

  const handleSetValue = (text: string) => {
    setValue(text);
    if (!text) {
      onSelect(null);
    }
  };

  useEffect(() => {
    if (!placesLib || !inputRef.current) return;
    inputRef.current.setAttribute('autocomplete', 'off');

    const autocomplete = new placesLib.Autocomplete(inputRef.current, {
      types: ['geocode'],
      fields: ['formatted_address', 'geometry', 'name'],
    });
    autocompleteRef.current = autocomplete;
    placesServiceRef.current = new placesLib.PlacesService(
      document.createElement('div')
    );
    autocompleteServiceRef.current = new placesLib.AutocompleteService();

    const handler = () => {
      const place = autocomplete.getPlace();
      if (place && place.formatted_address) {
        setValue(place.formatted_address);
        onSelect(place);
      }
    };

    autocomplete.addListener('place_changed', handler);

    return () => google.maps.event.clearInstanceListeners(autocomplete);
  }, [placesLib, onSelect, setValue]);

  useEffect(() => {
    const observer = new MutationObserver(() => {
      const pacVisible = document.querySelectorAll(
        '.pac-container:has(.pac-item)'
      );
      const gm = document.querySelector<HTMLElement>('.gm-style');
      if (gm) {
        const hasVisiblePac = Array.from(pacVisible).some(
          element => element.clientHeight > 0
        );

        if (hasVisiblePac) {
          gm.classList.add('disable-map');
        } else {
          gm.classList.remove('disable-map');
        }
      }
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true,
    });

    return () => {
      observer.disconnect();
    };
  }, []);

  useEffect(() => {
    setValue(valueProp ?? '');
  }, [valueProp]);

  return (
    <Input
      ref={inputRef}
      onChange={e => handleSetValue(e.target.value)}
      value={value}
      placeholder={placeholder || 'Enter the full address'}
      className="w-full pr-10"
    />
  );
}

export default function LocationAutocomplete({
  onSelect,
  value,
  onValueChange,
  placeholder,
}: {
  onSelect: (place: google.maps.places.PlaceResult | null) => void;
  value?: string;
  onValueChange?: (next: string) => void;
  placeholder?: string;
}) {
  return (
    <AutocompleteInput
      onSelect={onSelect}
      value={value}
      onValueChange={onValueChange}
      placeholder={placeholder}
    />
  );
}
