import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Plus } from 'lucide-react';
import Image from 'next/image';
import SimpleMultiSelect from './SimpleMultiSelect';
import AddTagModal from './AddTagModal';
import { useState } from 'react';
import { tagTypeOptions } from '@/app/admin/tags/tags-container';
import { Tag } from '@/types/tag';
import { TAG_SIZE_OPTIONS } from '@/types/constant';
interface TagsData {
  categories: string[];
  subCategory: string;
  style: string[];
  size: string;
}

interface TagsProps {
  data: TagsData;
  onChange: (data: Partial<TagsData>) => void;
  styleOption: { value: string; label: string }[];
  categoryOption: { value: string; label: string }[];
  subCategoryOption: { value: string; label: string }[];
  onCreateTag: (tag: Partial<Tag>) => void;
}

export default function TagsForm({
  data,
  onChange,
  styleOption,
  categoryOption,
  subCategoryOption,
  onCreateTag,
}: TagsProps) {
  const [isAddTagModalOpen, setIsAddTagModalOpen] = useState<boolean>(false);
  const handleCategoryToggle = (category: string) => {
    const isSelected = data.categories.includes(category);
    const newCategories = isSelected
      ? data.categories.filter(c => c !== category)
      : [...data.categories, category];

    onChange({ categories: newCategories });
    if (isSelected && !newCategories.length) {
      onChange({ subCategory: '' });
    }
  };

  const handleSizeToggle = (size: string) => {
    const checked = data.size !== size;

    onChange({ size: checked ? size : '' });
  };

  const handleSubCategoryChange = (value: string) => {
    onChange({ subCategory: value });
  };

  const handleStyleChange = (value: string[]) => {
    onChange({ style: value });
  };

  return (
    <>
      <div className="space-y-6">
        <div className="flex justify-start">
          <Button
            variant="outline"
            onClick={() => setIsAddTagModalOpen(true)}
            className="px-4 py-2"
          >
            <Plus /> Create a new tag
          </Button>
        </div>

        <div className="space-y-3">
          <Label className="text-sm font-medium text-gray-900">
            Choose the category
          </Label>
          <div className="flex gap-2 w-full overflow-y-auto scroll-hidden">
            {categoryOption.map(category => {
              const isSelected = data.categories.includes(category.value);
              return (
                <Badge
                  key={category.value}
                  variant="outline"
                  className={`cursor-pointer px-3 py-1 h-14 rounded-md gap-2 flex items-center justify-center w-1/3 sm:w-1/5 bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 ${
                    isSelected ? 'border-black' : ''
                  }`}
                  onClick={() => handleCategoryToggle(category.value)}
                >
                  <Image
                    src="/assets/circle-placeholder.svg"
                    alt="Circle Placeholder"
                    width={16}
                    height={16}
                  />
                  {category.label}
                </Badge>
              );
            })}
          </div>
        </div>

        {data.categories.length > 0 && (
          <div className="space-y-2">
            <Label className="text-sm font-medium text-gray-900">
              Select sub-category
            </Label>
            <Select
              value={data.subCategory}
              onValueChange={handleSubCategoryChange}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select sub-category" />
              </SelectTrigger>
              <SelectContent>
                {subCategoryOption.map(option => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        )}

        <div className="space-y-2">
          <Label className="text-sm font-medium text-gray-900">
            Choose the style or atmosphere
          </Label>
          <SimpleMultiSelect
            options={styleOption}
            value={data.style}
            onChange={handleStyleChange}
            placeholder="Select style"
          />
        </div>
        <div className="space-y-3">
          <Label className="text-sm font-medium text-gray-900">Size</Label>
          <div className="grid grid-cols-2 sm:grid-cols-5 gap-2">
            {TAG_SIZE_OPTIONS.map(size => {
              const isSelected = data.size === size;
              return (
                <Badge
                  key={size}
                  variant="outline"
                  className={`cursor-pointer px-3 py-1 h-14 rounded-md gap-2 flex items-center justify-center w-full bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 ${
                    isSelected ? 'border-black' : ''
                  }`}
                  onClick={() => handleSizeToggle(size)}
                >
                  <Image
                    src="/assets/circle-placeholder.svg"
                    alt="Circle Placeholder"
                    width={16}
                    height={16}
                  />
                  <span className="capitalize">{size}</span>
                </Badge>
              );
            })}
          </div>
        </div>
      </div>
      <AddTagModal
        open={isAddTagModalOpen}
        onOpenChange={setIsAddTagModalOpen}
        onSave={onCreateTag}
        typeOptions={tagTypeOptions}
      />
    </>
  );
}
