import { Badge } from './ui/badge';
import Image from 'next/image';
import { Location } from '@/types/location';
import { Button } from './ui/button';
import { DislikeIcon, LikeIcon } from '@/lib/icons';

interface PreviewLocationProps {
  data: Location;
  onSelect: (data: Location) => void;
  viewer?: boolean;
  isSubmitted?: boolean;
  onComment: (data: Location) => void;
}

export default function PreviewLocation({
  data,
  onSelect,
  viewer = false,
  isSubmitted = false,
  onComment,
}: PreviewLocationProps) {
  return (
    <>
      <div className="grid gap-4 sm:gap-30 grid-cols-2 mb-6 sm:mb-0 sm:space-y-6">
        <div className="grid gap-6 grid-cols-2 col-span-2 sm:col-span-1">
          {/* {data.images.length === 1 && (
            <div className="relative col-span-2">
              <div
                className="aspect-video bg-gray-200 rounded-lg overflow-hidden cursor-pointer"
                onClick={() => onSelect(data)}
              >
                <Image
                  src={data.images[0].url}
                  alt="Main reference image"
                  width={552}
                  height={337}
                  className="w-full h-full object-cover"
                />
              </div>
            </div>
          )}
          {data.images.length > 1 &&
            data.images.map((image, index) => (
              <div key={index} className="relative col-span-1">
                <div
                  onClick={() => onSelect(data)}
                  className="aspect-[264/337] bg-gray-200 rounded-lg overflow-hidden cursor-pointer"
                >
                  <Image
                    src={image.url}
                    alt="Main reference image"
                    width={264}
                    height={337}
                    className="w-full h-full object-cover"
                  />
                </div>
              </div>
            ))} */}
          <div className="relative col-span-2">
            <div
              className="aspect-video bg-gray-200 rounded-lg overflow-hidden cursor-pointer"
              onClick={() => onSelect(data)}
            >
              <Image
                src={data.coverImageUrl ?? ''}
                alt="Main reference image"
                width={552}
                height={337}
                className="w-full h-full object-cover"
              />
            </div>
          </div>
        </div>
        <div>
          {/* Location Header */}
          <div className="mb-3">
            <h3 className="text-lg font-semibold">{data.title}</h3>
            <p className="text-sm text-gray-600">{data.address}</p>
          </div>

          {/* Tags */}
          <div className="flex gap-2 mb-3 flex-wrap">
            {data.tags.map(tag => (
              <Badge
                key={tag.id}
                variant="secondary"
                className="bg-gray-100 text-gray-800"
              >
                {tag.name}
              </Badge>
            ))}
          </div>

          {/* Description */}
          <p className="text-sm text-gray-700 mb-3 leading-relaxed">
            {data.description}
          </p>

          {/* Comments */}
          {data.comments != null && data.comments > 0 && (
            <p
              onClick={() => onComment(data)}
              className="text-xs text-gray-500 cursor-pointer"
            >
              {data.comments} Comment
              {data.comments !== 1 ? 's' : ''}
            </p>
          )}
          {/* Approve or disapprove button */}
          {viewer && (
            <div className="flex items-center gap-4 mt-7">
              <Button
                disabled={isSubmitted}
                className="w-10 h-10 rounded-full !p-2"
              >
                <LikeIcon className="!w-5 !h-5" />
              </Button>
              <Button
                variant="outline"
                disabled={isSubmitted}
                className="w-10 h-10 rounded-full !p-2"
              >
                <DislikeIcon className="!w-6 !h-6" />
              </Button>
            </div>
          )}
        </div>
      </div>
    </>
  );
}
