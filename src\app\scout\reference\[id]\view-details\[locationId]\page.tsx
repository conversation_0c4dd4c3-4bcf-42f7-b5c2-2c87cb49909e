'use client';

import Image from 'next/image';
import { Location } from '@/types';
import { Plus } from 'lucide-react';
import { Heading } from '@/components/ui/typography';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { use, useCallback, useEffect, useState } from 'react';
import LocationOffers from '@/components/LocationOffers';
import {
  LightingIcon,
  NaturalLightIcon,
  ParkingIcon,
  RooftopAccessIcon,
  ShieldIcon,
  SnowflakeIcon,
  WheelchairIcon,
  WifiIcon,
} from '@/lib/icons';
import { toast } from 'sonner';
import Loading from './loading';
import { useRouter } from 'next/navigation';

const mockData: Location = {
  id: '1',
  name: 'Downtown Creative Loft',
  address: '123 Main St',
  city: 'Los Angeles',
  state: 'CA',
  zipCode: '90012',
  country: 'USA',
  latitude: 34.0522,
  longitude: -118.2437,
  tags: [
    { id: 'residential', name: 'Residential', type: 'category' },
    { id: 'modern', name: 'Modern', type: 'category' },
  ],
  description:
    'Perfect lighting setup here! The natural light from the east windows works great for morning shoots.',
  comments: 1,
  images: [
    {
      url: 'https://picsum.photos/350/200?random=1',
      order: 1,
      tags: [{ id: '', name: '', type: '' }],
    },
    {
      url: 'https://picsum.photos/350/200?random=5',
      order: 2,
      tags: [{ id: '', name: '', type: '' }],
    },
    {
      url: 'https://picsum.photos/350/200?random=6',
      order: 3,
      tags: [{ id: '', name: '', type: '' }],
    },
    {
      url: 'https://picsum.photos/350/200?random=7',
      order: 4,
      tags: [{ id: '', name: '', type: '' }],
    },
    {
      url: 'https://picsum.photos/350/200?random=8',
      order: 5,
      tags: [{ id: '', name: '', type: '' }],
    },
    {
      url: 'https://picsum.photos/350/200?random=9',
      order: 6,
      tags: [{ id: '', name: '', type: '' }],
    },
  ],
  coverImageUrl: 'https://picsum.photos/350/200?random=1',
  title: 'Downtown Creative Loft',
  createdAt: '',
  updatedAt: '',
  status: '',
};

const mockLocationOffers = [
  { icon: ParkingIcon, label: 'Free parking on premises' },
  { icon: NaturalLightIcon, label: 'Abundant natural light' },
  { icon: RooftopAccessIcon, label: 'Rooftop access' },
  { icon: WheelchairIcon, label: 'Wheelchair accessible' },
  { icon: WifiIcon, label: 'High-speed internet' },
  { icon: LightingIcon, label: 'Professional lighting equipment' },
  { icon: SnowflakeIcon, label: 'Climate controlled' },
  { icon: ShieldIcon, label: '24/7 security' },
];

export default function ViewLocationDetail({
  params,
}: {
  params: Promise<{ id: string; locationId: string }>;
}) {
  const router = useRouter();
  const { id, locationId } = use(params);
  const [location, setLocation] = useState<Location>();
  const [loading, setLoading] = useState<boolean>(true);

  const fetchLocation = useCallback(() => {
    try {
      setLoading(true);
    } catch {
      toast.error('Failed to get location');
    } finally {
      setLoading(false);
    }
    setLocation(mockData);
  }, []);

  useEffect(() => {
    if (!locationId) return;
    fetchLocation();
  }, [fetchLocation, locationId]);
  const onSeeAll = () => {
    router.push(`/scout/reference/${id}/view-details/${locationId}/all-photos`);
  };

  return (
    <>
      {loading && <Loading />}
      {!loading && (
        <div className="w-full flex justify-center">
          <div className="space-y-6 xl:w-[1128px] p-6 xl:px-0">
            <div className="grid gap-2 sm:gap-6 grid-cols-2 sm:grid-cols-4 grid-rows-2">
              {location?.images[0] && (
                <div className="relative row-span-2 col-span-2 rounded-xl overflow-hidden aspect-[552/337]">
                  <Image
                    src={location?.images[0].url}
                    alt="Most relevant photo"
                    fill
                    className="object-cover hover:scale-105 transition-transform duration-300"
                  />
                </div>
              )}

              {location?.images.slice(1, 5).map((photo, i) => (
                <div
                  key={i}
                  className="relative rounded-xl overflow-hidden sm:h-auto aspect-[263/157]"
                >
                  {i === 3 && location?.images.length > 5 ? (
                    <div
                      onClick={onSeeAll}
                      className="absolute inset-0 flex flex-col items-center justify-center bg-black/40 text-white text-sm sm:text-base font-medium hover:bg-black/50 transition cursor-pointer"
                    >
                      <span className="font-semibold flex items-center text-sm lg:gap-2">
                        <Plus /> See all photos
                      </span>
                    </div>
                  ) : (
                    <Image
                      src={photo.url}
                      alt={`Photo ${i + 2}`}
                      fill
                      className="object-cover hover:scale-105 transition-transform duration-300"
                    />
                  )}
                </div>
              ))}
            </div>
            <div className="flex justify-between items-start">
              <div className="pt-4 pb-3 flex-1 flex flex-col gap-1">
                <div className="flex justify-between items-center">
                  <Heading level={2} className="mb-1">
                    {location?.title}
                  </Heading>
                </div>
                <p className="text-sm text-gray-600">{location?.address}</p>

                <div className="mt-auto flex justify-start gap-1">
                  {location?.tags?.map((tag, ind) => (
                    <Badge
                      key={`tag-${ind}`}
                      variant="secondary"
                      className={`${tag.color ? `${tag.color} text-white` : ''}`}
                    >
                      {tag.name}
                    </Badge>
                  ))}
                </div>
              </div>
              <Button className="w-[155px] h-12">
                <Plus />
                Add to List
              </Button>
            </div>
            <p>{}</p>
            <div className="mt-8">
              <LocationOffers data={mockLocationOffers} />
            </div>
          </div>
        </div>
      )}
    </>
  );
}
