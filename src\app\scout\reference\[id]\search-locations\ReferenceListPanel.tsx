'use client';

import ConfirmDialog from '@/components/ConfirmDialog';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Body, Heading } from '@/components/ui/typography';
import { PencilIcon, ShareIcon, TrashIcon } from '@/lib/icons';
import { toastSuccess } from '@/lib/toast';
import { Reference } from '@/types';
import { MessageCircleIcon } from 'lucide-react';
import { ChevronsRight, FullscreenIcon } from 'lucide-react';
import { useState } from 'react';

interface ReferenceListPanelProps {
  reference?: Reference;
  isPanelOpen?: boolean;
}

export default function ReferenceListPanel({
  reference,
  isPanelOpen = false,
}: ReferenceListPanelProps) {
  const [isRemoveLocationDialogOpen, setIsRemoveLocationDialogOpen] =
    useState(false);
  const [selectedLocation, setSelectedLocation] =
    useState<Partial<Location> | null>(null);
  const locations = [
    {
      id: '1',
      name: 'Downtown Creative Loft',
      address: '123 Main St, Los Angeles, CA',
      comments: 1,
    },
    {
      id: '2',
      name: 'Rooftop Garden Terrace',
      address: '456 Sunset Blvd, Hollywood, CA',
    },
    {
      id: '3',
      name: 'Industrial Warehouse',
      address: '789 Arts District, LA, CA',
    },
  ];

  const handleRemoveLocation = () => {
    console.log('remove location:', selectedLocation);
    toastSuccess('Location removed successfully.');
    setIsRemoveLocationDialogOpen(false);
  };

  return (
    <div
      className={`border-l border-border p-6 pr-16 flex flex-col h-full transition-all duration-300 ${isPanelOpen ? 'opacity-100 bg-card-background' : 'opacity-0 overflow-hidden'}`}
    >
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Heading level={5}>{reference?.projectName}</Heading>
          <PencilIcon className="cursor-pointer" />
        </div>
        <ChevronsRight className="cursor-pointer" />
      </div>
      <div className="flex items-center gap-2 mt-2">
        <Body color="default">
          {reference?.productionHouse} • Jan 15–17, 2025 • 3 references
        </Body>
      </div>
      <div className="flex items-center gap-4 mt-6">
        <Button variant="outline" className="flex-1">
          <FullscreenIcon />
          Full Screen
        </Button>
        <Button className="flex-1">
          <ShareIcon />
          Share List
        </Button>
      </div>
      <div className="mt-5 flex flex-col gap-4">
        {locations.map(location => (
          <Card
            key={location.id}
            className="px-3 py-4 flex flex-row items-center gap-4 border-[#E5E5E5] shadow-none group"
          >
            <div className="flex-shrink-0">
              <div className="bg-[#A3A3A3] flex items-center justify-center rounded-lg h-12 w-12">
                <PencilIcon className="w-4 h-4 cursor-pointer opacity-0 group-hover:opacity-100 transition-opacity fill-[#1C1B1F]" />
              </div>
            </div>
            <div className="flex flex-col flex-1">
              <Heading level={6} className="text-sm">
                {location.name}
              </Heading>
              <Body className="text-xs text-muted-foreground">
                {location.address}
              </Body>
              {location.comments && location.comments > 0 ? (
                <>
                  <Body className="text-sm text-sub-header block group-hover:hidden cursor-pointer">
                    {location.comments > 1
                      ? `${location.comments} Comments`
                      : `${location.comments} Comment`}
                  </Body>
                  <Body className="text-sm text-sub-header hidden group-hover:flex items-center gap-1 cursor-pointer">
                    <MessageCircleIcon className="w-4 h-4 cursor-pointer fill-[#404040]" />
                    View Comment
                  </Body>
                </>
              ) : (
                <Body className="text-sm text-sub-header hidden group-hover:flex items-center gap-1 cursor-pointer">
                  <MessageCircleIcon className="w-4 h-4 cursor-pointer fill-[#404040]" />
                  Add Comment
                </Body>
              )}
            </div>
            <div className="flex items-center gap-2">
              <TrashIcon
                className="w-4 h-4 cursor-pointer opacity-0 group-hover:opacity-100 transition-opacity"
                onClick={() => {
                  setSelectedLocation(location as Partial<Location>);
                  setIsRemoveLocationDialogOpen(true);
                }}
              />
            </div>
          </Card>
        ))}
      </div>
      <ConfirmDialog
        open={isRemoveLocationDialogOpen}
        onOpenChange={setIsRemoveLocationDialogOpen}
        title="Remove reference from list?"
        description="This location will be removed from the reference list. You can add it again later if needed."
        confirmText="Remove"
        cancelText="Keep location"
        onConfirm={handleRemoveLocation}
        onCancel={() => setIsRemoveLocationDialogOpen(false)}
      />
    </div>
  );
}
