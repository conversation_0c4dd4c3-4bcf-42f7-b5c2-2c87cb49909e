export interface Step {
  label: string;
  value: string;
}
interface StepIndicatorProps {
  steps: Step[];
  currentStep: string;
  onStepClick: (step: string) => void;
}

export default function StepIndicator({
  steps,
  currentStep,
  onStepClick,
}: StepIndicatorProps) {
  return (
    <div className="flex items-center gap-4">
      {steps.map((step, index) => (
        <div key={index} className="flex items-center">
          {currentStep === step.value ? (
            <div className="flex items-center gap-1 sm:gap-2">
              <div className="w-8 h-8 rounded-full bg-black text-white flex items-center justify-center text-sm font-medium">
                {index + 1}
              </div>
              <span className="text-sm font-medium">{step.label}</span>
            </div>
          ) : (
            <div
              className="flex items-center gap-1 sm:gap-2 cursor-pointer"
              onClick={() => onStepClick(step.value)}
            >
              <div className="w-8 h-8 rounded-full bg-gray-200 text-gray-500 flex items-center justify-center text-sm font-medium">
                {index + 1}
              </div>
              <span className="text-sm font-medium text-gray-500">
                {step.label}
              </span>
            </div>
          )}
          {index < steps.length - 1 && (
            <div className="mx-2 sm:mx-4 h-px w-2 sm:w-12 bg-gray-300"></div>
          )}
        </div>
      ))}
    </div>
  );
}
