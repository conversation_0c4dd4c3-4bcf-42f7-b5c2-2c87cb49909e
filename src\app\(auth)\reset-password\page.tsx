'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useSearchParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { toast } from 'sonner';
import { Routes } from '@/lib/routes';
import Image from 'next/image';
import PasswordForm from '@/components/PasswordForm';
import { Heading, Body } from '@/components/ui/typography';
import { toastError, toastSuccess } from '@/lib/toast';
import { authService } from '@/lib/services/auth-service';
import { handleError } from '@/lib/error-handler';

export default function ResetPasswordPage() {
  const [loading, setLoading] = useState(false);
  const router = useRouter();
  const searchParams = useSearchParams();
  const token = searchParams.get('token');

  const onSubmit = async (password: string) => {
    if (!token) {
      toast.error(
        'Invalid or missing reset token. Please use the link from your email.'
      );
      return;
    }

    setLoading(true);
    try {
      await authService.resetPasswordConfirm({
        token,
        password,
      });

      // Show success toast
      toastSuccess('Password updated successfully.');
      router.push(Routes.SIGN_IN);
    } catch (err) {
      const { message, logError } = handleError(
        err,
        'An error occurred while resetting password'
      );

      logError();
      toastError(message);
    } finally {
      setLoading(false);
    }
  };

  if (!token) {
    return (
      <div className="flex items-center justify-center min-h-[calc(100vh-80px)]">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <CardTitle className="text-2xl font-bold text-red-600">
              Error
            </CardTitle>
            <CardDescription>
              Invalid or missing reset token. Please use the link from your
              email.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-center text-sm text-gray-600">
              If you didn&apos;t receive an email, please check your spam folder
              or request a new password reset link.
            </p>
            <Button
              onClick={() => router.push(Routes.FORGOT_PASSWORD)}
              className="w-full"
            >
              Request New Link
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="flex flex-col items-center w-full h-full">
      <div className="mx-auto px-4 py-16 flex-1 flex flex-col items-center justify-center">
        <div className="mx-auto flex justify-between max-w-[936px] gap-54">
          <div className="flex flex-col items-center justify-center max-w-[456px] px-9 py-10">
            <Heading level={4} className="text-header font-medium mb-2">
              Reset your password
            </Heading>
            <Body className="text-center mb-8">
              Choose a strong password to secure your account.
            </Body>
            <PasswordForm
              termEnable={false}
              buttonText="Reset Password"
              onSubmit={onSubmit}
              isLoading={loading}
              showSignInLink={false}
            />
          </div>
          <div className="flex justify-center">
            <Image
              src="/assets/circle-placeholder.svg"
              alt="Password Setup"
              width={360}
              height={360}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
