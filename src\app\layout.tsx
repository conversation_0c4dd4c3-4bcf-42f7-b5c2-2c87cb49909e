import type { Metada<PERSON> } from 'next';
import { Inter } from 'next/font/google';
import { Providers } from '@/components/layout/providers';
import { ConditionalLayout } from '@/components/layout/conditional-layout';
import { SidebarProvider } from '@/components/ui/sidebar';
import { cookies } from 'next/headers';
// import { ThemeWrapper } from '@/components/layout/theme-wrapper';
import NextTopLoader from 'nextjs-toploader';
import ErrorBoundary from '@/components/ErrorBoundary';
import './globals.css';

const inter = Inter({
  variable: '--font-inter',
  subsets: ['latin'],
});

export const metadata: Metadata = {
  title: 'Scoutr | Modern Web Application',
  description:
    'A modern Next.js application with authentication, beautiful UI components, and everything you need to build amazing web applications.',
};

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  // Persisting the sidebar state in the cookie.
  const cookieStore = await cookies();
  const defaultClose = cookieStore.get('sidebar_state')?.value === 'false';

  return (
    <html lang="en" suppressHydrationWarning>
      <body className={`${inter.variable} antialiased`}>
        <NextTopLoader
          showSpinner={false}
          color="#8b5cf6"
          height={2}
          easing="ease"
          speed={200}
          shadow={false}
        />
        {/* <ThemeWrapper> */}
        <ErrorBoundary>
          <SidebarProvider defaultOpen={!defaultClose}>
            <Providers>
              <ConditionalLayout>{children}</ConditionalLayout>
            </Providers>
          </SidebarProvider>
        </ErrorBoundary>
        {/* </ThemeWrapper> */}
      </body>
    </html>
  );
}
