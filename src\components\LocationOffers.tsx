import { Heading } from './ui/typography';

interface LocationOffersProps {
  data: {
    icon: React.ElementType;
    label: string;
  }[];
}

export default function LocationOffers({ data }: LocationOffersProps) {
  return (
    <>
      <Heading level={4} className="mb-6">
        What this location offers
      </Heading>
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-y-4 sm:gap-y-6 gap-x-8">
        {data.map(({ icon: Icon, label }) => (
          <div key={label} className="flex items-center space-x-3">
            <Icon className="w-5 h-5" />
            <span className="">{label}</span>
          </div>
        ))}
      </div>
    </>
  );
}
