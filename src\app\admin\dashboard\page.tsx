'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { useAuth } from '@/contexts/auth-context';
import { ArrowRightIcon, LocationIcon, TagIcon, UsersIcon } from '@/lib/icons';
import { Plus } from 'lucide-react';
import { useCallback, useEffect, useState } from 'react';
import LoadingSkeleton from './loading';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { getUserInitials } from '@/lib/utils';
import { Routes } from '@/lib/routes';
import Link from 'next/link';
import AddUserModal from '@/components/AddUserModal';
import { toast } from 'sonner';
import { User } from '@/types/user';
import { useRouter } from 'next/navigation';
import { AdminDashboardData } from '@/types/dashboard';
import { useDashboardService, useUserService } from '@/hooks/use-services';
import Image from 'next/image';

export default function AdminDashboard() {
  const { user } = useAuth();
  const router = useRouter();
  const dashboardService = useDashboardService();
  const userService = useUserService();
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [dashboardData, setDashboardData] = useState<AdminDashboardData>();
  const [isAddUserModalOpen, setIsAddUserModalOpen] = useState(false);
  const [isCreating, setIsCreating] = useState<boolean>(false);

  const fetchData = useCallback(async () => {
    try {
      setIsLoading(true);
      const response = await dashboardService.getDashboard();
      setDashboardData(response);
    } catch {
      toast.error('Failed to get dashboard data');
    } finally {
      setIsLoading(false);
    }
  }, [dashboardService]);

  const handleAddUser = async (user: Partial<User>) => {
    try {
      setIsCreating(true);
      const { name, email, role } = user;
      await userService.createUser({ name, email, role });
      setIsAddUserModalOpen(false);
      toast.success('Invite sent successfully.');
      router.push(Routes.ADMIN_USERS);
    } catch {
      toast.error('Failed to add new user');
    } finally {
      setIsCreating(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return (
    <>
      <div className="space-y-6">
        <div className="flex flex-col sm:flex-row items-end justify-between gap-2">
          <div>
            <h1 className="text-2xl font-bold mb-2">
              Good to see you, {user?.firstName ?? ''}
            </h1>
            <span className="text-base">
              Manage users, locations, and tags all in one place.
            </span>
          </div>
          <Button
            onClick={() => router.push(Routes.ADMIN_TAGS)}
            className="w-full sm:w-[200px] h-[42px]"
          >
            <TagIcon color="#fff" /> Manage tags
          </Button>
        </div>
        {isLoading ? (
          <LoadingSkeleton />
        ) : (
          <div className="flex flex-1">
            <div className="w-full">
              <div className="grid gap-8 mb-8 grid-cols-1 md:grid-cols-2">
                <Card className="hover:shadow-lg transition-shadow py-0 col-span-1">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium">Total Users</p>
                        <p className="text-2xl font-bold mt-1">
                          {dashboardData?.totalUsers.toLocaleString() ?? 0}
                        </p>
                      </div>
                      <div className="w-10 h-10 bg-[#F5F5F5] flex items-center justify-center rounded-lg">
                        <UsersIcon color="#525252" className="w-5 h-4" />
                      </div>
                    </div>
                  </CardContent>
                </Card>
                <Card className="hover:shadow-lg transition-shadow py-0">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium">Active Locations</p>
                        <p className="text-2xl font-bold mt-1">
                          {dashboardData?.activeLocations?.toLocaleString() ??
                            0}
                        </p>
                      </div>
                      <div className="w-10 h-10 bg-[#F5F5F5] flex items-center justify-center rounded-lg">
                        <LocationIcon color="#525252" className="w-3 h-4" />
                      </div>
                    </div>
                  </CardContent>
                </Card>
                <Card className="hover:shadow-lg transition-shadow py-0 col-span-1">
                  <CardContent className="p-6 flex flex-col justify-between h-full">
                    <div>
                      <div className="flex items-center justify-between mb-6">
                        <div className="flex items-center">
                          <div className="w-10 h-10 bg-[#F5F5F5] flex items-center justify-center rounded-lg">
                            <UsersIcon color="#525252" className="w-5 h-4" />
                          </div>
                          <p className="text-sm font-medium ml-3">Users</p>
                        </div>
                        <Badge variant="secondary">
                          {(() => {
                            const requestCount =
                              dashboardData?.pendingUserRequests ?? 0;
                            return `${requestCount} ${requestCount > 1 ? 'requests' : 'request'}`;
                          })()}
                        </Badge>
                      </div>
                      {!dashboardData?.recentUsers.length ? (
                        <div className="h-[248px] flex flex-col items-center justify-center gap-4">
                          <Image
                            src="/assets/circle-placeholder.svg"
                            alt="Circle Placeholder"
                            width={108}
                            height={108}
                          />
                          No users yet.
                        </div>
                      ) : (
                        dashboardData.recentUsers.map((user, index) => (
                          <Card
                            key={index}
                            className="hover:shadow-lg transition-shadow py-0 mt-4 border"
                          >
                            <CardContent className="px-3 py-[15px] rounded-lg">
                              <div className="flex items-center justify-between">
                                <div className="flex items-center">
                                  <Avatar className="h-10 w-10 mr-3">
                                    <AvatarImage
                                      src={user.avatar}
                                      alt="avatar"
                                    />
                                    <AvatarFallback className="text-xs">
                                      {getUserInitials(
                                        user.firstName + ' ' + user.lastName
                                      )}
                                    </AvatarFallback>
                                  </Avatar>
                                  <div className="">
                                    <p className="text-sm font-medium">
                                      {user.firstName + ' ' + user.lastName}
                                    </p>
                                    <p className="text-sm font-medium capitalize text-[#737373]">
                                      {user.role}
                                    </p>
                                  </div>
                                </div>
                              </div>
                            </CardContent>
                          </Card>
                        ))
                      )}
                    </div>

                    <div className="flex items-center justify-between mt-7">
                      <Link
                        href={Routes.ADMIN_USERS}
                        className="flex items-center justify-center space-x-2"
                      >
                        <p>View all users</p>
                        <ArrowRightIcon className="w-4 h-4" />
                      </Link>

                      <Button
                        onClick={() => setIsAddUserModalOpen(true)}
                        variant="default"
                        className="w-[127px] h-9"
                      >
                        <Plus /> Create new
                      </Button>
                    </div>
                  </CardContent>
                </Card>
                <Card className="hover:shadow-lg transition-shadow py-0">
                  <CardContent className="p-6 flex flex-col justify-between h-full">
                    <div>
                      <div className="flex items-center mb-6">
                        <div className="w-10 h-10 bg-[#F5F5F5] flex items-center justify-center rounded-lg">
                          <LocationIcon color="#525252" className="w-3 h-4" />
                        </div>
                        <p className="text-sm font-medium ml-3">Locations</p>
                      </div>
                      {!dashboardData?.recentLocations.length ? (
                        <div className="h-[248px] flex flex-col items-center justify-center gap-4">
                          <Image
                            src="/assets/circle-placeholder.svg"
                            alt="Circle Placeholder"
                            width={108}
                            height={108}
                          />
                          No locations yet.
                        </div>
                      ) : (
                        dashboardData.recentLocations.map((location, index) => (
                          <Card
                            key={index}
                            className="hover:shadow-lg transition-shadow py-0 mt-4 border"
                          >
                            <CardContent className="px-3 py-[15px] rounded-lg">
                              <div className="flex items-center justify-between">
                                <div className="flex items-center">
                                  <Avatar className="h-10 w-10 mr-3 rounded-lg">
                                    <AvatarImage
                                      src={location.coverImageUrl}
                                      alt="avatar"
                                    />
                                    <AvatarFallback className="text-xs rounded-lg">
                                      {getUserInitials(location.title)}
                                    </AvatarFallback>
                                  </Avatar>
                                  <div className="">
                                    <p className="text-sm font-medium">
                                      {location.title}
                                    </p>
                                    <p className="text-sm font-medium text-[#737373]">
                                      {location.address}
                                    </p>
                                  </div>
                                </div>
                              </div>
                            </CardContent>
                          </Card>
                        ))
                      )}
                    </div>
                    <div className="flex items-center justify-between mt-7">
                      <div className="flex items-center gap-2 cursor-pointer">
                        <Link
                          href={Routes.ADMIN_LOCATIONS}
                          className="flex items-center justify-center space-x-2"
                        >
                          <p>View all locations</p>
                          <ArrowRightIcon className="w-4 h-4" />
                        </Link>
                      </div>
                      <Button variant="default" className="w-[127px] h-9">
                        <Plus />
                        <Link
                          href={Routes.ADMIN_CREATE_LOCATION}
                          className="flex items-center justify-center space-x-2"
                        >
                          <p>Create new</p>
                        </Link>
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        )}
      </div>
      <AddUserModal
        open={isAddUserModalOpen}
        onOpenChange={setIsAddUserModalOpen}
        onSave={handleAddUser}
        loading={isCreating}
      />
    </>
  );
}
