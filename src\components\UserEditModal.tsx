'use client';

import * as React from 'react';
import { useState } from 'react';
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { User } from '@/types/user';
import { Role } from '@/types/enum';

interface UserEditModalProps {
  user: User | null;
  open: boolean;
  loading: boolean;
  onOpenChange: (open: boolean) => void;
  onSave: (userData: Partial<User>) => void;
}

interface ValidationErrors {
  name?: string;
  email?: string;
  role?: string;
}

export default function UserEditModal({
  user,
  open,
  onOpenChange,
  onSave,
  loading,
}: UserEditModalProps) {
  const [formData, setFormData] = React.useState<Partial<User>>({
    name: '',
    email: '',
    role: Role.VIEWER,
  });
  const [errors, setErrors] = useState<ValidationErrors>({});
  const [touched, setTouched] = useState<
    Record<keyof ValidationErrors, boolean>
  >({
    name: false,
    email: false,
    role: false,
  });

  const validateField = (
    field: keyof User,
    value: string
  ): string | undefined => {
    switch (field) {
      case 'name':
        if (!value.trim()) return 'The user name is required';
        break;
      case 'email':
        if (!value.trim()) return 'The email is required';
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(value))
          return 'Please enter a valid email address';
        break;
      case 'role':
        if (!value) return 'The role is required';
        break;
    }
    return undefined;
  };

  const validateForm = (): boolean => {
    const newErrors: ValidationErrors = {};
    let isValid = true;

    (Object.keys(formData) as Array<keyof ValidationErrors>).forEach(key => {
      const field = key;
      const error = validateField(field as keyof User, formData[field] || '');
      if (error) {
        newErrors[field] = error;
        isValid = false;
      }
    });

    setErrors(newErrors);
    return isValid;
  };

  const handleFieldChange = (field: keyof ValidationErrors, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    setTouched(prev => ({ ...prev, [field]: true }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const handleFieldBlur = (field: keyof ValidationErrors) => {
    setTouched(prev => ({ ...prev, [field]: true }));
    const error = validateField(field as keyof User, formData[field] || '');
    setErrors(prev => ({ ...prev, [field]: error }));
  };

  React.useEffect(() => {
    if (user) {
      setFormData({
        name: user.firstName + ' ' + user.lastName,
        email: user.email,
        role: user.role,
      });
      // Reset validation state when user changes
      setErrors({});
      setTouched({
        name: false,
        email: false,
        role: false,
      });
    }
  }, [user]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!user) return;

    setTouched({
      name: true,
      email: true,
      role: true,
    });

    if (!validateForm()) {
      return;
    }

    onSave({ ...user, ...formData });
  };

  const handleInputChange = (field: keyof ValidationErrors, value: string) => {
    handleFieldChange(field, value);
  };

  const resetForm = () => {
    if (user) {
      setFormData({
        name: user.firstName + ' ' + user.lastName,
        email: user.email,
        role: user.role,
      });
    }
    setErrors({});
    setTouched({
      name: false,
      email: false,
      role: false,
    });
  };

  const handleOpenChange = (open: boolean) => {
    if (!open) {
      resetForm();
    }
    onOpenChange(open);
  };

  if (!user) return null;

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-[329px]">
        <DialogHeader>
          <DialogTitle>Edit User</DialogTitle>
          <DialogDescription>
            Update the user&apos;s details or role.
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="flex items-center space-x-4">
            <div className="flex-1">
              <Label
                htmlFor="name"
                className="text-sm font-medium text-foreground"
              >
                User name
              </Label>
              <Input
                id="name"
                value={formData.name}
                onChange={e => handleInputChange('name', e.target.value)}
                onBlur={() => handleFieldBlur('name')}
                placeholder="Enter the user's name"
                className={`mt-1 ${
                  touched.name && errors.name
                    ? 'border-red-500 focus:border-red-500'
                    : ''
                }`}
              />
              {touched.name && errors.name && (
                <p className="text-sm text-red-500 mt-1">{errors.name}</p>
              )}
            </div>
          </div>

          <div className="space-y-2">
            <Label
              htmlFor="email"
              className="text-sm font-medium text-foreground"
            >
              Email
            </Label>
            <Input
              id="email"
              type="email"
              disabled
              value={formData.email}
              onChange={e => handleInputChange('email', e.target.value)}
              onBlur={() => handleFieldBlur('email')}
              placeholder="Enter the user's email"
              className={
                touched.email && errors.email
                  ? 'border-red-500 focus:border-red-500'
                  : ''
              }
            />
            {touched.email && errors.email && (
              <p className="text-sm text-red-500 mt-1">{errors.email}</p>
            )}
          </div>

          <div className="space-y-2 w-full">
            <Label
              htmlFor="role"
              className="text-sm font-medium text-foreground"
            >
              Role
            </Label>
            <Select
              value={formData.role}
              onValueChange={value => handleFieldChange('role', value)}
              onOpenChange={open => {
                if (!open) {
                  handleFieldBlur('role');
                }
              }}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select the user's role" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="admin">Admin</SelectItem>
                <SelectItem value="viewer">Viewer</SelectItem>
                <SelectItem value="scout">Scout</SelectItem>
              </SelectContent>
            </Select>
            {touched.role && errors.role && (
              <p className="text-sm text-red-500 mt-1">{errors.role}</p>
            )}
          </div>

          <DialogFooter className="mt-6">
            <Button
              type="button"
              variant="outline"
              onClick={() => handleOpenChange(false)}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={
                Object.keys(errors).some(
                  key => errors[key as keyof ValidationErrors]
                ) || loading
              }
            >
              Save changes
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
