import { dirname } from 'path';
import { fileURLToPath } from 'url';
import { FlatCompat } from '@eslint/eslintrc';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const compat = new FlatCompat({
  baseDirectory: __dirname,
});

const eslintConfig = [
  ...compat.extends('next/core-web-vitals', 'next/typescript'),
  {
    files: ['**/*.{js,jsx,ts,tsx}'],
    plugins: {
      prettier: (await import('eslint-plugin-prettier')).default,
    },
    rules: {
      'prettier/prettier': 'error',
      'arrow-body-style': 'off',
      'prefer-arrow-callback': 'off',
    },
  },
  {
    ignores: [
      // Dependencies
      'node_modules/',
      '.pnp',
      '.pnp.js',
      
      // Production builds
      '.next/',
      'out/',
      'build/',
      'dist/',
      
      // Cache directories
      '.cache/',
      '.parcel-cache/',
      
      // Generated files
      '*.min.js',
      '*.min.css',
      
      // Coverage reports
      'coverage/',
      
      // Config files
      'postcss.config.*',
      'tailwind.config.*',
      'sentry.*.config.*',
      
      // Public assets
      'public/',
      
      // TypeScript declaration files
      '*.d.ts',
    ],
  },
];

export default eslintConfig;
