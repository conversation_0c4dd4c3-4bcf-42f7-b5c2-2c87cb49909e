'use client';

import * as React from 'react';
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  SortingState,
  useReactTable,
} from '@tanstack/react-table';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { getUserInitials } from '@/lib/utils';
import UserEditModal from '@/components/UserEditModal';
import ConfirmDialog from '@/components/ConfirmDialog';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { Plus, ChevronLeft, ChevronRight } from 'lucide-react';
import { EditIcon, DeleteIcon, SearchIcon } from '@/lib/icons';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import Loading from './loading';
import { toast } from 'sonner';
import AddUserModal from '@/components/AddUserModal';
import { Card, CardContent, CardTitle } from '@/components/ui/card';
import { User } from '@/types/user';
import { useUserService } from '@/hooks/use-services';
import Image from 'next/image';
const LIMIT = 8;

export default function Users() {
  const userService = useUserService();
  const [sorting, setSorting] = useState<SortingState>([]);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isAddUserModalOpen, setIsAddUserModalOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isRejectUserDialogOpen, setIsRejectUserDialogOpen] = useState(false);
  const [isAcceptUserDialogOpen, setIsAcceptUserDialogOpen] = useState(false);
  const [userToDelete, setUserToDelete] = useState<User | null>(null);
  const [view, setView] = useState<'active' | 'requests'>('active');
  const [search, setSearch] = useState<string>('');
  const [status, setStatus] = useState<string>('all');
  const [role, setRole] = useState<string>('all');
  const [userList, setUserList] = useState<User[]>([]);
  const [requestsList, setRequestsList] = useState<User[]>([]);
  const [requestsCount, setRequestsCount] = useState<number>(0);
  const [page, setPage] = useState<number>(1);
  const [loading, setLoading] = useState<boolean>(true);
  const [totalPage, setTotalPage] = useState<number>(0);
  const [isEditingOrCreating, setIsEditingOrCreating] =
    useState<boolean>(false);

  const handleEdit = useCallback(
    (userId: string) => () => {
      const user = userList.find(u => u.id === userId);
      if (user) {
        setSelectedUser(user);
        setIsEditModalOpen(true);
      }
    },
    [userList, setSelectedUser, setIsEditModalOpen]
  );

  const handleDelete = useCallback(
    (userId: string) => () => {
      const user = userList.find(u => u.id === userId);
      if (user) {
        setUserToDelete(user);
        setIsDeleteDialogOpen(true);
      }
    },
    [userList, setUserToDelete, setIsDeleteDialogOpen]
  );

  const acceptUser = useCallback(
    (userId: string) => {
      const user = userList.find(u => u.id === userId);
      if (user) {
        setSelectedUser(user);
        setIsAcceptUserDialogOpen(true);
      }
    },
    [userList]
  );

  const rejectUser = useCallback(
    (userId: string) => {
      const user = userList.find(u => u.id === userId);
      if (user) {
        setSelectedUser(user);
        setIsRejectUserDialogOpen(true);
      }
    },
    [userList]
  );

  const handleConfirmDelete = async () => {
    if (userToDelete) {
      try {
        await userService.deleteUser(userToDelete.id);
        toast.success('User removed successfully.');
        fetchUserData(page, search, role, status); // Refresh user list after deletion
      } catch {
        toast.error('Failed to delete user');
      } finally {
        setUserToDelete(null);
      }
    }
  };

  const handleSaveUser = async (user: Partial<User>) => {
    if (user.id) {
      try {
        setIsEditingOrCreating(true);
        await userService.updateUser(user.id, {
          name: user.name,
          role: user.role,
        });
        toast.success('User updated successfully.');
        fetchUserData(page, search, role, status); // Refresh user list after deletion
        setSelectedUser(null);
        setIsEditModalOpen(false);
      } catch {
        toast.error('Failed to update user');
      } finally {
        setIsEditingOrCreating(false);
        setUserToDelete(null);
      }
    }
  };

  const handleAddUser = async (user: Partial<User>) => {
    try {
      setIsEditingOrCreating(true);
      const { name, email } = user;
      await userService.createUser({ name, email, role: user.role });
      setIsAddUserModalOpen(false);
      fetchUserData(page, search, role, status);
      toast.success('Invite sent successfully.');
    } catch {
      toast.error('Failed to add new user');
    } finally {
      setIsEditingOrCreating(false);
    }
  };

  const openAddUserChange = (open: boolean) => {
    setIsAddUserModalOpen(open);
  };

  const handleRejectUser = () => {
    console.log('Reject user', selectedUser);
    fetchUserData(page, search, role, status);
    toast.success('User invite rejected successfully.');
  };

  const handleAcceptUser = () => {
    console.log('Accept user', selectedUser);
    fetchUserData(page, search, role, status);
    toast.success('User invite accepted successfully.');
  };

  const isActiveView = useMemo(() => view === 'active', [view]);

  const columns: ColumnDef<User>[] = useMemo(() => {
    const commonColumns: ColumnDef<User>[] = [
      {
        accessorKey: 'name',
        header: ({ column }) => (
          <Button
            className="p-0"
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          >
            User
          </Button>
        ),
        cell: ({ row }) => (
          <div className="flex items-center space-x-2">
            <Avatar className="h-8 w-8">
              <AvatarImage src={row.original.avatar} alt="avatar" />
              <AvatarFallback className="text-xs">
                {getUserInitials(row.original.firstName)}
              </AvatarFallback>
            </Avatar>
            <span className="text-sm font-medium hidden sm:block">
              {row.original.firstName} {row.original.lastName}
            </span>
          </div>
        ),
      },
      {
        accessorKey: 'email',
        header: ({ column }) => (
          <Button
            className="p-0"
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          >
            Email
          </Button>
        ),
        cell: ({ row }) => <span>{row.getValue('email')}</span>,
      },
      {
        accessorKey: 'role',
        header: ({ column }) => (
          <div className="w-32 whitespace-nowrap">
            <Button
              className="p-0"
              variant="ghost"
              onClick={() =>
                column.toggleSorting(column.getIsSorted() === 'asc')
              }
            >
              Role
            </Button>
          </div>
        ),
        cell: ({ row }) => (
          <Badge variant="secondary" className="capitalize">
            {row.original.role}
          </Badge>
        ),
      },
    ];

    const statusColumn: ColumnDef<User> = {
      accessorKey: 'status',
      header: ({ column }) => (
        <div className="w-32 whitespace-nowrap">
          <Button
            className="p-0"
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          >
            Status
          </Button>
        </div>
      ),
      cell: ({ row }) => (
        <Badge variant="secondary" className="capitalize">
          {row.original.status}
        </Badge>
      ),
    };

    const actionsColumn: ColumnDef<User> = {
      id: 'actions',
      header: '',
      cell: ({ row }) => {
        const user = row.original;
        return (
          <div className="flex justify-end gap-2 opacity-0 group-hover/row:opacity-100 transition-opacity">
            {isActiveView ? (
              <>
                <EditIcon
                  className="cursor-pointer"
                  onClick={handleEdit(user.id)}
                />
                <DeleteIcon
                  className="cursor-pointer"
                  onClick={handleDelete(user.id)}
                />
              </>
            ) : (
              <>
                <Button
                  onClick={() => rejectUser(user.id)}
                  size="sm"
                  variant="outline"
                >
                  Reject
                </Button>
                <Button onClick={() => acceptUser(user.id)} size="sm">
                  Accept
                </Button>
              </>
            )}
          </div>
        );
      },
    };

    if (isActiveView) {
      return [...commonColumns, statusColumn, actionsColumn];
    } else {
      return [...commonColumns, actionsColumn];
    }
  }, [isActiveView, handleEdit, handleDelete, rejectUser, acceptUser]);

  const fetchUserData = useCallback(
    async (page = 1, search?: string, role?: string, status?: string) => {
      try {
        const response = await userService.getUsers(
          page,
          search,
          role,
          status,
          LIMIT
        );
        setUserList(response.data);
        setTotalPage(response.meta.totalPages);
      } catch {
        toast.error('Failed to get users');
      }
    },
    [userService]
  );

  const fetchRequestsData = useCallback(
    async (page = 1, search?: string, role?: string) => {
      try {
        const response = await userService.getUsersRequests(
          page,
          search,
          role,
          LIMIT
        );
        setRequestsList(response.data);
        setTotalPage(Math.ceil(response.meta.totalPages));
        if (page === 1 && !search && !role) {
          setRequestsCount(response.meta.totalItems);
        }
      } catch {
        toast.error('Failed to get requests');
      }
    },
    [userService]
  );

  const fetchData = useCallback(async () => {
    setLoading(true);
    try {
      if (isActiveView) {
        await fetchUserData(page, search, role, status);
      } else {
        await fetchRequestsData(page, search, role);
      }
    } finally {
      setLoading(false);
    }
  }, [
    fetchRequestsData,
    fetchUserData,
    isActiveView,
    page,
    role,
    search,
    status,
  ]);

  useEffect(() => {
    fetchData();
  }, [search, role, status, page, fetchUserData, fetchData]);

  useEffect(() => {
    fetchRequestsData();
  }, [fetchRequestsData]);

  const table = useReactTable({
    data: isActiveView ? userList : requestsList,
    columns,
    state: {
      sorting,
    },
    onSortingChange: setSorting,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
  });

  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold">Users</h1>
      <div className="text-base">
        Invite new users, update roles, and review requests.
      </div>
      <div className="mt-6 flex items-center gap-2">
        <div className="inline-flex rounded-lg border p-1 bg-secondary">
          <Button
            variant={isActiveView ? 'outline' : 'secondary'}
            size="sm"
            onClick={() => setView('active')}
            className={
              view === 'requests' ? 'border-0 shadow-none text-[#64748B]' : ''
            }
          >
            Active Users
          </Button>
          <Button
            variant={view === 'requests' ? 'outline' : 'secondary'}
            size="sm"
            onClick={() => setView('requests')}
            className={
              isActiveView
                ? 'border-0 shadow-none text-[#64748B] relative'
                : 'relative'
            }
          >
            Requests
            {requestsCount > 0 && (
              <Badge className="ml-2 h-5 px-1.5 text-[11px] text-black bg-[#D9D9D9] rounded-full">
                {requestsCount}
              </Badge>
            )}
          </Button>
        </div>
      </div>
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:flex-1">
          <div className="relative w-[256px]">
            <SearchIcon className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search by name or email..."
              value={search ?? ''}
              onChange={e => setSearch(e.target.value)}
              className="pl-9"
            />
          </div>
          <div className="flex flex-wrap items-center gap-2">
            <Select value={role} onValueChange={v => setRole(v)}>
              <SelectTrigger aria-label="Category">
                <SelectValue placeholder="Category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Roles</SelectItem>
                <SelectItem value="admin">Admin</SelectItem>
                <SelectItem value="scout">Scout</SelectItem>
                <SelectItem value="viewer">Viewer</SelectItem>
              </SelectContent>
            </Select>

            {isActiveView && (
              <Select value={status} onValueChange={v => setStatus(v)}>
                <SelectTrigger aria-label="Status">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="disabled">Disabled</SelectItem>
                </SelectContent>
              </Select>
            )}
          </div>
        </div>
        <Button
          onClick={() => setIsAddUserModalOpen(true)}
          variant="default"
          className="w-[154px] h-[42px]"
        >
          <Plus /> Create new user
        </Button>
      </div>
      {loading ? (
        <Loading isActive={isActiveView} />
      ) : (
        <>
          <Card className="py-0 rounded-md shadow-sm gap-0">
            <CardTitle className="h-[62px] flex items-center px-6 bg-[#FAFAFA] rounded-md">
              {isActiveView ? 'Active Users' : 'Requests'}
            </CardTitle>
            <CardContent className="px-0">
              <div className="border-t">
                <Table>
                  <TableHeader>
                    {table.getHeaderGroups().map(headerGroup => (
                      <TableRow key={headerGroup.id}>
                        {headerGroup.headers.map(header => (
                          <TableHead
                            className="px-6 bg-[#FAFAFA]"
                            key={header.id}
                          >
                            {header.isPlaceholder
                              ? null
                              : flexRender(
                                  header.column.columnDef.header,
                                  header.getContext()
                                )}
                          </TableHead>
                        ))}
                      </TableRow>
                    ))}
                  </TableHeader>
                  <TableBody>
                    {table.getRowModel().rows?.length ? (
                      table.getRowModel().rows.map(row => (
                        <TableRow key={row.id} className="group/row">
                          {row.getVisibleCells().map(cell => (
                            <TableCell className="px-6" key={cell.id}>
                              {flexRender(
                                cell.column.columnDef.cell,
                                cell.getContext()
                              )}
                            </TableCell>
                          ))}
                        </TableRow>
                      ))
                    ) : (
                      <TableRow>
                        <TableCell
                          colSpan={columns.length}
                          className="h-[518px] text-center"
                        >
                          <div className="h-full flex flex-col items-center justify-center gap-4">
                            <Image
                              src="/assets/circle-placeholder.svg"
                              alt="Circle Placeholder"
                              width={108}
                              height={108}
                            />
                            <span>No users yet.</span>
                          </div>
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </>
      )}
      <div className="mt-6 flex items-center justify-center">
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setPage(Math.max(page - 1, 1))}
            disabled={page === 1}
            className="w-9 h-9 p-0"
          >
            <ChevronLeft />
          </Button>

          <div className="flex items-center space-x-1">
            {Array.from({ length: Math.max(totalPage, 1) }, (_, i) => (
              <Button
                key={i}
                variant={page === i + 1 ? 'default' : 'outline'}
                size="sm"
                onClick={() => setPage(i + 1)}
                className="w-10 h-10 p-0"
              >
                {i + 1}
              </Button>
            ))}
          </div>

          <Button
            variant="outline"
            size="sm"
            onClick={() => setPage(Math.min(page + 1, totalPage))}
            disabled={page === totalPage}
            className="w-9 h-9 p-0"
          >
            <ChevronRight />
          </Button>
        </div>
      </div>

      <UserEditModal
        user={selectedUser}
        open={isEditModalOpen}
        onOpenChange={setIsEditModalOpen}
        onSave={handleSaveUser}
        loading={isEditingOrCreating}
      />
      <AddUserModal
        open={isAddUserModalOpen}
        onOpenChange={openAddUserChange}
        onSave={handleAddUser}
        loading={isEditingOrCreating}
      />
      <ConfirmDialog
        open={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
        title="Remove user?"
        description="This user will be removed from the platform and lose access immediately."
        confirmText="Remove"
        cancelText="Keep user"
        onConfirm={handleConfirmDelete}
      />
      <ConfirmDialog
        open={isRejectUserDialogOpen}
        onOpenChange={setIsRejectUserDialogOpen}
        title="Reject user invite?"
        description="This action will decline the request to join the platform. The user will not gain access unless a new invite is sent."
        confirmText="Reject invite"
        cancelText="Cancel"
        onConfirm={handleRejectUser}
      />
      <ConfirmDialog
        open={isAcceptUserDialogOpen}
        onOpenChange={setIsAcceptUserDialogOpen}
        title="Accept user invite?"
        description="This request will grant the user access to the platform. You can edit their role or remove access later if needed."
        confirmText="Accept invite"
        cancelText="Keep user"
        onConfirm={handleAcceptUser}
      />
    </div>
  );
}
