'use client';

import React, {
  createContext,
  useContext,
  useState,
  useCallback,
  ReactNode,
} from 'react';

export interface NavbarItem {
  id: string;
  label: string;
  icon?: ReactNode;
  onClick?: () => void;
  href?: string;
  variant?:
    | 'default'
    | 'destructive'
    | 'outline'
    | 'secondary'
    | 'ghost'
    | 'link';
  disabled?: boolean;
  badge?: ReactNode;
  align?: 'left' | 'right';
  iconPosition?: 'left' | 'right';
  className?: string;
}

interface NavbarContextType {
  additionalItems: NavbarItem[];
  setAdditionalItems: (items: NavbarItem[]) => void;
  addNavbarItem: (item: NavbarItem) => void;
  removeNavbarItem: (id: string) => void;
  clearNavbarItems: () => void;
}

const NavbarContext = createContext<NavbarContextType | undefined>(undefined);

interface NavbarProviderProps {
  children: ReactNode;
}

export function NavbarProvider({ children }: NavbarProviderProps) {
  const [additionalItems, setAdditionalItems] = useState<NavbarItem[]>([]);

  const addNavbarItem = useCallback((item: NavbarItem) => {
    setAdditionalItems(prev => {
      // Check if item already exists
      const exists = prev.some(existingItem => existingItem.id === item.id);
      if (exists) {
        return prev;
      }
      return [...prev, item];
    });
  }, []);

  const removeNavbarItem = useCallback((id: string) => {
    setAdditionalItems(prev => prev.filter(item => item.id !== id));
  }, []);

  const clearNavbarItems = useCallback(() => {
    setAdditionalItems([]);
  }, []);

  const value: NavbarContextType = {
    additionalItems,
    setAdditionalItems,
    addNavbarItem,
    removeNavbarItem,
    clearNavbarItems,
  };

  return (
    <NavbarContext.Provider value={value}>{children}</NavbarContext.Provider>
  );
}

export function useNavbar() {
  const context = useContext(NavbarContext);
  if (context === undefined) {
    throw new Error('useNavbar must be used within a NavbarProvider');
  }
  return context;
}
