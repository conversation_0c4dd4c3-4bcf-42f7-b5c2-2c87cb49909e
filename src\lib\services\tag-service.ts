import { Tag } from '@/types/tag';
import { httpClient } from '../http-client';
const buildQueryParams = (params: {
  page?: number;
  search?: string;
  type?: string;
  limit?: number;
}) => {
  const { page, search, type, limit } = params;
  const queryParams: string[] = [];
  if (page) {
    queryParams.push(`page=${page}`);
  }
  if (limit) {
    queryParams.push(`limit=${limit}`);
  }
  if (search && search !== '') {
    queryParams.push(`search=${search}`);
  }
  if (type && type !== 'all') {
    queryParams.push(`type=${type}`);
  }
  return queryParams.join('&');
};
export class TagService {
  async getTags(params?: {
    page?: number;
    search?: string;
    type?: string;
    limit?: number;
  }): Promise<{ data: Tag[]; meta: { totalPages: number } }> {
    let queryParams = '';
    if (params) {
      const { page, search, type, limit } = params;
      queryParams = buildQueryParams({
        page,
        search,
        type,
        limit,
      });
    }

    return httpClient.get<{ data: Tag[]; meta: { totalPages: number } }>(
      `/tags${queryParams ? `?${queryParams}` : ''}`
    );
  }

  /**
   * Create new tag (admin only)
   */
  async createTag(tagData: Partial<Tag>): Promise<Tag> {
    return httpClient.post<Tag>('/tags', tagData);
  }

  /**
   * Update tag by ID (admin only)
   */
  async updateTag(id: string, tagData: Partial<Tag>): Promise<Tag> {
    return httpClient.patch<Tag>(`/tags/${id}`, tagData);
  }
  /**
   * Delete tag by ID (admin only)
   */
  async deleteTag(id: string): Promise<void> {
    return httpClient.delete<void>(`/tags/${id}`);
  }

  async bulkDeleteTags(tagIds: string[]): Promise<void> {
    return httpClient.delete<void>(`/tags/bulk`, { tagIds });
  }
}

// Export singleton instance
export const tagService = new TagService();
