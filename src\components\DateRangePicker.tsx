import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@radix-ui/react-popover';
import React from 'react';
import { DateRange } from 'react-day-picker';
import { Button } from './ui/button';
import { CalendarIcon } from '@/lib/icons';
import { Calendar } from './ui/calendar';
import { format } from 'date-fns';
import { cn, formatDateRange } from '@/lib/utils';

interface DateRangePickerProps {
  className?: string;
  onDateRangeChange: (dateRange: DateRange | undefined) => void;
  value?: DateRange;
  placeholder?: string;
}

export function DateRangePicker({
  className,
  onDateRangeChange,
  value,
  placeholder,
}: DateRangePickerProps) {
  const [date, setDate] = React.useState<DateRange | undefined>(value);

  const handleSetDate = (dateRange: DateRange | undefined) => {
    setDate(dateRange);
    onDateRangeChange(dateRange);
  };
  const handleClear = () => setDate(undefined);

  return (
    <div className={cn('grid gap-2')}>
      <Popover>
        <PopoverTrigger asChild>
          <Button
            id="date"
            variant={'outline'}
            className={cn(
              'justify-between text-left font-normal border-input h-10',
              className
            )}
          >
            {date?.from ? (
              date.to ? (
                <>{formatDateRange(date.from, date.to)}</>
              ) : (
                format(date.from, 'LLL dd, y')
              )
            ) : (
              <span className={className}>{placeholder || 'Date range'}</span>
            )}
            {date ? (
              <div
                className="h-4 w-4 cursor-pointer text-muted-foreground flex items-center justify-center"
                onClick={e => {
                  e.stopPropagation();
                  handleClear();
                }}
              >
                x
              </div>
            ) : (
              <CalendarIcon className="ml-2" />
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent
          className="w-auto mt-2 p-2 border bg-white rounded-lg z-50"
          align="start"
        >
          <Calendar
            mode="range"
            defaultMonth={date?.from}
            selected={date}
            onSelect={handleSetDate}
            numberOfMonths={2}
          />
          {date && (
            <Button
              variant="ghost"
              size="sm"
              className="mt-1 w-full border"
              onClick={handleClear}
            >
              Clear
            </Button>
          )}
        </PopoverContent>
      </Popover>
    </div>
  );
}
