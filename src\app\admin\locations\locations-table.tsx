import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { DeleteIcon, EditIcon } from '@/lib/icons';
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  SortingState,
  useReactTable,
} from '@tanstack/react-table';
import { useMemo, useState } from 'react';
import LocationsTableLoading from './locations-table-loading';
import ConfirmDialog from '@/components/ConfirmDialog';
import { toast } from 'sonner';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { Location } from '@/types';
import { useLocationService } from '@/hooks/use-services';
interface LocationTableProps {
  data: Location[];
  loading: boolean;
  onActionSuccess: () => void;
}

export default function LocationsTable({
  data,
  loading,
  onActionSuccess,
}: LocationTableProps) {
  const locationService = useLocationService();
  const router = useRouter();
  const [sorting, setSorting] = useState<SortingState>([]);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState<boolean>(false);
  const [locationSelected, setLocationSelected] = useState<Location>();
  const columns: ColumnDef<Location>[] = useMemo(() => {
    return [
      {
        accessorKey: 'location',
        header: ({ column }) => (
          <Button
            className="p-0"
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          >
            Location
          </Button>
        ),
        cell: ({ row }) => {
          const coverImageUrl = row.original.coverImageUrl;
          return (
            <div className="flex items-center space-x-4">
              <div className="relative w-15 h-12">
                {!coverImageUrl ? (
                  <div className="w-full h-full flex items-center justify-center bg-border rounded-lg text-white">
                    Photo
                  </div>
                ) : (
                  <Image
                    src={coverImageUrl}
                    alt="Cover Image"
                    fill
                    className="rounded-lg object-cover"
                  />
                )}
              </div>
              <div className="text-sm font-medium w-[128px] break-normal whitespace-normal">
                {row.original.title}
              </div>
            </div>
          );
        },
      },
      {
        accessorKey: 'address',
        header: ({ column }) => (
          <Button
            className="p-0"
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          >
            Address
          </Button>
        ),
        cell: ({ row }) => (
          <p className="whitespace-break-spaces">{row.getValue('address')}</p>
        ),
      },
      {
        accessorKey: 'tags',
        header: ({ column }) => (
          <div className="w-32 whitespace-nowrap">
            <Button
              className="p-0"
              variant="ghost"
              onClick={() =>
                column.toggleSorting(column.getIsSorted() === 'asc')
              }
            >
              Tags
            </Button>
          </div>
        ),
        cell: ({ row }) => (
          <div className="flex items-center gap-2 flex-wrap">
            {row.original.tags.map((tag, index) => (
              <Badge
                key={index}
                variant="secondary"
                className={`${tag.color ? `${tag.color} text-white` : ''}`}
              >
                {tag.name}
              </Badge>
            ))}
          </div>
        ),
      },
      {
        id: 'actions',
        header: '',
        cell: ({ row }) => {
          return (
            <div className="flex justify-end gap-2 opacity-0 group-hover/row:opacity-100 transition-opacity">
              <EditIcon
                className="cursor-pointer"
                onClick={() => {
                  router.push(
                    `/admin/locations/edit-location/${row.original.id}`
                  );
                }}
              />
              <DeleteIcon
                className="cursor-pointer"
                onClick={() => {
                  setLocationSelected(row.original);
                  setIsDeleteDialogOpen(true);
                }}
              />
            </div>
          );
        },
      },
    ];
  }, [router]);
  const handleConfirmDelete = async () => {
    if (locationSelected) {
      try {
        await locationService.deleteLocation(locationSelected.id);
        toast.success('Location removed successfully.');
        onActionSuccess();
      } catch {
        toast.error('Failed to delete location');
      } finally {
        setIsDeleteDialogOpen(false);
      }
    }
  };

  const table = useReactTable({
    data,
    columns,
    state: {
      sorting,
    },
    onSortingChange: setSorting,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
  });

  return (
    <>
      {loading ? (
        <LocationsTableLoading />
      ) : (
        <Card className="py-0 rounded-md shadow-sm gap-0">
          <CardContent className="px-0">
            <Table>
              <TableHeader>
                {table.getHeaderGroups().map(headerGroup => (
                  <TableRow key={headerGroup.id}>
                    {headerGroup.headers.map(header => (
                      <TableHead
                        className="px-6 bg-[#FAFAFA] rounded-md"
                        key={header.id}
                      >
                        {header.isPlaceholder
                          ? null
                          : flexRender(
                              header.column.columnDef.header,
                              header.getContext()
                            )}
                      </TableHead>
                    ))}
                  </TableRow>
                ))}
              </TableHeader>
              <TableBody>
                {table.getRowModel().rows?.length ? (
                  table.getRowModel().rows.map(row => (
                    <TableRow key={row.id} className="group/row">
                      {row.getVisibleCells().map(cell => (
                        <TableCell className="px-6" key={cell.id}>
                          {flexRender(
                            cell.column.columnDef.cell,
                            cell.getContext()
                          )}
                        </TableCell>
                      ))}
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell
                      colSpan={columns.length}
                      className="h-[551px] text-center"
                    >
                      <div className="h-full flex flex-col items-center justify-center gap-4">
                        <Image
                          src="/assets/circle-placeholder.svg"
                          alt="Circle Placeholder"
                          width={108}
                          height={108}
                        />
                        <span>No locations yet.</span>
                      </div>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      )}
      <ConfirmDialog
        open={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
        title="Remove location?"
        description="This location will be permanently removed from the platform. It will no longer be available in reference lists."
        confirmText="Remove"
        cancelText="Keep location"
        onConfirm={handleConfirmDelete}
      />
    </>
  );
}
