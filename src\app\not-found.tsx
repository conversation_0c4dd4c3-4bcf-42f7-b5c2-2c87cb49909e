'use client';

import Link from 'next/link';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { HomeIcon, ArrowLeftIcon } from 'lucide-react';
import { Routes } from '@/lib/routes';
import { useAuth } from '@/contexts/auth-context';
import { useMemo } from 'react';
import { Role } from '@/types/enum';

export default function NotFound() {
  const { user } = useAuth();

  const dashboardLink = useMemo(() => {
    if (!user) {
      return '';
    }
    return user?.role === Role.ADMIN
      ? Routes.ADMIN_DASHBOARD
      : Routes.DASHBOARD;
  }, [user]);

  return (
    <div className="flex items-center justify-center min-h-[calc(100vh-64px)] w-full">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-muted">
            <span className="text-3xl font-bold text-muted-foreground">
              404
            </span>
          </div>
          <CardTitle className="text-2xl font-bold">Page Not Found</CardTitle>
          <CardDescription className="text-muted-foreground">
            Sorry, we couldn&apos;t find the page you&apos;re looking for. It
            might have been moved, deleted, or you entered the wrong URL.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex flex-col space-y-2">
            <Button asChild className="w-full">
              <Link
                href={Routes.HOME}
                className="flex items-center justify-center space-x-2"
              >
                <HomeIcon className="h-4 w-4" />
                <span>Go to Home</span>
              </Link>
            </Button>
            {!!dashboardLink && (
              <Button variant="outline" asChild className="w-full">
                <Link
                  href={dashboardLink}
                  className="flex items-center justify-center space-x-2"
                >
                  <ArrowLeftIcon className="h-4 w-4" />
                  <span>Go to Dashboard</span>
                </Link>
              </Button>
            )}
          </div>
          <div className="text-center text-sm text-muted-foreground">
            <p>If you believe this is an error, please contact support.</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
