'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { toast } from 'sonner';
import { Lock } from 'lucide-react';

interface ResetPasswordDialogProps {
  trigger?: React.ReactNode;
  defaultEmail?: string;
  onSuccess?: () => void;
}

export function ResetPasswordDialog({
  trigger,
  defaultEmail = '',
  onSuccess,
}: ResetPasswordDialogProps) {
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [email, setEmail] = useState(defaultEmail);
  const [error, setError] = useState<string | undefined>();
  const [touched, setTouched] = useState(false);

  // Email validation function
  const validateEmail = (email: string): string | undefined => {
    if (!email) return 'Email is required';

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return 'Please enter a valid email address';
    }

    return undefined;
  };

  // Handle email change
  const handleEmailChange = (value: string) => {
    setEmail(value);
    setTouched(true);
    setError(undefined);
  };

  // Validate email on blur
  const handleEmailBlur = () => {
    setTouched(true);
    const emailError = validateEmail(email);
    setError(emailError);
  };

  // Debounced validation
  useEffect(() => {
    if (touched && email) {
      const timer = setTimeout(() => {
        const emailError = validateEmail(email);
        setError(emailError);
      }, 500);

      return () => clearTimeout(timer);
    }
  }, [email, touched]);

  const handleSubmit = async () => {
    setTouched(true);
    const emailError = validateEmail(email);

    if (emailError) {
      setError(emailError);
      return;
    }

    setLoading(true);
    try {
      const response = await fetch('/nextapi/auth/forgot-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to send reset email');
      }

      toast.success('Password reset email sent successfully!');
      setOpen(false);
      setEmail('');
      setError(undefined);
      setTouched(false);

      onSuccess?.();
    } catch (error) {
      console.error('Error sending reset email:', error);
      toast.error(
        error instanceof Error ? error.message : 'Failed to send reset email'
      );
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    setOpen(false);
    setEmail(defaultEmail);
    setError(undefined);
    setTouched(false);
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {trigger || (
          <Button variant="outline" className="flex items-center space-x-2">
            <Lock className="h-4 w-4" />
            <span>Reset Password</span>
          </Button>
        )}
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Reset Password</DialogTitle>
          <DialogDescription>
            Enter your email address to receive a password reset link.
          </DialogDescription>
        </DialogHeader>
        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="reset-email">Email Address</Label>
            <Input
              id="reset-email"
              type="email"
              value={email}
              onChange={e => handleEmailChange(e.target.value)}
              onBlur={handleEmailBlur}
              placeholder="Enter your email address"
              className={touched && error ? 'border-red-500' : ''}
            />
            {touched && error && (
              <p className="text-sm text-red-500">{error}</p>
            )}
          </div>
          <div className="flex space-x-3 pt-4">
            <Button
              onClick={handleSubmit}
              disabled={loading}
              className="flex-1"
            >
              {loading ? 'Sending...' : 'Send Reset Link'}
            </Button>
            <Button variant="outline" onClick={handleCancel} disabled={loading}>
              Cancel
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
