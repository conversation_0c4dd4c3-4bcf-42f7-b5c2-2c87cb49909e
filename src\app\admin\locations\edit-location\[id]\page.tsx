'use client';
import { But<PERSON> } from '@/components/ui/button';
import { Routes } from '@/lib/routes';
import { ArrowLeft } from 'lucide-react';
import { use, useCallback, useEffect, useState } from 'react';
import { ArrowRightIcon } from '@/lib/icons';
import { isEmpty } from 'lodash';
import { toast } from 'sonner';
import { useRouter } from 'next/navigation';
import PhotosForm from '@/components/photo/PhotosForm';
import { MINIMUM_IMAGES } from '@/types/constant';
import BasicDetailsForm from '@/components/BasicDetailsForm';
import TagsForm from '@/components/TagsForm';
import { useLocationService, useTagService } from '@/hooks/use-services';
import { Tag, TagOption } from '@/types/tag';
import { Location, LocationRequest, Photo } from '@/types/location';
import { UpdateLocationData } from '@/lib/services/location-service';
import Loading from './loading';
import { TagType } from '@/types/enum';

const locationDefault: LocationRequest = {
  title: '',
  address: '',
  description: '',
  categories: [],
  subCategory: '',
  style: [],
  size: '',
  images: [],
};
export default function EditLocationPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = use(params);
  const router = useRouter();
  const tagService = useTagService();
  const locationService = useLocationService();
  const [formData, setFormData] = useState<LocationRequest>(locationDefault);
  const [categoryOption, setCategoryOption] = useState<TagOption[]>([]);
  const [subCategoryOption, setSubCategoryOption] = useState<TagOption[]>([]);
  const [styleOption, setStyleOption] = useState<TagOption[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  const buildEditLocationPayload = (
    data: LocationRequest
  ): UpdateLocationData => {
    const {
      title,
      address,
      latitude,
      longitude,
      description,
      categories,
      subCategory,
      style,
      size,
      images,
    } = data;

    return {
      title,
      address,
      latitude,
      longitude,
      description,
      tagIds: [...categories, subCategory, ...style],
      size,
      images: images.map(image => ({
        url: image.url,
        order: image.order,
        key: image.key || '',
        tagIds: image.tags.map(tag => tag.id),
      })),
    };
  };

  const handleEditLocation = async () => {
    try {
      if (formData.images.some((image: Photo) => isEmpty(image.tags))) {
        toast.warning('Not all images have a tag.');
      } else {
        const payload = buildEditLocationPayload(formData);
        await locationService.updateLocation(id, payload);
        toast.success('Location updated successfully.');
        router.push(Routes.ADMIN_LOCATIONS);
      }
    } catch {
      toast.error('Failed to update location');
    }
  };

  const validateForm = (data: Partial<LocationRequest>) => {
    if (!data) {
      return true;
    }
    const basicAndTagsFields: Array<
      | 'title'
      | 'address'
      | 'description'
      | 'categories'
      | 'subCategory'
      | 'style'
      | 'size'
    > = [
      'title',
      'address',
      'description',
      'categories',
      'subCategory',
      'style',
      'size',
    ];
    const fieldError = basicAndTagsFields.some(field => isEmpty(data[field]));

    return (
      !data.images ||
      (Array.isArray(data.images) && data.images.length < MINIMUM_IMAGES) ||
      fieldError
    );
  };

  const handlePhotosChange = (newPhotos: (Photo | undefined)[]) => {
    const filteredPhotos: Photo[] = newPhotos.filter(
      (photo): photo is Photo => photo !== undefined
    );
    setFormData((prev: LocationRequest) => ({
      ...prev,
      images: filteredPhotos,
    }));
  };

  const handleChange = (data: Partial<LocationRequest>) => {
    if (data) {
      setFormData((prev: LocationRequest) => ({
        ...prev,
        ...data,
      }));
    }
  };

  const fetchLocationData = useCallback(async () => {
    try {
      setIsLoading(true);
      const locationData = (await locationService.getLocationById(
        id
      )) as Location;
      const photosWithFiles = locationData.images;
      const categories = locationData.tags
        .filter(tag => tag.type === TagType.CATEGORY)
        .map(p => p.id);
      const subCategory = locationData.tags.find(
        tag => tag.type === TagType.SUB_CATEGORY
      );
      const style = locationData.tags
        .filter(tag => tag.type === TagType.STYLE)
        .map(p => p.id);
      setFormData((prev: LocationRequest) => ({
        ...prev,
        ...locationData,
        size: 'small',
        categories,
        subCategory: subCategory ? subCategory.id : '',
        style,
        latitude: Number(locationData.latitude),
        longitude: Number(locationData.longitude),
        images: photosWithFiles as unknown as Photo[],
      }));
    } finally {
      setIsLoading(false);
    }
  }, [id, locationService]);

  const fetchCategories = useCallback(async () => {
    try {
      const response = await tagService.getTags({ type: TagType.CATEGORY });
      setCategoryOption(
        response.data.map(data => ({
          value: data.id,
          label: data.name,
          color: data.color,
        }))
      );
    } catch {
      toast.error('Failed to get tag categories');
    }
  }, [tagService]);

  const fetchSubCategories = useCallback(async () => {
    try {
      const response = await tagService.getTags({ type: TagType.SUB_CATEGORY });
      setSubCategoryOption(
        response.data.map(data => ({
          value: data.id,
          label: data.name,
          color: data.color,
        }))
      );
    } catch {
      toast.error('Failed to get tag sub-categories');
    }
  }, [tagService]);

  const fetchStyles = useCallback(async () => {
    try {
      const response = await tagService.getTags({ type: TagType.STYLE });
      setStyleOption(
        response.data.map(data => ({
          value: data.id,
          label: data.name,
          color: data.color,
        }))
      );
    } catch {
      toast.error('Failed to get tag styles');
    }
  }, [tagService]);

  const handleCreateNewTag = async (tag: Partial<Tag>) => {
    try {
      const { name, type, color } = tag;
      await tagService.createTag({ name, type, color });
      toast.success('Tag created successfully.');
      fetchTags();
    } catch {
      toast.error('Failed to add new tag');
    }
  };

  useEffect(() => {
    if (!id) return;
    fetchLocationData();
  }, [fetchLocationData, id]);

  const fetchTags = useCallback(() => {
    fetchCategories();
    fetchSubCategories();
    fetchStyles();
  }, [fetchCategories, fetchStyles, fetchSubCategories]);

  useEffect(() => {
    fetchTags();
  }, [fetchTags]);

  return (
    <>
      {isLoading && <Loading />}
      {!isLoading && (
        <div className="flex flex-col items-center pb-20">
          <div className="w-full sm:w-[unset] sm:max-w-4xl space-y-8">
            <div
              onClick={e => {
                e.preventDefault();
                router.push(Routes.ADMIN_LOCATIONS);
              }}
              className="cursor-pointer inline-flex items-center gap-2 text-sm  hover:text-gray-900 transition-colors"
            >
              <ArrowLeft className="h-4 w-4" />
              Back to Locations
            </div>
            <div className="w-full sm:w-[672px] flex flex-col gap-8">
              <div className="space-y-6">
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">
                    Edit location
                  </h1>
                  <p className=" mt-2">
                    Update the details, tags, or photos for this location.
                  </p>
                </div>
                {/* Basic details */}
                <h2 className="text-xl font-bold text-gray-900">
                  Basic details
                </h2>
                <BasicDetailsForm data={formData} onChange={handleChange} />
                {/* Tags */}
                <h2 className="text-xl font-bold text-gray-900">Tags</h2>
                <p className=" mt-2">
                  Select the category, style, and size tags that capture the
                  look and feel. These tags make it easier for scouts to
                  discover the right fit.
                </p>
                <TagsForm
                  data={formData}
                  categoryOption={categoryOption}
                  subCategoryOption={subCategoryOption}
                  styleOption={styleOption}
                  onChange={handleChange}
                  onCreateTag={handleCreateNewTag}
                />
                <PhotosForm
                  data={formData.images}
                  onChange={handlePhotosChange}
                />
              </div>
            </div>
            <div className="bg-white z-50 fixed left-0 py-5 px-4 sm:px-0 right-0 bottom-0 border-t flex justify-center items-center">
              <div className="w-[672px] flex items-center justify-between">
                <Button
                  variant="outline"
                  onClick={() => router.push(Routes.ADMIN_LOCATIONS)}
                  className="px-6"
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleEditLocation}
                  disabled={validateForm(formData)}
                  className="px-6 bg-black hover:bg-gray-800 text-white"
                >
                  Save changes
                  <ArrowRightIcon color="white" className="h-4 w-4 ml-2" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
