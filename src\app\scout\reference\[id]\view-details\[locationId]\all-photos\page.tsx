'use client';
import { Photo } from '@/types/location';
import AllPhotoGrid from '@/components/AllPhotoGrid';
import { ArrowLeft } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { use, useCallback, useEffect, useState } from 'react';
import { toast } from 'sonner';
import { Heading } from '@/components/ui/typography';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import Loading from './loading';
import DetailScreen from '@/components/DetailScreen';
const mockData: Photo[] = [
  {
    url: 'https://picsum.photos/350/200?random=1',
    order: 1,
    tags: [{ id: 'tag-1', name: 'Modern', type: 'style' }],
  },
  {
    url: 'https://picsum.photos/350/200?random=5',
    order: 2,
    tags: [
      { id: 'tag-1', name: 'Modern', type: 'style' },
      { id: 'tag-2', name: 'Large', type: 'style' },
    ],
  },
  {
    url: 'https://picsum.photos/350/200?random=6',
    order: 3,
    tags: [{ id: 'tag-2', name: 'Large', type: 'style' }],
  },
  {
    url: 'https://picsum.photos/350/200?random=7',
    order: 4,
    tags: [{ id: 'tag-2', name: 'Large', type: 'style' }],
  },
  {
    url: 'https://picsum.photos/350/200?random=8',
    order: 5,
    tags: [{ id: 'tag-2', name: 'Large', type: 'style' }],
  },
  {
    url: 'https://picsum.photos/350/200?random=9',
    order: 6,
    tags: [{ id: 'tag-1', name: 'Modern', type: 'style' }],
  },
];
export default function AllPhotos({
  params,
}: {
  params: Promise<{ id: string; locationId: string }>;
}) {
  const router = useRouter();
  const { id, locationId } = use(params);
  const [images, setImages] = useState<Photo[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [isDetailScreenOpen, setIsDetailScreenOpen] = useState<boolean>(false);
  const [startIndex, setStartIndex] = useState<number>(0);

  const fetchImages = useCallback(() => {
    try {
      setLoading(true);
      setImages(mockData);
    } catch {
      toast.error('Failed to get photos');
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    if (!locationId) return;
    fetchImages();
  }, [fetchImages, locationId]);

  return (
    <>
      {loading && <Loading />}
      {!loading && (
        <div className="w-full flex justify-center">
          <div className="space-y-11 w-full xl:w-[1128px] p-11 xl:px-0">
            <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-4">
              <div
                onClick={() => {
                  router.push(
                    `/scout/reference/${id}/view-details/${locationId}`
                  );
                }}
                className="cursor-pointer inline-flex items-center gap-3 text-sm text-gray-600 hover:text-gray-900 transition-colors"
              >
                <ArrowLeft className="h-5 w-5" />
                <div className="flex flex-col justify-start">
                  <Heading level={3}>All Photos</Heading>
                  <p>Downtown Creative Loft</p>
                </div>
              </div>
              <Button className="w-full sm:w-[155px] h-12">
                <Plus />
                Add to List
              </Button>
            </div>
            <AllPhotoGrid
              images={images}
              onClickImage={index => {
                setIsDetailScreenOpen(true);
                setStartIndex(index);
              }}
            />
          </div>
        </div>
      )}
      <DetailScreen
        open={isDetailScreenOpen}
        images={images.map(image => image.url) ?? []}
        onOpenChange={setIsDetailScreenOpen}
        startIndex={startIndex}
      />
    </>
  );
}
