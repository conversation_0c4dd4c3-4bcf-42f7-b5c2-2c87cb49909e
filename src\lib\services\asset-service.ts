import { httpClient } from '../http-client';

export interface Asset {
  id: string;
  name: string;
  description?: string;
  category: string;
  subcategory?: string;
  locationId: string;
  locationName: string;
  status: 'available' | 'in_use' | 'maintenance' | 'retired';
  condition: 'excellent' | 'good' | 'fair' | 'poor';
  purchaseDate?: string;
  purchasePrice?: number;
  serialNumber?: string;
  model?: string;
  manufacturer?: string;
  warrantyExpiry?: string;
  images: string[];
  tags: string[];
  createdAt: string;
  updatedAt: string;
}

export interface CreateAssetData {
  name: string;
  description?: string;
  category: string;
  subcategory?: string;
  locationId: string;
  status?: 'available' | 'in_use' | 'maintenance' | 'retired';
  condition?: 'excellent' | 'good' | 'fair' | 'poor';
  purchaseDate?: string;
  purchasePrice?: number;
  serialNumber?: string;
  model?: string;
  manufacturer?: string;
  warrantyExpiry?: string;
  tags?: string[];
}

export interface UpdateAssetData extends Partial<CreateAssetData> {
  status?: 'available' | 'in_use' | 'maintenance' | 'retired';
  condition?: 'excellent' | 'good' | 'fair' | 'poor';
}

export interface AssetCategory {
  id: string;
  name: string;
  description?: string;
  subcategories: string[];
}

/**
 * Asset Service - Handles all asset-related API operations
 */
export class AssetService {
  /**
   * Get all assets
   */
  async getAssets(): Promise<Asset[]> {
    return httpClient.get<Asset[]>('/assets');
  }

  /**
   * Get asset by ID
   */
  async getAssetById(id: string): Promise<Asset> {
    return httpClient.get<Asset>(`/assets/${id}`);
  }

  /**
   * Create new asset
   */
  async createAsset(assetData: CreateAssetData): Promise<Asset> {
    return httpClient.post<Asset>('/assets', assetData);
  }

  /**
   * Update asset by ID
   */
  async updateAsset(id: string, assetData: UpdateAssetData): Promise<Asset> {
    return httpClient.put<Asset>(`/assets/${id}`, assetData);
  }

  /**
   * Delete asset by ID
   */
  async deleteAsset(id: string): Promise<void> {
    return httpClient.delete<void>(`/assets/${id}`);
  }

  /**
   * Get assets by location
   */
  async getAssetsByLocation(locationId: string): Promise<Asset[]> {
    return httpClient.get<Asset[]>(`/assets?locationId=${locationId}`);
  }

  /**
   * Get assets by category
   */
  async getAssetsByCategory(category: string): Promise<Asset[]> {
    return httpClient.get<Asset[]>(
      `/assets?category=${encodeURIComponent(category)}`
    );
  }

  /**
   * Get assets by status
   */
  async getAssetsByStatus(
    status: 'available' | 'in_use' | 'maintenance' | 'retired'
  ): Promise<Asset[]> {
    return httpClient.get<Asset[]>(`/assets?status=${status}`);
  }

  /**
   * Search assets
   */
  async searchAssets(query: string): Promise<Asset[]> {
    return httpClient.get<Asset[]>(
      `/assets/search?q=${encodeURIComponent(query)}`
    );
  }

  /**
   * Get asset categories
   */
  async getAssetCategories(): Promise<AssetCategory[]> {
    return httpClient.get<AssetCategory[]>('/assets/categories');
  }

  /**
   * Upload asset images
   */
  async uploadAssetImages(
    assetId: string,
    files: File[]
  ): Promise<{ urls: string[] }> {
    return httpClient.uploadFile<{ urls: string[] }>(
      '/assets/images',
      files[0],
      {
        assetId,
        imageCount: files.length.toString(),
      }
    );
  }

  /**
   * Update asset status
   */
  async updateAssetStatus(
    id: string,
    status: 'available' | 'in_use' | 'maintenance' | 'retired'
  ): Promise<Asset> {
    return httpClient.patch<Asset>(`/assets/${id}/status`, { status });
  }

  /**
   * Update asset condition
   */
  async updateAssetCondition(
    id: string,
    condition: 'excellent' | 'good' | 'fair' | 'poor'
  ): Promise<Asset> {
    return httpClient.patch<Asset>(`/assets/${id}/condition`, { condition });
  }
}

// Export singleton instance
export const assetService = new AssetService();
